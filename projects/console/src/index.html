<!doctype html>
<html lang="en" data-theme="light">
  <head>
    <meta charset="utf-8" />
    <title>Console</title>
    <base href="/" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <link rel="icon" type="image/x-icon" href="favicon.ico" />
    <!-- Mulish font import -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Mulish:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <!-- Since we're having issues with ReactFlow, let's remove it and focus on our custom implementation -->
  </head>
  <body>
    <app-root></app-root>
  </body>
</html>
