import { Component, OnInit } from '@angular/core';
import { Router, RouterOutlet } from '@angular/router';
import { NavHeaderComponent } from './shared/components/nav-header/nav-header.component';
import { BreadcrumbComponent } from './shared/components/breadcrumb/breadcrumb.component';
import { LoaderComponent } from './shared/components/loader/loader.component';
import { ThemeService } from './shared/services/theme/theme.service';
import { AuthTokenService } from '@shared/auth/services/auth-token.service';
import { AuthService } from '@shared/auth/services/auth.service';
import { AuthConfig } from '@shared/auth/interfaces/auth-config.interface';
import { CommonModule } from '@angular/common';
import { environment } from '../environments/environment';
import { TokenStorageService } from '@shared/auth/services/token-storage.service';

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [RouterOutlet, NavHeaderComponent, BreadcrumbComponent, LoaderComponent,CommonModule],
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss'
})
export class AppComponent implements OnInit {
  title = 'console';
  showHeaderAndNav: boolean = true;
  
  constructor(
    private themeService: ThemeService,
    private router: Router,
    private authTokenService: AuthTokenService,
    private authService: AuthService,
    private tokenStorage: TokenStorageService
  ) {}
  
  ngOnInit(): void {
    const savedTheme = this.themeService.getCurrentTheme();
    this.themeService.setTheme(savedTheme);

    // Configure auth service with app-specific settings
    const authConfig: AuthConfig = {
      apiAuthUrl: environment.consoleApiAuthUrl,
      redirectUrl: environment.consoleRedirectUrl,
      postLoginRedirectUrl: '/dashboard',
      appName: 'console'
    };
    this.authService.setAuthConfig(authConfig);

    this.authTokenService.handleAuthCodeAndToken();
    this.authTokenService.startTokenCheck();
    
    this.router.events.subscribe((event) => {
      if (this.router.url === '/login' || this.router.url === '/org-config') {
        this.showHeaderAndNav = false;
      } else {
        this.showHeaderAndNav = true;
      }
    });
  }

   ngOnDestroy() {
    this.authTokenService.stopTokenCheck();
  }
}
