import { Routes } from '@angular/router';
import { DashboardComponent } from './pages/dashboard/dashboard.component';
import { AgentsComponent } from './pages/agents/agents.component';
import { WorkflowsComponent } from './pages/workflows/workflows.component';
import { CreateAgentComponent } from './pages/create-agent/create-agent.component';
import { IndividualAgentComponent } from './pages/individual-agent/individual-agent.component';
import { CollaborativeAgentComponent } from './pages/collaborative-agent/collaborative-agent.component';
import { PromptsComponent } from './pages/libraries/prompts/prompts.component';
import { ModelsComponent } from './pages/libraries/models/models.component';
import { KnowledgeBaseComponent } from './pages/libraries/knowledge-base/knowledge-base.component';
import { ToolsComponent } from './pages/libraries/tools/tools.component';
import { GuardrailsComponent } from './pages/libraries/guardrails/guardrails.component';
import { CreatePromptsComponent } from './pages/libraries/prompts/create-prompts/create-prompts.component';
import { CreateGuardrailsComponent } from './pages/libraries/guardrails/create-guardrails/create-guardrails.component';
import { CreateToolsComponent } from './pages/libraries/tools/create-tools/create-tools.component';
import { CreateModelsComponent } from './pages/libraries/models/create-models/create-models.component';
import { CreateKnowledgeBaseComponent } from './pages/libraries/knowledge-base/create-knowledge-base/create-knowledge-base.component';
import { WorkflowEditorComponent } from './pages/workflows/workflow-editor/workflow-editor.component';
import { WorkflowExecutionComponent } from './pages/workflows/workflow-execution/workflow-execution.component';
import { ChatDemoComponent } from './pages/chat-demo/chat-demo.component';
import { AdminManagementComponent } from './pages/admin-management/admin-management.component';
import { LoginComponent } from '@shared/auth/components/login/login.component';
import { AuthGuard } from '@shared/auth/guards/auth.guard';
import { CallbackComponent } from '@shared/auth/components/callback/callback.component';
import { OrgConfigComponent } from './pages/org-config/org-config.component';
import { ApprovalComponent } from './pages/approval/approval.component';
import { BuildAgentsComponent } from './pages/agents/build-agents/build-agents.component';

export const routes: Routes = [
  {
    path: 'login',
    component: LoginComponent
  },
  { path: 'callback', component: CallbackComponent },

  {
    path: 'dashboard',
    component: DashboardComponent,
    canActivate: [AuthGuard],
    data: { requireOrgPath: true }
  },
  {
    path: 'chat-demo',
    component: ChatDemoComponent,
    canActivate: [AuthGuard],
    data: { requireOrgPath: true }
  },
  {
    path: 'launch',
    canActivate: [AuthGuard],
    data: { requireOrgPath: true },
    children: [
      {
        path: 'agents',
        component: AgentsComponent
      },
      {
        path: 'agents/create',
        component: BuildAgentsComponent
      },
      // {
      //   path: 'agents/create/individual',
      //   component: IndividualAgentComponent
      // },
      // {
      //   path: 'agents/create/collaborative',
      //   component: CollaborativeAgentComponent
      // },
      // {
      //   path: 'agents/individual',
      //   component: IndividualAgentComponent
      // },
      // {
      //   path: 'agents/collaborative',
      //   component: CollaborativeAgentComponent
      // },
      {
        path: 'workflows',
        component: WorkflowsComponent
      },
      {
        path: 'workflows/create',
        component: WorkflowEditorComponent
      },
      {
        path: 'workflows/edit/:id',
        component: WorkflowEditorComponent
      },
      {
        path: 'workflows/execute/:id',
        component: WorkflowExecutionComponent
      },
      {
        path: '',
        redirectTo: '/dashboard',
        pathMatch: 'full'
      },
      {
        path: '**',
        redirectTo: '/login'
      }
    ]
  },
  {
    path: 'libraries',
    canActivate: [AuthGuard],
    data: { requireOrgPath: true },
    children: [
      {
        path: 'prompts',
        component: PromptsComponent
      },
      {
        path: 'prompts/create',
        component: CreatePromptsComponent
      },
      {
        path: 'prompts/edit/:id',
        component: CreatePromptsComponent
      },
      {
        path: 'models',
        component: ModelsComponent
      },
      {
        path: 'models/create',
        component: CreateModelsComponent
      },
      {
        path: 'models/edit/:id',
        component: CreateModelsComponent
      },
      {
        path: 'knowledge-base',
        component: KnowledgeBaseComponent
      },
      {
        path: 'knowledge-base/create',
        component: CreateKnowledgeBaseComponent
      },
      {
        path: 'knowledge-base/edit/:id',
        component: CreateKnowledgeBaseComponent
      },
      {
        path: 'tools',
        component: ToolsComponent
      },
      {
        path: 'tools/create',
        component: CreateToolsComponent
      },
      {
        path: 'tools/edit/:id',
        component: CreateToolsComponent
      },
      {
        path: 'guardrails',
        component: GuardrailsComponent
      },
      {
        path: 'guardrails/create',
        component: CreateGuardrailsComponent
      },
      {
        path: 'guardrails/edit/:id',
        component: CreateGuardrailsComponent
      },
      {
        path: '',
        redirectTo: '/libraries/prompts',
        pathMatch: 'full'
      }
    ]
  },
  {
    path: 'manage',
    canActivate: [AuthGuard],
    data: { requireOrgPath: true },
    children: [
      {
        path: 'admin-management',
        component: AdminManagementComponent
      },
      {
        path: '',
        redirectTo: '/admin-management',
        pathMatch: 'full'
      }
    ]
  },
  {
    path: 'org-config',
    component: OrgConfigComponent,
    canActivate: [AuthGuard],
    data: { requireOrgPath: true }
  },
  {
    path: 'approval',
    component: ApprovalComponent,
    canActivate: [AuthGuard],
    data: { requireOrgPath: true }
  },
  {
    path: '',
    redirectTo: '/dashboard',
    pathMatch: 'full'
  },
];
