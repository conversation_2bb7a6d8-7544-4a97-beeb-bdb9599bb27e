<div class="agents-container">
  <div class="agents-header">
    <div class="header-content">
      <div class="search-section">
        <ava-textbox placeholder="Search..."></ava-textbox>
      </div>
      <div class="action-buttons">
        <ava-dropdown
          dropdownTitle="choose agent"
          [options]="agentsOptions"
          (selectionChange)="onSelectionChange($event)"
        >
        </ava-dropdown>
      </div>
    </div>
  </div>

  <div class="cards-container">
    <ava-text-card
      [type]="'create'"
      [iconName]="'plus'"
      iconColor="#144692"
      [title]="agentsConfig.labels.createAgent"
      (cardClick)="onCreateAgent()"
    >
    </ava-text-card>

    <!-- No results message -->
    <div class="no-results" *ngIf="displayedAgents.length === 0">
      No Agent found matching your criteria
    </div>
    <!-- Agent Data Cards -->
    <ava-text-card
      *ngFor="let agent of displayedAgents"
      [type]="'prompt'"
      [title]="agent.title"
      [name]="agent.name || 'AAVA'"
      [date]="agent.createdDate"
      [iconList]="iconList"
      iconColor="#144692"
      [userCount]="agent.userCount || 0"
      (iconClick)="onIconClicked($event, agent.id)"
      (cardClick)="onCardClicked(agent.id)"
    >
    </ava-text-card>
  </div>

  <!-- Page Footer with Pagination -->
  <app-page-footer
    *ngIf="filteredAgents.length > 0"
    [totalItems]="filteredAgents.length + (showCreateCard ? 1 : 0)"
    [currentPage]="currentPage"
    [itemsPerPage]="itemsPerPage"
    (pageChange)="onPageChange($event)"
  >
  </app-page-footer>
</div>
