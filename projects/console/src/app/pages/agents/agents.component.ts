import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { PageFooterComponent } from '../../shared/components/page-footer/page-footer.component';
import { PaginationService } from '../../shared/services/pagination.service';
import { AgentServiceService } from './services/agent-service.service';
import agentsConfigData from './constants/agents.json';
import {
  AvaTextboxComponent,
  DropdownComponent,
  DropdownOption,
  TextCardComponent,
} from '@ava/play-comp-library';
import { LucideAngularModule } from 'lucide-angular';
import { FilterConfig } from '../../shared/models/filter.model';

@Component({
  selector: 'app-agents',
  standalone: true,
  imports: [
    CommonModule,
    PageFooterComponent,
    TextCardComponent,
    AvaTextboxComponent,
    DropdownComponent,
    LucideAngularModule,
  ],
  templateUrl: './agents.component.html',
  styleUrl: './agents.component.scss',
})
export class AgentsComponent implements OnInit {
  allAgents: any[] = [];
  filteredAgents: any[] = [];
  displayedAgents: any[] = [];
  agentsConfig = agentsConfigData;
  isLoading: boolean = false;
  error: string | null = null;
  currentPage: number = 1;
  itemsPerPage: number = agentsConfigData.pagination.itemsPerPage;
  totalPages: number = 1;
  agentsOptions: DropdownOption[] = [
    { name: 'All', value: 'all' },
    { name: 'Owned by me', value: 'owned' },
    { name: 'Experience', value: 'experience' },
    { name: 'Product', value: 'product' },
    { name: 'Data', value: 'data' },
    { name: 'Finops', value: 'finops' },
    { name: 'Quality Engineering', value: 'quality' },
    { name: 'Platform', value: 'platform' },
  ];
  selectedData: any = null;
  iconList = [
    { name: 'archive', iconName: 'archive', cursor: true },
    { name: 'trash', iconName: 'trash-2', cursor: true },
    { name: 'copy', iconName: 'copy', cursor: true },
    { name: 'play', iconName: 'play', cursor: true },
  ];
  
  agentFilterConfig!: FilterConfig;

  // Track filter bar visibility
  isFilterBarVisible: boolean = false;

  

  // Map filter IDs to CardData properties
  private filterPropertyMap: {[key: string]: string} = agentsConfigData.filterProperties;

  constructor(
    private paginationService: PaginationService,
    private router: Router,
    private agentService: AgentServiceService,
  ) {}

  ngOnInit(): void {
    this.fetchAgents();
  }

  fetchAgents(): void {
    this.isLoading = true;
    this.agentService.getAllAgentList().subscribe({
      next: (response) => {
        this.allAgents = response.agentWithUseCaseDetails.map((agent: any) => {
          const createdAtDate = new Date(agent.createdAt);
          const formattedDate = `${createdAtDate.getMonth() + 1}/${createdAtDate.getDate()}/${createdAtDate.getFullYear()}`;

          return {
            ...agent,
            id: agent.id.toString(),
            title: agent.name,
            name: agent.name,
            createdDate: formattedDate,
            userCount: agent.users || 0,
          };
        });

        this.filteredAgents = [...this.allAgents];
        this.updateDisplayedAgents();
        this.isLoading = false;
      },
      error: (error) => {
        this.error = error.message || 'Failed to load agents';
        this.isLoading = false;
      },
    });
  }

  updateDisplayedAgents(): void {
    const result = this.paginationService.getPaginatedItems(
      this.filteredAgents,
      this.currentPage,
      this.itemsPerPage,
    );

    this.displayedAgents = result.displayedItems;
    this.totalPages = result.totalPages;
  }

  onCreateAgent(): void {
    this.router.navigate([agentsConfigData.navigation.createRoute]);
  }

  navigateToBuildAgent(): void {
    this.router.navigate(['/launch/agents/build']);
  }

  onCardClicked(agentId: string): void {
    console.log(`Agent card clicked: ${agentId}`);

    // Find the agent to determine its type
    const agent = this.allAgents.find(a => a.id === agentId);
    if (agent) {
      // Check if it's a collaborative agent
      const isCollaborative = agent.tags.some((tag: { label: string; }) => tag.label === 'Collaborative');

      // Navigate to the appropriate agent page
      const route = isCollaborative ? agentsConfigData.navigation.collaborativeRoute : agentsConfigData.navigation.individualRoute;
      this.router.navigate([route], {
        queryParams: { id: agentId },
      });
    }
  }

  onIconClicked(icon: any, agentId: string): void {
    switch (icon.name) {
      case 'trash':
        this.deleteAgent(agentId);
        break;
      case 'edit':
        this.router.navigate([`/agent-list/edit/${agentId}`]);
        break;
      case 'copy':
        this.duplicateAgent(agentId);
        break;
      default:
        break;
    }
  }

  deleteAgent(agentId: string): void {
    // Implement delete logic
  }

  duplicateAgent(agentId: string): void {
    // Implement duplicate logic
  }

  onSelectionChange(data: any) {
    this.selectedData = data;
    // Implement filter logic if needed
  }

  onPageChange(page: number): void {
    this.currentPage = page;
    this.updateDisplayedAgents();
  }

  get showCreateCard(): boolean {
    return this.currentPage === 1 && !this.isLoading && !this.error;
  }
}
