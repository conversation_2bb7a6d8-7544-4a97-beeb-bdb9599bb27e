import { <PERSON><PERSON>nent, On<PERSON>nit, <PERSON><PERSON><PERSON><PERSON>, ViewChild, ElementRef, AfterViewInit, ChangeDetectorRef, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, FormControl } from '@angular/forms';
import { Router, ActivatedRoute, RouterModule } from '@angular/router';
import { FormFieldComponent } from '../../../shared/components/form-field/form-field.component';
import { WorkflowGraphService, WorkflowNode, WorkflowEdge } from './services/workflow-graph.service';
import { ReactFlowService } from './services/react-flow.service';
import { AgentNodeComponent } from './components/agent-node/agent-node.component';
import { Subscription } from 'rxjs';
import { Agent } from './models/agent.model';
import { DragDropModule, CdkDragDrop } from '@angular/cdk/drag-drop';
import { CardComponent } from '../../../shared/components/card/card.component';
import workflowLabels from './../constants/workflows.json';
import { SelectDropdownComponent } from '../../../shared/components/select-dropdown/select-dropdown.component';
import { WorkflowService } from '../../../shared/services/workflow.service';
import { CanvasBoardComponent, CanvasNode, CanvasEdge } from '../../../shared/components/canvas-board/canvas-board.component';

interface NodePosition {
  x: number;
  y: number;
}

@Component({
  selector: 'app-workflow-editor',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    RouterModule,
    FormFieldComponent,
    AgentNodeComponent,
    DragDropModule,
    CardComponent,
    SelectDropdownComponent,
    CanvasBoardComponent
  ],
  templateUrl: './workflow-editor.component.html',
  styleUrls: ['./workflow-editor.component.scss']
})
export class WorkflowEditorComponent implements OnInit, AfterViewInit, OnDestroy {
  @Input() primaryButtonText: string = '';

  workflowId: string | null = null;
  isEditMode: boolean = false;
  workflowForm: FormGroup;

  // Labels used across the Knowledge Base UI components (titles)
  public workFlowLabels = workflowLabels.labels;

  levels = ['organization', 'domain', 'project','team'];
  filterLabels = ['organization', 'domain', 'project','team'];
  optionsMap: { [level: number]: any[] } = {};
  levelOptionsMap: Record<number, any[]> = {};

  // Canvas board properties
  canvasNodes: CanvasNode[] = [];
  canvasEdges: CanvasEdge[] = [];
  navigationHints: string[] = [
    `${workflowLabels.labels.alt} + ${workflowLabels.labels.drag} ${workflowLabels.labels.toPanCanvas}`,
    `${workflowLabels.labels.mouseWheel} ${workflowLabels.labels.toZoom}`,
    `${workflowLabels.labels.space} ${workflowLabels.labels.toResetView}`
  ];

  // Agent library
  agents: Agent[] = [
    {
      id: 'agent1',
      name: 'Test Agent Agent Agent Agent Agent Agent',
      description: 'Translating user needs into actionable development tasks.',
      type: 'Individual',
      capabilities: ['Code Generation', 'Translation']
    },
    {
      id: 'agent2',
      name: 'Test Agent 2',
      description: 'Processing data and generating insights.',
      type: 'Individual',
      capabilities: ['Data Analysis', 'Visualization']
    },
    {
      id: 'agent3',
      name: 'Test Agent 3',
      description: 'Handling complex natural language processing tasks.',
      type: 'Collaborative',
      capabilities: ['NLP', 'Summarization', 'Translation']
    }
  ];

  // Filtered agents for search
  filteredAgents: Agent[] = [];

  // Available agents (filtered and not used yet)
  availableAgents: Agent[] = [];

  // Workflow nodes and edges (original format)
  nodes: WorkflowNode[] = [];
  edges: WorkflowEdge[] = [];

  // Selected node
  selectedNodeId: string | null = null;

  // Subscriptions
  private subscriptions: Subscription[] = [];

  // Track used agent IDs
  usedAgentIds: Set<string> = new Set();



  constructor(
    private fb: FormBuilder,
    private router: Router,
    private route: ActivatedRoute,
    private workflowGraphService: WorkflowGraphService,
    private workflowService: WorkflowService,
    private reactFlowService: ReactFlowService,
    private cdr: ChangeDetectorRef
  ) {
    this.workflowForm = this.fb.group({
      // Workflow details
      name: [''],
      description: [''],

      // Filters
      organization: [''],
      domain: [''],
      project: [''],
      team: [''],

      // Enable Manager LLM
      enableManagerLLM: [false],

      // LLM Configuration settings
      temperature: [0.3],
      topP: [0.95],
      maxRPM: [0],
      maxToken: [4000],
      maxIteration: [1],
      maxExecutionTime: [30],

      // Search filter
      agentFilter: ['']
    });

    // Initialize filtered agents
    this.filteredAgents = [...this.agents];
  }

  ngOnInit(): void {
    this.fetchChildOptions(0, -1);
    // Check if we're in edit mode
    this.workflowId = this.route.snapshot.paramMap.get('id');
    this.isEditMode = !!this.workflowId;

    if (this.isEditMode && this.workflowId) {
      // In a real app, you would fetch the workflow data by ID
      console.log(`Editing workflow with ID: ${this.workflowId}`);
      // this.loadWorkflowData(this.workflowId);
    }

    // Subscribe to nodes to track used agents
    this.subscriptions.push(
      this.workflowGraphService.nodes$.subscribe(nodes => {
        this.nodes = nodes;
        this.canvasNodes = this.convertToCanvasNodes(nodes);

        // Update used agent IDs
        this.updateUsedAgentIds();
      })
    );

    this.subscriptions.push(
      this.workflowGraphService.edges$.subscribe(edges => {
        this.edges = edges;
        this.canvasEdges = this.convertToCanvasEdges(edges);
      })
    );



    // Subscribe to the search filter changes
    this.subscriptions.push(
      this.getControl('agentFilter').valueChanges.subscribe(filterValue => {
        this.filterAgents(filterValue);
      })
    );
  }

  ngAfterViewInit(): void {
    // Canvas board handles its own initialization
  }

  // Convert WorkflowNode to CanvasNode
  private convertToCanvasNodes(nodes: WorkflowNode[]): CanvasNode[] {
    return nodes.map(node => ({
      id: node.id,
      type: node.type,
      data: node.data,
      position: node.position
    }));
  }

  // Convert WorkflowEdge to CanvasEdge
  private convertToCanvasEdges(edges: WorkflowEdge[]): CanvasEdge[] {
    return edges.map(edge => ({
      id: edge.id,
      source: edge.source,
      target: edge.target,
      animated: edge.animated
    }));
  }

  onLevelChange(level: number, selectedValue: string | string[]): void {
  const selected = Array.isArray(selectedValue) ? selectedValue[0] : selectedValue;

  if (!selected) {
    return;
  }

  const controlName = this.filterLabels[level];
  const control = this.workflowForm.get(controlName);

  if (control) {
    control.setValue(selected);
  }

  // Reset controls and options for levels below the current one
  for (let i = level + 1; i < this.levels.length; i++) {
    this.resetControlAtLevel(i);
    this.levelOptionsMap[i] = [];
  }

  const selectedNumber = Number(selected);
  if (!isNaN(selectedNumber)) {
    this.fetchChildOptions(level + 1, selectedNumber);
  }
}



resetControlAtLevel(level: number): void {
  const controlName = this.filterLabels[level];
  const control = this.workflowForm.get(controlName);
  if (control) {
    control.setValue(null);
  }
}


getOptionsForLevel(level: number): any[] {
  return this.levelOptionsMap[level] || [];
}


fetchChildOptions(level: number, parentId: number) {
  if (!this.filterLabels[level]) return;

  this.workflowService.getDropdownList(level, parentId).subscribe({
    next: (res) => {
      this.levelOptionsMap[level] = Array.isArray(res) ? res : [];
    },
    error: () => {
      this.levelOptionsMap[level] = [];
    }
  });
}
  ngOnDestroy(): void {
    // Clean up subscriptions
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  // Canvas board event handlers
  onCanvasDropped(event: {event: DragEvent, position: {x: number, y: number}}): void {
    const dragEvent = event.event;
    const position = event.position;

    if (dragEvent.dataTransfer) {
      const agentData = dragEvent.dataTransfer.getData('application/reactflow');

      if (agentData) {
        try {
          const agent = JSON.parse(agentData);

          // Create a new node for the agent
          const newNode: WorkflowNode = {
            id: this.workflowGraphService.generateNodeId(),
            type: 'agent',
            data: {
              label: agent.name,
              agentId: agent.id,
              agentName: agent.name,
              description: agent.description,
              capabilities: agent.capabilities,
              width: 280 // Default width
            },
            position: position
          };

          this.workflowGraphService.addNode(newNode);
        } catch (error) {
          console.error('Error adding node:', error);
        }
      }
    }
  }

  onConnectionCreated(edge: CanvasEdge): void {
    const newEdge: WorkflowEdge = {
      id: edge.id,
      source: edge.source,
      target: edge.target,
      animated: edge.animated || true
    };

    this.workflowGraphService.addEdge(newEdge);
  }

  filterAgents(filterValue: string): void {
    // First filter by search term
    if (!filterValue || filterValue.trim() === '') {
      this.filteredAgents = [...this.agents];
    } else {
      filterValue = filterValue.toLowerCase().trim();
      this.filteredAgents = this.agents.filter(agent =>
        agent.name.toLowerCase().includes(filterValue) ||
        agent.description.toLowerCase().includes(filterValue) ||
        (agent.type && agent.type.toLowerCase().includes(filterValue)) ||
        (agent.capabilities && agent.capabilities.some(cap => cap.toLowerCase().includes(filterValue)))
      );
    }

    // Then filter out agents that are already used
    this.updateAvailableAgents();
  }

  updateAvailableAgents(): void {
    this.availableAgents = this.filteredAgents.filter(agent =>
      !this.usedAgentIds.has(agent.id)
    );
  }



  onDragStart(event: DragEvent, agent: Agent): void {
    if (event.dataTransfer) {
      event.dataTransfer.setData('application/reactflow', JSON.stringify(agent));
      event.dataTransfer.effectAllowed = 'move';
    }
  }

  onDeleteNode(nodeId: string): void {
    this.workflowGraphService.removeNode(nodeId);

    // If the deleted node was selected, clear selection
    if (this.selectedNodeId === nodeId) {
      this.selectedNodeId = null;
    }
  }

  onNodeSelected(nodeId: string): void {
    this.selectedNodeId = nodeId;
  }

  onNodeMoved(data: {nodeId: string, position: NodePosition}): void {
    // Find the node index
    const nodeIndex = this.nodes.findIndex(node => node.id === data.nodeId);
    if (nodeIndex === -1) return;

    // Create a new array with the updated node
    const updatedNodes = [...this.nodes];
    updatedNodes[nodeIndex] = {
      ...this.nodes[nodeIndex],
      position: data.position
    };

    // Update the node positions through service
    this.workflowGraphService.updateNodePositions(updatedNodes);

    // Force a change detection cycle
    this.cdr.detectChanges();
  }

  onStartConnection(data: {nodeId: string, handleType: 'source' | 'target', event: MouseEvent}): void {
    // Canvas board handles connection logic
    // This method is called when a connection starts but the canvas board manages the temp connection
  }



  updateNodePosition(data: {nodeId: string, position: {x: number, y: number}}): void {
    // Canvas board handles connection point updates
  }



  onSave(): void {
    const workflowData = {
      ...this.workflowForm.value,
      nodes: this.nodes,
      edges: this.edges
    };

    console.log('Saving workflow:', workflowData);

    if (this.isEditMode) {
      console.log('Updating existing workflow');
      // this.workflowService.updateWorkflow(this.workflowId, workflowData);
    } else {
      console.log('Creating new workflow');
      // this.workflowService.createWorkflow(workflowData);
    }

    this.router.navigate(['/launch/workflows']);
  }

  onExit(): void {
    this.router.navigate(['/launch/workflows']);
  }

  onReset(): void {
    this.workflowGraphService.clearWorkflow();
    this.selectedNodeId = null;
  }

  onUndo(): void {
    console.log('Undo triggered from workflow editor');
  }

  onRedo(): void {
    console.log('Redo triggered from workflow editor');
  }

  onCanvasStateChanged(state: {nodes: CanvasNode[], edges: CanvasEdge[]}): void {
    console.log('Canvas state changed:', state);

    // Convert canvas nodes back to workflow nodes
    const workflowNodes: WorkflowNode[] = state.nodes.map(node => ({
      id: node.id,
      type: node.type,
      data: node.data,
      position: node.position
    }));

    // Convert canvas edges back to workflow edges
    const workflowEdges: WorkflowEdge[] = state.edges.map(edge => ({
      id: edge.id,
      source: edge.source,
      target: edge.target,
      animated: edge.animated
    }));

    // Update the workflow service to sync the state
    this.workflowGraphService.setNodes(workflowNodes);
    this.workflowGraphService.setEdges(workflowEdges);
  }



  onExecute(): void {
    console.log('Executing workflow');
    // Navigate to workflow execution page
    if (this.workflowId) {
      this.router.navigate(['/launch/workflows/execute', this.workflowId]);
    } else {
      // For new workflows, save first then navigate
      alert('Please save the workflow before executing it.');
    }
  }

  // Helper method to get form controls easily from the template
  getControl(name: string): FormControl {
    return this.workflowForm.get(name) as FormControl;
  }

  /**
   * Check if an agent is already used in the workflow
   * @param agentId ID of the agent to check
   */
  isAgentUsed(agentId: string): boolean {
    return this.usedAgentIds.has(agentId);
  }

  /**
   * Update the set of used agent IDs
   */
  updateUsedAgentIds(): void {
    this.usedAgentIds.clear();
    this.nodes.forEach(node => {
      if (node.data && node.data.agentId) {
        this.usedAgentIds.add(node.data.agentId);
      }
    });

    // Update available agents whenever used agents change
    this.updateAvailableAgents();
  }
}
