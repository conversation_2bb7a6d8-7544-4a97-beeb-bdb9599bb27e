<div class="workflow-editor-container">
  <!-- Main content area with left sidebar and right editor -->
  <div class="editor-layout">
    <!-- Left sidebar for workflow details and agent library -->
    <div class="sidebar">
      <!-- Workflow Details -->
      <app-card>
        <div class="sidebar-section">
          <h3>{{workFlowLabels.workflowName}}</h3>
          <app-form-field
            [control]="getControl('name')"
            placeholder="{{workFlowLabels.workflowPlaceholder}}"
            type="text">
          </app-form-field>

          <h3 class="description-label">{{workFlowLabels.workflowPlaceholder}}</h3>
          <app-form-field
            [control]="getControl('description')"
            placeholder="{{workFlowLabels.describeWorkflow}}"
            type="textarea">
          </app-form-field>
          <ng-container *ngFor="let level of levels; let i = index">
            <h3 class="text-capitalize">{{ level }}</h3>
            <div class="dropdown-container">
              <app-select-dropdown [options]="getOptionsForLevel(i)" [formControl]="getControl(level)"
                [placeholder]="'Select ' + level" (selectionChange)="onLevelChange(i, $event)">
              </app-select-dropdown>
            </div>
          </ng-container>
          </div>
          </app-card>
      <app-card>
        <!-- Agent Library -->
        <div class="sidebar-section agent-library">
          <h3>{{workFlowLabels.agentLibrary}}</h3>
          <p class="help-text">
            {{workFlowLabels.agentLibraryHelpText}}
          </p>

          <!-- Search Filter -->
          <div class="search-filter">
            <app-form-field [control]="getControl('agentFilter')"
              placeholder="{{workFlowLabels.searchAgents}}"
              type="text"></app-form-field>
          </div>

          <!-- Draggable Agents Container with Fixed Height and Scroll -->
          <div class="agent-list-container">
            <div class="agent-list">
              <div *ngFor="let agent of availableAgents" class="agent-item" draggable="true"
                (dragstart)="onDragStart($event, agent)">
                <h4>{{ agent.name }}</h4>
                <p>{{ agent.description }}</p>
                <div class="agent-tags" *ngIf="agent.capabilities && agent.capabilities.length > 0">
                  <span class="agent-tag" *ngFor="let capability of agent.capabilities">{{ capability }}</span>
                </div>
              </div>

              <!-- No results message -->
              <div class="no-agents" *ngIf="availableAgents.length === 0">
                {{workFlowLabels.noResults}}
              </div>
            </div>
          </div>
        </div>

        <!-- Enable Manager LLM -->
        <div class="sidebar-section llm-toggle">
          <div class="toggle-container">
            <label class="switch">
              <input type="checkbox" [formControl]="getControl('enableManagerLLM')" />
              <span class="slider"></span>
            </label>
            <span class="toggle-label">{{workFlowLabels.managerLLMToggleLabel}}</span>
          </div>

          <!-- LLM Settings - Only visible when enableManagerLLM is true -->
          <div class="llm-settings" *ngIf="getControl('enableManagerLLM').value">
            <!-- Temperature -->
            <div class="setting-item">
              <h3>{{workFlowLabels.temperature}}</h3>
              <div class="slider-with-value">
                <input type="range" min="0" max="1" step="0.1"
                  [formControl]="getControl('temperature')"
                  class="setting-slider" />
                <div class="value-display">
                  {{ getControl("temperature").value }}
                </div>
              </div>
            </div>

            <!-- Top P -->
            <div class="setting-item">
              <h3>{{workFlowLabels.topP}}</h3>
              <input type="number" min="0" max="1" step="0.01" [formControl]="getControl('topP')"
                class="setting-input" />
            </div>

            <!-- Max RPM -->
            <div class="setting-item">
              <h3>{{workFlowLabels.maxRPM}}</h3>
              <input type="number" min="0" [formControl]="getControl('maxRPM')" class="setting-input" />
            </div>

            <!-- Max Token -->
            <div class="setting-item">
              <h3>{{workFlowLabels.maxToken}}</h3>
              <div class="token-container">
                <input type="number" min="0" [formControl]="getControl('maxToken')" class="setting-input" />
                <span class="tokens-used">
                  {{ getControl("maxToken").value }}/{{workFlowLabels.tokensUsed}}
                </span>
              </div>
            </div>

            <!-- Max Iteration -->
            <div class="setting-item">
              <h3>{{workFlowLabels.maxIteration}}</h3>
              <input type="number" min="1" [formControl]="getControl('maxIteration')" class="setting-input" />
            </div>

            <!-- Max Execution Time -->
            <div class="setting-item">
              <h3>{{workFlowLabels.maxExecutionTime}}</h3>
              <input type="number" min="0" [formControl]="getControl('maxExecutionTime')" class="setting-input" />
            </div>
          </div>
        </div>
      </app-card>
    </div>

    <!-- Right side workflow editor canvas -->

    <div class="editor-canvas">
      <h2>{{workFlowLabels.workflowEditor}}</h2>

      <!-- Canvas Board Component -->
      <app-canvas-board
        [nodes]="canvasNodes"
        [edges]="canvasEdges"
        [navigationHints]="navigationHints"
        [fallbackMessage]="workFlowLabels.fallbackMessage"
        [primaryButtonText]="workFlowLabels.execute"
        (canvasDropped)="onCanvasDropped($event)"
        (nodeSelected)="onNodeSelected($event)"
        (nodeMoved)="onNodeMoved($event)"
        (nodeRemoved)="onDeleteNode($event)"

        (connectionStarted)="onStartConnection($event)"
        (connectionCreated)="onConnectionCreated($event)"
        (undoAction)="onUndo()"
        (redoAction)="onRedo()"
        (resetAction)="onReset()"
        (primaryButtonClicked)="onExecute()"
        (stateChanged)="onCanvasStateChanged($event)">

        <!-- Node template for rendering agent nodes -->
        <ng-template #nodeTemplate let-node let-selected="selected" let-onDelete="onDelete"
                     let-onMove="onMove" let-onSelect="onSelect" let-onStartConnection="onStartConnection">
          <app-agent-node
            [node]="node"
            [selected]="selected"
            (deleteNode)="onDelete($event)"
            (moveNode)="onMove($event)"
            (nodeSelected)="onSelect($event)"
            (startConnection)="onStartConnection($event)"

            (nodePositionChanged)="updateNodePosition($event)">
          </app-agent-node>
        </ng-template>
      </app-canvas-board>
    </div>
  </div>
</div>
