.workflow-editor-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 20px;
  font-family: var(--font-family);
}

.breadcrumb {
  margin-bottom: 20px;
  font-size: 14px;

  a {
    color: var(--text-secondary);
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }

  span {
    font-weight: 500;
  }
}

.editor-layout {
  display: flex;
  gap: 20px;
  height: calc(100vh - 120px);

  .sidebar {
    width: 350px;
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    gap: 20px;
    max-height: 100%;
    overflow-y: auto;
    padding-right: 5px;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: var(--dashboard-scrollbar-track);
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: var(--dashboard-scrollbar-thumb);
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: var(--dashboard-scrollbar-thumb-hover);
    }

    .sidebar-section {
      padding: 20px;

      h3 {
        margin-top: 0;
        margin-bottom: 12px;
        font-size: 16px;
        font-weight: 500;
        color: var(--text-color);

        &.description-label {
          margin-top: 20px;
        }
      }

      .text-capitalize {
        text-transform: capitalize;
      }

      input[type="text"], textarea {
        width: 100%;
        padding: 12px;
        border: 1px solid var(--form-input-border);
        border-radius: 6px;
        font-size: 14px;
        outline: none;
        background-color: var(--form-input-bg);
        color: var(--form-input-color);

        &:focus {
          border-color: var(--form-input-focus-border);
          box-shadow: 0 0 0 2px var(--form-input-focus-shadow);
        }
      }

      textarea {
        min-height: 80px;
        resize: vertical;
      }
    }

    .agent-library {
      flex-shrink: 0;
      display: flex;
      flex-direction: column;
      height: auto;

      .help-text {
        font-size: 12px;
        color: var(--text-secondary);
        margin-bottom: 16px;
      }

      .search-filter {
        margin-bottom: 16px;
      }

      .agent-list-container {
        height: 300px;
        position: relative;
        margin-bottom: 10px;
      }

      .agent-list {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        overflow-y: auto;
        display: flex;
        flex-direction: column;
        gap: 12px;
        padding-right: 8px;

        &::-webkit-scrollbar {
          width: 6px;
        }

        &::-webkit-scrollbar-track {
          background: var(--dashboard-scrollbar-track);
          border-radius: 3px;
        }

        &::-webkit-scrollbar-thumb {
          background: var(--dashboard-scrollbar-thumb);
          border-radius: 3px;
        }

        &::-webkit-scrollbar-thumb:hover {
          background: var(--dashboard-scrollbar-thumb-hover);
        }

        .agent-item {
          background-color: var(--card-bg);
          border: 1px solid var(--agent-card-border);
          border-radius: 8px;
          padding: 12px;
          cursor: grab;
          transition: all 0.2s ease;

          &:hover {
            border-color: var(--agent-template-card-hover-border);
            box-shadow: 0 0 0 2px var(--dashboard-shadow-hover);
          }

          &.disabled {
            opacity: 0.5;
            pointer-events: none;
            cursor: not-allowed;
            border-color: var(--form-input-border);
            background-color: var(--form-input-bg);
          }

          h4 {
            margin: 0 0 4px 0;
            font-size: 14px;
            font-weight: 500;
            color: var(--text-color);
          }

          p {
            margin: 0 0 8px 0;
            font-size: 12px;
            color: var(--text-secondary);
          }

          .agent-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 4px;

            .agent-tag {
              font-size: 10px;
              padding: 2px 6px;
              background-color: var(--dropdown-selected-bg);
              color: var(--dropdown-selected-text);
              border-radius: 10px;
              font-weight: 500;
            }
          }
        }

        .no-agents {
          padding: 20px;
          text-align: center;
          color: var(--text-secondary);
          font-size: 14px;
        }
      }
    }

    .llm-toggle {
      .toggle-container {
        display: flex;
        align-items: center;

        .toggle-label {
          margin-left: 12px;
          font-size: 14px;
          color: var(--text-color);
          font-weight: 500;
        }
      }

      /* Toggle Switch */
      .switch {
        position: relative;
        display: inline-block;
        width: 50px;
        height: 24px;

        input {
          opacity: 0;
          width: 0;
          height: 0;

          &:checked + .slider {
            background-color: var(--dashboard-primary);
          }

          &:checked + .slider:before {
            transform: translateX(26px);
          }
        }

        .slider {
          position: absolute;
          cursor: pointer;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background-color: #ccc;
          transition: .4s;
          border-radius: 24px;

          &:before {
            position: absolute;
            content: "";
            height: 16px;
            width: 16px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
          }
        }
      }
    }
  }

  .editor-canvas {
    flex-grow: 1;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    padding: 20px;
    border: 1px solid var(--card-border);
    background-color: var(--card-bg);
    box-shadow: 0 4px 16px var(--card-shadow);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);

    h2 {
      margin-top: 0;
      margin-bottom: 16px;
      font-size: 18px;
      font-weight: 500;
      color: var(--text-color);
    }

    app-canvas-board {
      flex-grow: 1;
      margin-bottom: 20px;
    }


  }
}



.toggle-container {
  display: flex;
  align-items: center;

  .toggle-label {
    margin-left: 12px;
    font-size: 14px;
    color: var(--text-color);
    font-weight: 500;
  }
}

/* LLM Settings styles */
.llm-settings {
  margin-top: 16px;

  .setting-item {
    margin-bottom: 16px;

    h3 {
      margin-top: 0;
      margin-bottom: 8px;
      font-size: 16px;
      font-weight: 500;
      color: var(--text-color);
    }

    .slider-with-value {
      display: flex;
      align-items: center;
      gap: 12px;

      .setting-slider {
        flex-grow: 1;
        height: 6px;
        -webkit-appearance: none;
        appearance: none;
        background: var(--agent-slider-bg);
        border-radius: 3px;
        outline: none;

        &::-webkit-slider-thumb {
          -webkit-appearance: none;
          appearance: none;
          width: 18px;
          height: 18px;
          border-radius: 50%;
          background: var(--agent-slider-thumb-bg);
          cursor: pointer;
          box-shadow: var(--agent-slider-thumb-shadow);
        }

        &::-moz-range-thumb {
          width: 18px;
          height: 18px;
          border-radius: 50%;
          background: var(--agent-slider-thumb-bg);
          cursor: pointer;
          border: none;
          box-shadow: var(--agent-slider-thumb-shadow);
        }
      }

      .value-display {
        min-width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        color: var(--form-input-color);
        background: var(--form-input-bg);
        border-radius: 6px;
        padding: 0 10px;
        border: 1px solid var(--form-input-border);
      }
    }

    .setting-input {
      width: 100%;
      padding: 10px;
      border: 1px solid var(--form-input-border);
      border-radius: 6px;
      font-size: 14px;
      background-color: var(--form-input-bg);
      color: var(--form-input-color);
    }

    .token-container {
      position: relative;

      .tokens-used {
        position: absolute;
        right: 0;
        top: -20px;
        font-size: 12px;
        color: var(--text-secondary);
      }
    }
  }
}
