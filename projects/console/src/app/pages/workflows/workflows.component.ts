import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { PageFooterComponent } from '../../shared/components/page-footer/page-footer.component';
import { PaginationService } from '../../shared/services/pagination.service';
import { WorkflowService } from '../../shared/services/workflow.service';
import {
  AvaTextboxComponent,
  DropdownComponent,
  DropdownOption,
  TextCardComponent,
} from '@ava/play-comp-library';
import { LucideAngularModule } from 'lucide-angular';
import workflowLabels from './constants/workflows.json';

@Component({
  selector: 'app-workflows',
  standalone: true,
  imports: [
    CommonModule,
    PageFooterComponent,
    TextCardComponent,
    AvaTextboxComponent,
    DropdownComponent,
    LucideAngularModule,
  ],
  templateUrl: './workflows.component.html',
  styleUrl: './workflows.component.scss',
})
export class WorkflowsComponent implements OnInit {
  public workFlowLabels = workflowLabels.labels;
  allWorkflows: any[] = [];
  filteredWorkflows: any[] = [];
  displayedWorkflows: any[] = [];
  isLoading: boolean = false;
  error: string | null = null;
  currentPage: number = 1;
  itemsPerPage: number = 12;
  totalPages: number = 1;
  workflowsOptions: DropdownOption[] = [
    { name: 'All', value: 'all' },
    { name: 'Type A', value: 'typeA' },
    { name: 'Type B', value: 'typeB' },
  ];
  selectedData: any = null;
  iconList = [
    { name: 'archive', iconName: 'archive', cursor: true },
    { name: 'trash', iconName: 'trash-2', cursor: true },
    { name: 'copy', iconName: 'copy', cursor: true },
    { name: 'play', iconName: 'play', cursor: true },
  ];

  constructor(
    private paginationService: PaginationService,
    private router: Router,
    private workflowService: WorkflowService,
  ) {}

  ngOnInit(): void {
    this.fetchAllWorkflows();
  }

  fetchAllWorkflows() {
    this.isLoading = true;
    this.workflowService.fetchAllWorkflows().subscribe({
      next: (response: any) => {
        const pipeLines = response.pipeLines || response;
        this.allWorkflows = pipeLines.map((item: any) => ({
          ...item,
          id: item.pipelineId,
          title: item.name,
          name: item.name,
          createdDate: item.createdAt,
        }));
        this.filteredWorkflows = [...this.allWorkflows];
        this.updateDisplayedWorkflows();
        this.isLoading = false;
      },
      error: (error) => {
        this.error = error.message || 'Failed to load workflows';
        this.isLoading = false;
      },
    });
  }

  updateDisplayedWorkflows(): void {
    const paginationResult = this.paginationService.getPaginatedItems(
      this.filteredWorkflows,
      this.currentPage,
      this.itemsPerPage,
    );
    this.displayedWorkflows = paginationResult.displayedItems;
    this.totalPages = paginationResult.totalPages;
  }

  onCreateWorkflow(): void {
    this.router.navigate(['/launch/workflows/create']);
  }

  onCardClicked(workflowId: string): void {
    this.router.navigate(['/launch/workflows/edit', workflowId]);
  }

  onIconClicked(icon: any, workflowId: string): void {
    switch (icon.name) {
      case 'trash':
        this.deleteWorkflow(workflowId);
        break;
      case 'edit':
        this.router.navigate([`/launch/workflows/edit/${workflowId}`]);
        break;
      case 'copy':
        this.duplicateWorkflow(workflowId);
        break;
      default:
        break;
    }
  }

  deleteWorkflow(workflowId: string): void {
    // Implement delete logic
  }

  duplicateWorkflow(workflowId: string): void {
    // Implement duplicate logic
  }

  onSelectionChange(data: any) {
    this.selectedData = data;
    // Implement filter logic if needed
  }

  onPageChange(page: number): void {
    this.currentPage = page;
    this.updateDisplayedWorkflows();
  }

  get showCreateCard(): boolean {
    return this.currentPage === 1 && !this.isLoading && !this.error;
  }
}
