<div class="workflows-container">
  <div class="workflows-header">
    <div class="header-content">
      <div class="search-section">
        <ava-textbox placeholder="Search..."></ava-textbox>
      </div>
      <div class="action-buttons">
        <ava-dropdown
          dropdownTitle="choose workflow"
          [options]="workflowsOptions"
          (selectionChange)="onSelectionChange($event)"
        >
        </ava-dropdown>
      </div>
    </div>
  </div>

  <div class="cards-container">
    <!-- Create Workflow Card - show only on first page -->
    <ava-text-card
      *ngIf="showCreateCard"
      [type]="'create'"
      [iconName]="'plus'"
      iconColor="#144692"
      title="Create Workflow"
      (cardClick)="onCreateWorkflow()"
    >
    </ava-text-card>

    <!-- No results message -->
    <div class="no-results" *ngIf="displayedWorkflows.length === 0">
      No workflow found matching your criteria
    </div>

    <!-- Workflow Data Cards -->
    <ava-text-card
      *ngFor="let workflow of displayedWorkflows"
      [type]="'prompt'"
      [title]="workflow.title"
      [name]="workflow.name || 'AAVA'"
      [date]="workflow.createdDate"
      [iconList]="iconList"
      iconColor="#144692"
      [userCount]="workflow.userCount || 0"
      (iconClick)="onIconClicked($event, workflow.id)"
      (cardClick)="onCardClicked(workflow.id)"
    >
    </ava-text-card>
  </div>

  <!-- Page Footer with Pagination -->
  <app-page-footer
    *ngIf="filteredWorkflows.length > 0"
    [totalItems]="filteredWorkflows.length + (showCreateCard ? 1 : 0)"
    [currentPage]="currentPage"
    [itemsPerPage]="itemsPerPage"
    (pageChange)="onPageChange($event)"
  >
  </app-page-footer>
</div>
