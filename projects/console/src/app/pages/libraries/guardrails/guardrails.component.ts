import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, ActivatedRoute } from '@angular/router';
import { PageFooterComponent } from '../../../shared/components/page-footer/page-footer.component';
import { PaginationService } from '../../../shared/services/pagination.service';
import {
  AvaTextboxComponent,
  DropdownComponent,
  DropdownOption,
  TextCardComponent,
} from '@ava/play-comp-library';
import { LucideAngularModule } from 'lucide-angular';
import { MOCK_GUARDRAILS } from '../../../shared/mock-data/guardrail-mock-data';

@Component({
  selector: 'app-guardrails',
  standalone: true,
  imports: [
    CommonModule,
    PageFooterComponent,
    TextCardComponent,
    AvaTextboxComponent,
    DropdownComponent,
    LucideAngularModule,
  ],
  templateUrl: './guardrails.component.html',
  styleUrl: './guardrails.component.scss',
})
export class GuardrailsComponent implements OnInit {
  allGuardrails: any[] = MOCK_GUARDRAILS;
  filteredGuardrails: any[] = [];
  displayedGuardrails: any[] = [];
  isLoading: boolean = false;
  error: string | null = null;
  currentPage: number = 1;
  itemsPerPage: number = 12;
  totalPages: number = 1;
  guardrailsOptions: DropdownOption[] = [
    { name: 'All', value: 'all' },
    { name: 'Type A', value: 'typeA' },
    { name: 'Type B', value: 'typeB' },
  ];
  selectedData: any = null;
  iconList = [
    { name: 'archive', iconName: 'archive', cursor: true },
    { name: 'trash', iconName: 'trash-2', cursor: true },
    { name: 'copy', iconName: 'copy', cursor: true },
    { name: 'play', iconName: 'play', cursor: true },
  ];

  constructor(
    private paginationService: PaginationService,
    private router: Router,
    private route: ActivatedRoute,
  ) {}

  ngOnInit(): void {
    this.filteredGuardrails = [...this.allGuardrails];
    const pageParam = this.route.snapshot.queryParamMap.get('page');
    if (pageParam) {
      this.currentPage = parseInt(pageParam, 10);
    }
    this.updateDisplayedGuardrails();
  }

  updateDisplayedGuardrails(): void {
    const result = this.paginationService.getPaginatedItems(
      this.filteredGuardrails,
      this.currentPage,
      this.itemsPerPage,
    );
    this.displayedGuardrails = result.displayedItems;
    this.totalPages = result.totalPages;
  }

  onCreateGuardrail(): void {
    this.router.navigate(['/libraries/guardrails/create']);
  }

  onCardClicked(guardrailId: string): void {
    this.router.navigate(['/libraries/guardrails/edit', guardrailId], {
      queryParams: { returnPage: this.currentPage },
    });
  }

  onIconClicked(icon: any, guardrailId: string): void {
    switch (icon.name) {
      case 'trash':
        this.deleteGuardrail(guardrailId);
        break;
      case 'edit':
        this.router.navigate([`/libraries/guardrails/edit/${guardrailId}`]);
        break;
      case 'copy':
        this.duplicateGuardrail(guardrailId);
        break;
      default:
        break;
    }
  }

  deleteGuardrail(guardrailId: string): void {
    // Implement delete logic
  }

  duplicateGuardrail(guardrailId: string): void {
    // Implement duplicate logic
  }

  onSelectionChange(data: any) {
    this.selectedData = data;
    // Implement filter logic if needed
  }

  onPageChange(page: number): void {
    this.currentPage = page;
    this.updateDisplayedGuardrails();
  }

  get showCreateCard(): boolean {
    return this.currentPage === 1 && !this.isLoading && !this.error;
  }
}
