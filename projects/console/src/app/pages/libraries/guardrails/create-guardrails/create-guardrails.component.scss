.create-guardrails-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: transparent;
}

.page-title {
  font-weight: 600;
  font-size: 20px;
}

.chat-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 400px;
  border: 1px solid #e1e4e8;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
  background: #ffffff;
}

.playground-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--dashboard-primary);
  margin: 0 0 16px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

// Make sure chat interface takes full height
app-chat-interface {
  height: 100%;
  display: flex;
  flex-direction: column;
  flex: 1;
}

form {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 260px);
  overflow: hidden;
}

.form-layout {
  display: flex;
  flex-direction: row;
  gap: 0;
  padding: 0;
  flex: 1;
  overflow: hidden;
  height: 100%;
  border: 1px solid #e1e4e8;
  background: #ffffff;

  &.three-column-layout {
    .left-column {
      width: 25%;
      transition: width 0.3s ease;
      
      @media (max-width: 1400px) {
        width: 25%;
      }
      
      @media (max-width: 1200px) {
        width: 100%;
      }
    }
    
    .middle-column {
      width: 40%;
      transition: width 0.3s ease;
      
      @media (max-width: 1400px) {
        width: 35%;
      }
      
      @media (max-width: 1200px) {
        width: 100%;
      }
    }
    .rightEnd-column {
      width: 35%;
      flex-shrink: 0;
      display: flex;
      flex-direction: column;
      overflow-y: auto;
      @media (max-width: 1400px) {
        width: 60%;
      }
      @media (max-width: 1200px) {
        width: 60%;
      }
    }
  }

  @media (max-width: 1400px) {
    gap: 16px;
    padding: 16px;
  }
  
  @media (max-width: 1200px) {
    flex-wrap: wrap;
  }
  
  @media (max-width: 576px) {
    gap: 12px;
    padding: 12px;
    flex-direction: column;
  }

  .middle-column {
    padding: 0px !important;
  }

  .left-column, .middle-column, .chat-column {
    display: flex;
    flex-direction: column;
    gap: 20px;
    height: 100%;
    padding: 16px;
    background: #ffffff;
    border-right: 1px solid #e1e4e8;
    overflow-y: auto;
    &:last-child {
      border-right: none;
    }
    @media (max-width: 1400px) {
      gap: 16px;
      padding: 16px;
    }
    
    @media (max-width: 1200px) {
      height: auto;
      padding: 16px;
      border-right: none;
      border-bottom: 1px solid #e1e4e8;
      overflow-y: visible;
      &:last-child {
        border-bottom: none;
      }
    }
    @media (max-width: 576px) {
      gap: 12px;
      width: 100% !important;
      padding: 12px;
    }
  }

  .left-column {
    width: 40%;
    flex-shrink: 0;
    transition: width 0.3s ease;
    
    @media (max-width: 1400px) {
      width: 40%;
    }
    @media (max-width: 1200px) {
      width: 100%;
    }
    app-card {
      flex-shrink: 0;
    }
    
    app-card:first-of-type {
      flex: 0 0 auto;
    }
    
    app-card:last-of-type {
      flex: 1;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      
      .card-content {
        overflow-y: auto;
      }
    }
  }

  .middle-column {
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    min-width: 300px;
    width: 60%;
    @media (max-width: 1200px) {
      width: 100%;
      min-width: unset;
    }
    .middle-column-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      height: 100%;
    }
    app-card {
      flex: 1;
      display: flex;
      flex-direction: column;
    }
  }

  .rightEnd-column {
    width: 35%;
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    @media (max-width: 1400px) {
      width: 60%;
    }
    @media (max-width: 1200px) {
      width: 60%;
    }
  }

  .chat-column {
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    background-color: var(--agent-chat-column-bg);
    border-left: 1px solid var(--agent-chat-column-border);
    box-shadow: -2px 0 10px var(--agent-chat-column-shadow);
    min-width: 250px;
    height: 100%;
    width: 35%;
    app-card {
      flex: 1;
      display: flex;
      flex-direction: column;
      height: 100%;
    }
    @media (max-width: 1200px) {
      width: 100%;
      min-width: unset;
      border-left: none;
      border-top: 1px solid var(--agent-chat-column-border);
      box-shadow: 0 -2px 10px var(--agent-chat-column-shadow);
    }
    .chat-column-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      height: 100%;
    }
    
    app-card {
      flex: 1;
      display: flex;
      flex-direction: column;
    }
  }
}

.card-content {
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  
  @media (max-width: 576px) {
    padding: 12px;
    gap: 12px;
  }
}

// Middle and chat columns need specific height
.middle-column .card-content,
.chat-column .card-content {
  flex: 1;
  height: 100%;
  display: flex;
  flex-direction: column;
}

// Make sure chat card content takes full height
.chat-column .card-content {
  padding-bottom: 0;
  display: flex;
  flex-direction: column;
  height: 100%;
  flex: 1;
  .chat-container {
    display: flex;
    flex-direction: column;
    flex: 1;
    height: 100%;
    overflow: hidden;
  }
}
 .head-section {
  display: flex;
  justify-content: space-between;
  

.section-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 8px;
  color: var(--text-color);
  
  @media (max-width: 576px) {
    font-size: 14px;
    margin-bottom: 6px;
  }
  }
 }

.code-format-buttons {
  display: flex;
  gap: 10px;
  margin-bottom: 16px;
  flex-shrink: 0;
  
  .format-button {
    padding: 8px 16px;
    border-radius: 10px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    background-color: var(--form-input-bg);
    border: none;
    color: var(--text-secondary);
    transition: all 0.2s ease;
    
    &:hover {
      background-color: var(--dropdown-hover-bg);
    }
    
    &.active {
      background-color: var(--dropdown-selected-bg);
      color: var(--dropdown-selected-text);
      font-weight: 600;
    }
  }
}

.code-editor-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  flex: 1;
  
  .code-editor-field {
    height: 100%;
    display: flex;
    flex-direction: column;
    flex: 1;
  }
  
  app-form-field {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  ::ng-deep .form-field {
    height: 100%;
    display: flex;
    flex-direction: column;
    flex: 1;
  }
  
  ::ng-deep textarea {
    font-family: 'Courier New', monospace;
    line-height: 1.5;
    padding: 16px;
    background-color: var(--form-input-bg);
    border: 1px solid var(--form-input-border);
    flex: 1;
    min-height: 350px;
    resize: none;
  }
}

.middle-column-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  padding: 33px 13px 4px;
  margin-top: auto;
  margin-bottom: 0;
  flex-shrink: 0;
  
  @media (max-width: 576px) {
    gap: 12px;
    padding: 12px 0 0;
    margin-top: auto;
    margin-bottom: 0;
  }
  
  .exit-button, .save-button, .execute-button {
    padding: 10px 24px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    
    @media (max-width: 576px) {
      padding: 8px 16px;
      font-size: 13px;
    }
  }
  
  .exit-button {
    background-color: transparent;
    border: 1px solid var(--button-secondary-border);
    color: var(--button-secondary-text);
    
    &:hover {
      background-color: var(--button-secondary-hover-bg);
    }
  }
  
  .save-button, .execute-button {
    background: var(--button-gradient);
    border: none;
    color: var(--button-primary-text);
    
    &:hover {
      opacity: var(--button-hover-opacity);
    }
  }
  
  .execute-button {
    background: var(--button-gradient);
    transition: all 0.2s ease;
    
    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 8px var(--dashboard-shadow-hover);
    }
  }
}

.note {
  font-weight: 400;
  color: #A3A7C2;
  font-size: 12px;
}

::ng-deep .card-container {
  padding: 0px !important;
  box-shadow: none !important;
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
  &.clickable:hover:not(.no-hover) {
    box-shadow: none !important;
  }
}
