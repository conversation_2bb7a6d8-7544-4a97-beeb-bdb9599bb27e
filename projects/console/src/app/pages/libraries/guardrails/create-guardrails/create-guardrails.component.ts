import { Component, On<PERSON>ni<PERSON>, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, FormControl } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { CardComponent } from '../../../../shared/components/card/card.component';
import { FormFieldComponent } from '../../../../shared/components/form-field/form-field.component';
import { ChatMessage } from '../../../../shared/components/chat-window/chat-window.component';
import { MOCK_GUARDRAILS } from '../../../../shared/mock-data/guardrail-mock-data';
import { ToolExecutionService } from '../../../../shared/services/tool-execution/tool-execution.service';
import { Subscription } from 'rxjs';
import { SelectOption } from 'projects/console/src/app/shared/components/select-dropdown/select-dropdown.component';
import { PlaygroundComponent } from 'projects/console/src/app/shared/components/playground/playground.component';
import { AvaTextareaComponent, AvaTextboxComponent, ButtonComponent } from '@ava/play-comp-library';


@Component({
  selector: 'app-create-guardrails',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    CardComponent,
    FormFieldComponent,
    PlaygroundComponent,
    AvaTextboxComponent,
    ButtonComponent,
    AvaTextareaComponent
  ],
  templateUrl: './create-guardrails.component.html',
  styleUrls: ['./create-guardrails.component.scss']
})
export class CreateGuardrailsComponent implements OnInit, OnDestroy {
  // Mode flags
  guardrailId: string | null = null;
  isEditMode: boolean = false;
  isExecuteMode: boolean = false;
  showChatInterface: boolean = false;
  
  guardrailForm: FormGroup;
  
  // Code format options
  codeFormats: string[] = ['Colang', 'YML'];
  selectedCodeFormat: string = 'Colang';
  
  // Chat interface properties
  chatMessages: ChatMessage[] = [];
  isProcessingChat: boolean = false;
  
  // Subscription
  private executionSubscription: Subscription = new Subscription();
  
  constructor(
    private fb: FormBuilder,
    private router: Router,
    private route: ActivatedRoute,
    private toolExecutionService: ToolExecutionService
  ) {
    this.guardrailForm = this.fb.group({
      // Guardrail details
      name: [''],
      description: [''],
      
      // Filters
      organization: [''],
      domain: [''],
      project: [''],
      team: [''],
      
      // Code content
      codeContent: ['<!DOCTYPE html>\n<html lang="en">\n<head>\n  <meta charset="UTF-8">\n  <meta name="viewport" content="width=device-width, initial-scale=1">\n  <title>Agentic Activity Log</title>\n  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">\n</head>\n<body>\n\n<div class="container mt-5">\n  <h2 class="text-center mb-4">Agentic Activity Log</h2>\n  \n  <div class="card shadow-lg">\n    <div class="card-body">\n      <h5 class="card-title">Lead Qualification Agent</h5>\n      <p><strong>Execution ID:</strong> LQ-20250323-001</p>\n      <p><strong>Status:</strong> <span class="badge bg-success">Completed</span></p>\n      \n      <table class="table table-bordered mt-3">\n        <thead class="table-dark">\n          <tr>\n            <th>Step</th>\n            <th>Details</th>\n            <th>Time</th>']
    });
  }

  ngOnInit(): void {
    // Check if we're in edit mode
    this.guardrailId = this.route.snapshot.paramMap.get('id');
    const executeParam = this.route.snapshot.queryParamMap.get('execute');
    
    this.isEditMode = !!this.guardrailId;
    this.isExecuteMode = executeParam === 'true';
    this.showChatInterface = this.isExecuteMode;
    
    if (this.isEditMode && this.guardrailId) {
      // Load guardrail data for editing
      console.log(`Editing guardrail with ID: ${this.guardrailId}`);
      this.loadGuardrailData(this.guardrailId);
      
      // If in execute mode, start the execution
      if (this.isExecuteMode) {
        // Initialize messages
        this.chatMessages = [
          {
            from: 'ai',
            text: 'Hi Akash, this is the guardrail testing'
          },
          {
            from: 'user',
            text: 'Test this input'
          },
          {
            from: 'ai',
            text: 'Here is the output'
          }
        ];
        
        // Start execution (after a small delay to ensure UI is ready)
        setTimeout(() => {
          this.toolExecutionService.startExecution(this.guardrailId!, this.chatMessages);
          
          // Subscribe to execution state changes
          this.executionSubscription = this.toolExecutionService.getExecutionState().subscribe(state => {
            if (state.isExecuting && state.toolId === this.guardrailId) {
              this.chatMessages = state.chatMessages;
            }
          });
        }, 100);
      }
    }
  }
  
  ngOnDestroy(): void {
    // Clean up subscription
    if (this.executionSubscription) {
      this.executionSubscription.unsubscribe();
    }
  }

  onSave(): void {
    console.log('Form data:', this.guardrailForm.value);
    
    // Get the return page if available
    const returnPage = this.route.snapshot.queryParamMap.get('returnPage');
    const pageNumber = returnPage ? parseInt(returnPage) : 1;
    
    if (this.isEditMode) {
      console.log('Updating existing guardrail');
      // this.guardrailService.updateGuardrail(this.guardrailId, this.guardrailForm.value);
      this.router.navigate(['/libraries/guardrails'], {
        queryParams: { page: pageNumber }
      });
    } else {
      console.log('Creating new guardrail');
      // this.guardrailService.createGuardrail(this.guardrailForm.value);
      this.router.navigate(['/libraries/guardrails']);
    }
  }
  
  onExecute(): void {
    console.log('Executing guardrail:', this.guardrailForm.value);
    if (this.guardrailId) {
      // If we're already in execute mode with chat interface showing
      if (this.isExecuteMode && this.showChatInterface) {
        // Process the execution
        console.log('Processing execution');
      } else {
        console.log('Entering execute mode, showing chat interface');
        
        // Set flags to show chat interface
        this.isExecuteMode = true;
        this.showChatInterface = true;
        
        // Set the initial messages
        this.chatMessages = [
          {
            from: 'ai',
            text: 'Hi Akash, this is the guardrail testing'
          },
          {
            from: 'user',
            text: 'Test this input'
          },
          {
            from: 'ai',
            text: 'Here is the output'
          }
        ];
        
        // Delay starting the execution service slightly to allow UI to update
        setTimeout(() => {
          console.log('Starting execution service for guardrail ID:', this.guardrailId);
          this.toolExecutionService.startExecution(this.guardrailId!, this.chatMessages);
        }, 100);
      }
    }
  }

  onExit(): void {
    // If we're in execute mode with chat interface showing
    if (this.isExecuteMode && this.isEditMode) {
      // Return to edit mode without chat interface
      this.isExecuteMode = false;
      this.showChatInterface = false;
      this.toolExecutionService.stopExecution();
      console.log('Exited execution mode, returning to edit mode');
    } else {
      // Get the return page if available
      const returnPage = this.route.snapshot.queryParamMap.get('returnPage');
      const pageNumber = returnPage ? parseInt(returnPage) : 1;
      
      // Exit to guardrails list at correct page
      this.router.navigate(['/libraries/guardrails'], {
        queryParams: { page: pageNumber }
      });
    }
  }
  
  // Helper method to get form controls easily from the template
  getControl(name: string): FormControl {
    return this.guardrailForm.get(name) as FormControl;
  }
  
  // Method to change the selected code format
  setCodeFormat(format: string): void {
    this.selectedCodeFormat = format;
    console.log(`Code format changed to: ${format}`);
  }
  
  // Load guardrail data from mock data
  loadGuardrailData(guardrailId: string): void {
    // In a real app, this would use data fetched from a service
    const guardrail = MOCK_GUARDRAILS.find(g => g.id === guardrailId);
    
    if (guardrail) {
      // Set form values based on the guardrail data
      this.guardrailForm.get('name')?.setValue(guardrail.title);
      this.guardrailForm.get('description')?.setValue(`This is the ${guardrail.title} description.`);
      
      // Set filter data based on the guardrail properties
      this.guardrailForm.get('organization')?.setValue('Ascendion');
      this.guardrailForm.get('domain')?.setValue(guardrail.department || 'AI Safety');
      this.guardrailForm.get('project')?.setValue(guardrail.project || 'Safety Guardrails');
      this.guardrailForm.get('team')?.setValue('AI Safety Team');
      
      // Set code content based on the guardrail type
      // (In a real app, you would have actual code here)
    }
  }
  
  // Handle chat messages
  handleChatMessage(message: string): void {
    if (this.guardrailId && this.isExecuteMode) {
      this.isProcessingChat = true;
      
      // Process through the service - it will handle adding user and AI messages
      this.toolExecutionService.processUserMessage(message);
      
      // Reset loading state after a delay that matches the service's response time
      setTimeout(() => {
        this.isProcessingChat = false;
      }, 1000);
    }
  }
  //Drop Down
    promptOptions: SelectOption[] = [
    { value: 'default', label: 'Choose Prompt' },
    { value: 'ruby-developer', label: 'Senior Ruby Developer' },
    { value: 'python-developer', label: 'Python Developer' },
    { value: 'data-scientist', label: 'Data Scientist' },
    { value: 'frontend-developer', label: 'Frontend Developer' },
  ];
  onPromptChanged(option: SelectOption) {
    console.log('Prompt changed in parent:', option);
    // your logic to handle selected prompt
  }
}