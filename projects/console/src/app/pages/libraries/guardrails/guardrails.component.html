<div class="guardrails-container">
  <!-- Header section -->
  <div class="guardrails-header">
    <div class="header-content">
      <div class="search-section">
        <ava-textbox placeholder="Search..."></ava-textbox>
      </div>
      <div class="action-buttons">
        <ava-dropdown
          dropdownTitle="choose guardrail"
          [options]="guardrailsOptions"
          (selectionChange)="onSelectionChange($event)"
        >
        </ava-dropdown>
      </div>
    </div>
  </div>

  <div class="cards-container">
    <!-- Create Guardrail Card - show only on first page -->
    <ava-text-card
      *ngIf="showCreateCard"
      [type]="'create'"
      [iconName]="'plus'"
      iconColor="#144692"
      title="Create Guardrail"
      (cardClick)="onCreateGuardrail()"
    >
    </ava-text-card>

    <!-- No results message -->
    <div class="no-results" *ngIf="filteredGuardrails.length === 0">
      No guardrails found matching your criteria
    </div>

    <!-- Guardrail Data Cards -->
    <ava-text-card
      *ngFor="let guardrail of displayedGuardrails"
      [type]="'prompt'"
      [title]="guardrail.title"
      [name]="guardrail.name || 'AAVA'"
      [date]="guardrail.createdDate"
      [iconList]="iconList"
      iconColor="#144692"
      [userCount]="120"
      (iconClick)="onIconClicked($event, guardrail.id)"
      (cardClick)="onCardClicked(guardrail.id)"
    >
    </ava-text-card>
  </div>

  <!-- Page Footer with Pagination -->
  <app-page-footer
    *ngIf="filteredGuardrails.length > 0"
    [totalItems]="filteredGuardrails.length + (showCreateCard ? 1 : 0)"
    [currentPage]="currentPage"
    [itemsPerPage]="itemsPerPage"
    (pageChange)="onPageChange($event)"
  >
  </app-page-footer>
</div>
