<div class="prompts-container">
  <!-- Header section -->
  <div class="prompts-header">
    <div class="header-content">
      <div class="search-section">
        <!-- <app-search-bar></app-search-bar> -->
        <ava-textbox placeholder="Search..."></ava-textbox>
      </div>
      <div class="action-buttons">
        <ava-dropdown
          dropdownTitle="choose prompt"
          [options]="simpleOptions"
          (selectionChange)="onSelectionChange($event)"
        >
        </ava-dropdown>
      </div>
    </div>
  </div>

  <!-- Loading State -->
  <div class="loading-container" *ngIf="isLoading">
    <div class="loading-spinner">
      <div class="spinner"></div>
      <p>{{ promptLabels.loadingText }}</p>
    </div>
  </div>

  <div class="cards-container">
    <!-- Create Prompt Card - show only on first page -->
    <ava-text-card
      *ngIf="showCreateCard"
      [type]="'create'"
      [iconName]="'plus'"
      iconColor="#144692"
      [title]="promptLabels.createPrompt"
      (cardClick)="onCreatePrompt()"
    >
    </ava-text-card>

    <!-- No results message -->
    <div class="no-results" *ngIf="filteredPrompts.length === 0">
      {{ promptLabels.noResults }}
    </div>

    <!-- Prompt Data Cards - Now showing paginated cards -->
    <ava-text-card
      *ngFor="let prompt of displayedPrompts"
      [title]="prompt.title"
      [description]="prompt.tagSummary ?? ''"
      [iconName]="'plus'"
      [type]="'prompt'"
      [name]="prompt.name || 'Ava'"
      [date]="prompt.createdDate"
      [iconList]="iconList"
      iconColor="#144692"
      [userCount]="120"
      (iconClick)="onIconClicked($event, prompt.id)"
      (cardClick)="onCardClicked(prompt.id)"
    >
    </ava-text-card>
  </div>

  <!-- Page Footer with Pagination - use totalPages from the service -->
  <app-page-footer
    *ngIf="filteredPrompts.length > 0"
    [totalItems]="filteredPrompts.length + 1"
    [currentPage]="currentPage"
    [itemsPerPage]="itemsPerPage"
    (pageChange)="onPageChange($event)"
  ></app-page-footer>
</div>
