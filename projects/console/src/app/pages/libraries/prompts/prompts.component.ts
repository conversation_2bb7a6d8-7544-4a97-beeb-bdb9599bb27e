import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, ActivatedRoute } from '@angular/router';
import { takeUntil, of, map, catchError, Subject } from 'rxjs';
import SearchBar from '../../../shared/components/search-bar/search-bar.component';
import { CreateCardComponent } from '../../../shared/components/create-card/create-card.component';
import { DataCardComponent } from '../../../shared/components/data-card/data-card.component';
import {
  CardAction,
  CardData,
  CardTag,
} from '../../../shared/models/card.model';
//import { MOCK_PROMPTS } from '../../../shared/mock-data/prompt-mock-data';
import { FilterBarComponent } from '../../../shared/components/filter-bar/filter-bar.component';
import { FilterService } from '../../../shared/services/filter.service';
import { FilterConfig } from '../../../shared/models/filter.model';
import { PageFooterComponent } from '../../../shared/components/page-footer/page-footer.component';
import { PaginationService } from '../../../shared/services/pagination.service';
import { PromptsService } from '../../../shared/services/prompts.service';
import { formatToDisplayDate } from '../../../shared/utils/date-utils';
import promptsLabels from './constants/prompts.json';
import { PROMPTS_BASE_ACTIONS } from './prompts-actions';
import { AvaTextboxComponent, TextCardComponent,DropdownComponent, DropdownOption } from '@ava/play-comp-library';
import { LucideAngularModule } from 'lucide-angular';

@Component({
  selector: 'app-prompts',
  standalone: true,
  imports: [
    CommonModule,
    PageFooterComponent,
    TextCardComponent,
    AvaTextboxComponent,
    DropdownComponent,
    LucideAngularModule,
  ],
  templateUrl: './prompts.component.html',
  styleUrl: './prompts.component.scss',
})
export class PromptsComponent implements OnInit, OnDestroy {
  // Labels used across the Prompt UI components (titles)
  public promptLabels = promptsLabels.labels;
  allPrompts: CardData[] = [];
  filteredPrompts: CardData[] = [];
  displayedPrompts: CardData[] = [];
  promptFilterConfig!: FilterConfig;

  // Track filter bar visibility
  isFilterBarVisible: boolean = false;

  // Controls loading spinner

  // Pagination
  currentPage: number = 1;
  itemsPerPage: number = 12; // Show 12 cards per page
  totalPages: number = 1;

  // Map filter IDs to CardData properties
  private filterPropertyMap: { [key: string]: string } = {
    organization: 'client',
    domain: 'department',
    project: 'role',
    team: 'project',
    relevance: 'createdDate',
  };

  // Used to clean up subscriptions
  protected destroy$ = new Subject<void>();
  selectedData: any = null;
  constructor(
    private filterService: FilterService,
    private paginationService: PaginationService,
    private promptsService: PromptsService,
    private router: Router,
    private route: ActivatedRoute,
  ) {}

  ngOnInit(): void {
    // Get the filter configuration for prompts
    this.promptFilterConfig = this.filterService.getFilterConfig('prompts');
    //this.filteredPrompts = [...this.allPrompts];
    this.isLoading = true; // Start loading

    this.promptsService
      .fetchAllPrompts()
      .pipe(
        takeUntil(this.destroy$),
        map((response: CardData[] | { prompts: CardData[] }) => {
          const prompts = 'prompts' in response ? response.prompts : response;

          return {
            prompts: prompts.map((item: any) => {
              const {
                name,
                updatedAt,
                categoryName,
                domainName,
                tags = [],
                ...rest
              } = item;

              const customTags: CardTag[] = [];
              if (categoryName) {
                customTags.push({ label: categoryName, type: 'primary' });
              }
              if (domainName) {
                customTags.push({ label: domainName, type: 'secondary' });
              }

              const allTags: CardTag[] = [...tags, ...customTags];
              const tagSummary: string = allTags
                .map((tag) => tag.label)
                .join(', ');
              const formattedDate = formatToDisplayDate(updatedAt);
              const actions: CardAction[] = PROMPTS_BASE_ACTIONS;

              return {
                title: name,
                createdDate: formattedDate,
                tags: allTags,
                tagSummary,
                actions,
                ...rest,
              } as CardData & { tagSummary: string };
            }),
          };
        }),
        catchError((error) => {
          console.error('Error fetching prompts:', error);
          this.isLoading = false;
          return of({ prompts: [] }); // Fallback data
        }),
      )
      .subscribe({
        next: (res) => {
          this.allPrompts = res.prompts;
          this.filteredPrompts = [...this.allPrompts];
          this.updateDisplayedPrompts();
          const pageParam = this.route.snapshot.queryParamMap.get('page');
          if (pageParam) {
            this.currentPage = parseInt(pageParam, 10);
          }
        },
        error: (err) => {
          console.error('Subscription error:', err.message);
        },
        complete: () => {
          this.isLoading = false; // End loading regardless of success or error
          console.log('Prompt fetch complete');
        },
      });
  }

  updateDisplayedPrompts(): void {
    // Use the pagination service to get displayed items and total pages
    const result = this.paginationService.getPaginatedItems(
      this.filteredPrompts,
      this.currentPage,
      this.itemsPerPage,
    );

    this.displayedPrompts = result.displayedItems;
    this.totalPages = result.totalPages;
  }

  onCreatePrompt(): void {
    console.log('Create Prompt clicked');
    this.router.navigate(['/libraries/prompts/create']);
  }

  onCardClicked(promptId: string): void {
    console.log(`Prompt card clicked: ${promptId}`);
    this.router.navigate(['/libraries/prompts/edit', promptId], {
      queryParams: { returnPage: this.currentPage },
    });
  }

  onActionClicked(event: { action: string; cardId: string }): void {
    console.log(`Action ${event.action} clicked for prompt: ${event.cardId}`);

    // Handle different actions
    if (event.action === 'execute') {
      console.log(`Executing prompt: ${event.cardId}`);
      this.router.navigate(['/libraries/prompts/edit', event.cardId], {
        queryParams: { execute: 'true', returnPage: this.currentPage },
      });
    } else if (event.action === 'delete') {
      console.log(`Deleting prompt: ${event.cardId}`);
      // Implement delete functionality
    }
  }

  toggleFilterBar(): void {
    this.isFilterBarVisible = !this.isFilterBarVisible;
    console.log('Filter bar visibility:', this.isFilterBarVisible);
  }

  onFilterChange(filters: { [key: string]: string }): void {
    // Apply filters to prompts
    if (Object.keys(filters).length === 0) {
      this.filteredPrompts = [...this.allPrompts];
    } else {
      this.filteredPrompts = this.filterService.filterData(
        this.allPrompts,
        filters,
        this.filterPropertyMap,
      );
    }

    // Reset to first page when filters change
    this.currentPage = 1;
    this.updateDisplayedPrompts();

    console.log('Applied filters:', filters);
    console.log('Filtered prompts count:', this.filteredPrompts.length);
  }

  onPageChange(page: number): void {
    this.currentPage = page;
    this.updateDisplayedPrompts();
    console.log('Page changed to:', this.currentPage);

    // Update URL with page parameter
    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: { page: this.currentPage },
      queryParamsHandling: 'merge',
    });
  }

  // Helper to check if we should show the create card (only on first page)
  get showCreateCard(): boolean {
    return this.currentPage === 1;
  }

  // Cleanup: notify subscribers and complete the destroy$ subject
  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  //icon list

  public isLoading: boolean = false;
  trackByPrompt(index: number, prompt: CardData): string {
    return prompt.id;
  }

  iconList = [
    { name: 'archive', iconName: 'archive', cursor: true },
    { name: 'trash', iconName: 'trash-2', cursor: true },
    { name: 'copy', iconName: 'copy', cursor: true },
    { name: 'play', iconName: 'play', cursor: true },
  ];

  iconClick(event: any) {
    console.log(event);
  }

  onIconClicked(icon: any, promptId: string): void {
    console.log('Clicked icon:', icon);
    console.log('From prompt ID:', promptId);

    switch (icon.name) {
      case 'trash':
        this.deletePrompt(promptId);
        break;
      case 'play':
        this.executePrompt(promptId);
        break;
      case 'copy':
        this.copyPrompt(promptId);
        break;
      default:
        console.log('Unknown icon action:', icon.name);
    }
  }

  deletePrompt(promptId: string): void {
    console.log(`Delete prompt ${promptId}`);
  }

  executePrompt(promptId: string): void {
    console.log(`Execute prompt ${promptId}`);
    this.router.navigate(['/libraries/prompts/edit', promptId], {
      queryParams: { execute: 'true', returnPage: this.currentPage },
    });
  }

  copyPrompt(promptId: string): void {
    console.log(`Copy prompt ${promptId}`);
  }

  //drop down value 
  simpleOptions: DropdownOption[] = [
    { name: 'Option 1', value: '1' },
    { name: 'Option 2', value: '2' },
    { name: 'Option 3', value: '3' },
    { name: 'Option 4', value: '4' },
    { name: 'Option 5', value: '5' },
  ];

  onSelectionChange(data: any) {
    this.selectedData = data;
    console.log('Selection changed:', data);
  }
}
