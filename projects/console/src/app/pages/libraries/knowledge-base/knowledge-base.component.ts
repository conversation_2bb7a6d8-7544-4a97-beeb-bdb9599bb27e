import { Component, OnDestroy, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { of, map, catchError, Subject, takeUntil } from 'rxjs';
import { PageFooterComponent } from '../../../shared/components/page-footer/page-footer.component';
import { PaginationService } from '../../../shared/services/pagination.service';
import { KnowledgeBaseService } from '../../../shared/services/knowledge-base.service';
import {
  AvaTextboxComponent,
  DropdownComponent,
  DropdownOption,
  TextCardComponent,
} from '@ava/play-comp-library';
import { LucideAngularModule } from 'lucide-angular';
import knowledgeBaseLabels from '../knowledge-base/constants/knowledge-base.json';

@Component({
  selector: 'app-knowledge-base',
  standalone: true,
  imports: [
    CommonModule,
    PageFooterComponent,
    TextCardComponent,
    AvaTextboxComponent,
    DropdownComponent,
    LucideAngularModule,
  ],
  templateUrl: './knowledge-base.component.html',
  styleUrls: ['./knowledge-base.component.scss'],
})
export class KnowledgeBaseComponent implements OnInit, OnDestroy {
  public kbLabels = knowledgeBaseLabels.labels;
  allKnowledgeBase: any[] = [];
  filteredKnowledgeBase: any[] = [];
  displayedKnowledgeBase: any[] = [];
  isLoading: boolean = false;
  error: string | null = null;
  currentPage: number = 1;
  itemsPerPage: number = 12;
  totalPages: number = 1;
  private destroy$ = new Subject<void>();

  knowledgeBaseOptions: DropdownOption[] = [
    { name: 'All', value: 'all' },
    { name: 'Type A', value: 'typeA' },
    { name: 'Type B', value: 'typeB' },
  ];
  selectedData: any = null;

  iconList = [
    { name: 'archive', iconName: 'archive', cursor: true },
    { name: 'trash', iconName: 'trash-2', cursor: true },
    { name: 'copy', iconName: 'copy', cursor: true },
    { name: 'play', iconName: 'play', cursor: true },
  ];

  constructor(
    private knowledgeBaseService: KnowledgeBaseService,
    private paginationService: PaginationService,
    private router: Router,
  ) {}

  ngOnInit(): void {
    this.fetchAllKnowledge();
  }

  fetchAllKnowledge() {
    this.isLoading = true;
    this.knowledgeBaseService
      .fetchAllKnowledge()
      .pipe(
        map((response: any[]) => {
          return response.map((item: any) => ({
            ...item,
            title: item.collectionName,
            createdDate: item.createdDate,
            userCount: item.userCount || 0,
          }));
        }),
        catchError((error) => {
          this.isLoading = false;
          return of([]);
        }),
        takeUntil(this.destroy$),
      )
      .subscribe({
        next: (res) => {
          this.allKnowledgeBase = res;
          this.filteredKnowledgeBase = [...this.allKnowledgeBase];
          this.updateDisplayedKnowledgeBase();
          this.isLoading = false;
        },
        error: (err) => {
          this.isLoading = false;
        },
      });
  }

  updateDisplayedKnowledgeBase(): void {
    const paginationResult = this.paginationService.getPaginatedItems(
      this.filteredKnowledgeBase,
      this.currentPage,
      this.itemsPerPage,
    );
    this.displayedKnowledgeBase = paginationResult.displayedItems;
    console.log(this.displayedKnowledgeBase)
    this.totalPages = paginationResult.totalPages;
  }

  onCreateKnowledgeBase(): void {
    this.router.navigate(['/libraries/knowledge-base/create']);
  }

  onCardClicked(knowledgeBaseId: string): void {
    // Navigate to knowledge base details page
    this.router.navigate([`/libraries/knowledge-base/edit/${knowledgeBaseId}`]);
  }

  onIconClicked(icon: any, knowledgeBaseId: string): void {
    switch (icon.name) {
      case 'trash':
        this.deleteKnowledgeBase(knowledgeBaseId);
        break;
      case 'edit':
        this.router.navigate([
          `/libraries/knowledge-base/edit/${knowledgeBaseId}`,
        ]);
        break;
      case 'copy':
        this.duplicateKnowledgeBase(knowledgeBaseId);
        break;
      default:
        break;
    }
  }

  deleteKnowledgeBase(knowledgeBaseId: string): void {
    // Implement delete logic
  }

  duplicateKnowledgeBase(knowledgeBaseId: string): void {
    // Implement duplicate logic
  }

  onSelectionChange(data: any) {
    this.selectedData = data;
    // Implement filter logic if needed
  }

  onPageChange(page: number): void {
    this.currentPage = page;
    this.updateDisplayedKnowledgeBase();
  }

  get showCreateCard(): boolean {
    return this.currentPage === 1 && !this.isLoading && !this.error;
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
