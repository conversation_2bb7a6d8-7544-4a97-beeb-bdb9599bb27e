<div class="knowledge-base-container">
  <!-- Header section -->
  <div class="knowledge-base-header">
    <div class="header-content">
      <div class="search-section">
        <ava-textbox placeholder="Search..."></ava-textbox>
      </div>
      <div class="action-buttons">
        <ava-dropdown
          dropdownTitle="choose knowledge base"
          [options]="knowledgeBaseOptions"
          (selectionChange)="onSelectionChange($event)"
        >
        </ava-dropdown>
      </div>
    </div>
  </div>

  <!-- Loading State -->
  <div class="loading-container" *ngIf="isLoading">
    <div class="loading-spinner">
      <div class="spinner"></div>
      <p>{{ kbLabels.loadingText }}</p>
    </div>
  </div>

  <div class="cards-container">
    <!-- Create Knowledge Base Card -->
    <ava-text-card
      *ngIf="showCreateCard"
      [type]="'create'"
      [iconName]="'plus'"
      iconColor="#144692"
      [title]="kbLabels.createKnowledgeBase"
      (cardClick)="onCreateKnowledgeBase()"
    >
    </ava-text-card>

    <!-- No results message -->
    <div class="no-results" *ngIf="filteredKnowledgeBase.length === 0">
      {{ kbLabels.noResults }}
    </div>

    <!-- Knowledge Base Data Cards -->
    <ava-text-card
      *ngFor="let knowledgeBase of displayedKnowledgeBase"
      [title]="knowledgeBase.title"
      [type]="'prompt'"
      [name]="knowledgeBase.name || 'AAVA'"
      [date]="knowledgeBase.createdDate"
      [iconList]="iconList"
      iconColor="#144692"
      [userCount]="120"
      (iconClick)="onIconClicked($event, knowledgeBase.id)"
      (cardClick)="onCardClicked(knowledgeBase.id)"
    >
    </ava-text-card>
  </div>

  <!-- Page Footer with Pagination -->
  <app-page-footer
    *ngIf="filteredKnowledgeBase.length > 0"
    [totalItems]="filteredKnowledgeBase.length + (showCreateCard ? 1 : 0)"
    [currentPage]="currentPage"
    [itemsPerPage]="itemsPerPage"
    (pageChange)="onPageChange($event)"
  >
  </app-page-footer>
</div>
