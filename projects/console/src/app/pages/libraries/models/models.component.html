<div class="models-container">
  <!-- Header section -->
  <div class="models-header">
    <div class="header-content">
      <div class="search-section">
        <ava-textbox placeholder="Search..."></ava-textbox>
      </div>
      <div class="action-buttons">
        <ava-dropdown
          dropdownTitle="choose model"
          [options]="modelOptions"
          (selectionChange)="onSelectionChange($event)"
        >
        </ava-dropdown>
      </div>
    </div>
  </div>

  <!-- Loading State -->
  <div class="loading-container" *ngIf="isLoading">
    <div class="loading-spinner">
      <div class="spinner"></div>
      <p>Loading models...</p>
    </div>
  </div>

  <div class="cards-container">
    <!-- Create Model Card - show only on first page -->
    <ava-text-card
      *ngIf="showCreateCard"
      [type]="'create'"
      [iconName]="'plus'"
      iconColor="#144692"
      title="Create Model"
      (cardClick)="onCreateModel()"
    >
    </ava-text-card>

    <!-- No results message -->
    <div class="no-results" *ngIf="filteredModels.length === 0">
      No models found.
    </div>

    <!-- Model Data Cards -->
    <ava-text-card
      *ngFor="let model of displayedModels"
      [title]="model.title"
      [iconName]="'plus'"
      [type]="'prompt'"
      [name]="model.name || 'Ava'"
      [date]="model.createdDate"
      [iconList]="iconList"
      iconColor="#144692"
      [userCount]="120"
      (iconClick)="onIconClicked($event, model.id)"
      (cardClick)="onCardClicked(model.id)"
    >
    </ava-text-card>
  </div>

  <!-- Pagination -->
  <app-page-footer
    *ngIf="filteredModels.length > 0"
    [totalItems]="filteredModels.length + 1"
    [currentPage]="currentPage"
    [itemsPerPage]="itemsPerPage"
    (pageChange)="onPageChange($event)"
  >
  </app-page-footer>
</div>
