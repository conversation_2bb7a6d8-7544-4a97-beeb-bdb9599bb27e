import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { Subject, takeUntil } from 'rxjs';
import { CardData, Model } from '../../../shared/models/card.model';
import { FilterService } from '../../../shared/services/filter.service';
import { FilterConfig } from '../../../shared/models/filter.model';
import { PageFooterComponent } from '../../../shared/components/page-footer/page-footer.component';
import { PaginationService } from '../../../shared/services/pagination.service';
import { ModelService } from '../../../shared/services/model.service';
import {
  AvaTextboxComponent,
  DropdownComponent,
  DropdownOption,
  TextCardComponent,
} from '@ava/play-comp-library';
import { LucideAngularModule } from 'lucide-angular';

@Component({
  selector: 'app-models',
  standalone: true,
  imports: [
    CommonModule,
    PageFooterComponent,
    TextCardComponent,
    AvaTextboxComponent,
    DropdownComponent,
    LucideAngularModule,
  ],
  templateUrl: './models.component.html',
  styleUrl: './models.component.scss',
})
export class ModelsComponent implements OnInit, OnDestroy {
  allModels: CardData[] = [];
  filteredModels: CardData[] = [];
  displayedModels: CardData[] = [];
  modelFilterConfig!: FilterConfig;
  isFilterBarVisible: boolean = false;
  isLoading: boolean = false;
  error: string | null = null;
  currentPage: number = 1;
  itemsPerPage: number = 12;
  totalPages: number = 1;
  private destroy$ = new Subject<void>();
  private filterPropertyMap: { [key: string]: string } = {
    modelType: 'userType',
    provider: 'client',
    category: 'department',
    capability: 'role',
    series: 'project',
    license: 'category',
  };
  selectedData = null;

  modelOptions: DropdownOption[] = [
    { name: 'Option 1', value: '1' },
    { name: 'Option 2', value: '2' },
    { name: 'Option 3', value: '3' },
    { name: 'Option 4', value: '4' },
    { name: 'Option 5', value: '5' },
  ];

  iconList = [
    { name: 'archive', iconName: 'archive', cursor: true },
    { name: 'trash', iconName: 'trash-2', cursor: true },
    { name: 'copy', iconName: 'copy', cursor: true },
    { name: 'play', iconName: 'play', cursor: true },
  ];
  constructor(
    private filterService: FilterService,
    private paginationService: PaginationService,
    private router: Router,
    private modelService: ModelService,
  ) {}

  ngOnInit(): void {
    this.modelFilterConfig = this.filterService.getFilterConfig('models');
    this.fetchModels();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private transformModelsToCardData(models: Model[]): CardData[] {
    return models.map((model) => {
      const modelAny = model as any;

      return {
        id: String(modelAny.id || ''),
        title:
          modelAny.modelDeploymentName || modelAny.model || 'Unnamed Model',
        description: modelAny.modelDescription || '',
        tags: [
          { label: `Model Name: ${modelAny.model}`, type: 'primary' },
          { label: `Model Type: ${modelAny.modelType}`, type: 'primary' },
          { label: `AI Engine: ${modelAny.aiEngine}`, type: 'primary' },
        ],
        createdDate: modelAny.date || new Date().toISOString(),
        updatedDate: modelAny.date || new Date().toISOString(),
        userType: modelAny.modelType || '',
        client: modelAny.aiEngine || '',
        department: '',
        role: '',
        project: modelAny.model || '',
        category: modelAny.modelType || '',
        model: modelAny.model,
        modelType: modelAny.modelType,
        aiEngine: modelAny.aiEngine,
        status: 'active',
      } as CardData;
    });
  }

  onIconClicked(icon: any, promptId: string): void {
    console.log('Clicked icon:', icon);
    console.log('From prompt ID:', promptId);

    switch (icon.name) {
      case 'trash':
        this.deletePrompt(promptId);
        break;
      case 'play':
        this.executePrompt(promptId);
        break;
      case 'copy':
        this.copyPrompt(promptId);
        break;
      default:
        console.log('Unknown icon action:', icon.name);
    }
  }

  deletePrompt(promptId: string): void {
    console.log(`Delete prompt ${promptId}`);
  }

  executePrompt(promptId: string): void {
    console.log(`Execute prompt ${promptId}`);
    this.router.navigate(['/libraries/prompts/edit', promptId], {
      queryParams: { execute: 'true', returnPage: this.currentPage },
    });
  }

  copyPrompt(promptId: string): void {
    console.log(`Copy prompt ${promptId}`);
  }

  onSelectionChange(data: any) {
    this.selectedData = data;
    console.log('Selection changed:', data);
  }

  fetchSingleModel(modelId: string): void {
    console.log(`=== FETCHING SINGLE MODEL ===`);
    console.log(`Model ID: ${modelId}`);
    console.log(
      `Service method exists:`,
      typeof this.modelService.getOneModeById === 'function',
    );

    this.modelService
      .getOneModeById(modelId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (model: any) => {
          console.log('=== SINGLE MODEL SUCCESS ===');
          console.log('Single model fetched successfully:', model);

          if (model) {
            console.log('Model details:', model);
            console.log('Model Deployment Name:', model.modelDeploymentName);
            console.log('Model ID:', model.id);
          } else {
            console.log('No model data received');
          }
        },
        error: (error: any) => {
          console.log('=== SINGLE MODEL ERROR ===');
          console.error('Error fetching single model:', error);
          console.error('Error details:', error.message);
        },
      });
  }

  fetchModels(): void {
    this.isLoading = true;
    this.error = null;

    this.modelService
      .getAllModelList()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (models) => {
          console.log('Models fetched successfully:', models);
          this.allModels = this.transformModelsToCardData(models || []);
          this.filteredModels = [...this.allModels];
          this.updateDisplayedModels();
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error fetching models:', error);
          this.error = error.message || 'Failed to load models';
          this.isLoading = false;
        },
      });
  }

  retryFetch(): void {
    this.fetchModels();
  }

  updateDisplayedModels(): void {
    const result = this.paginationService.getPaginatedItems(
      this.filteredModels,
      this.currentPage,
      this.itemsPerPage,
    );

    this.displayedModels = result.displayedItems;
    console.log(this.displayedModels);
    this.totalPages = result.totalPages;
  }

  onCreateModel(): void {
    console.log('Create Model clicked');
    this.router.navigate(['/libraries/models/create']);
  }

  onCardClicked(modelId: string): void {
    console.log(`Model card clicked: ${modelId}`);
    console.log('About to fetch single model...');
    this.fetchSingleModel(modelId);
    setTimeout(() => {
      this.router.navigate([`/libraries/models/edit/${modelId}`]);
    }, 1000);
  }
  onActionClicked(event: { action: string; cardId: string }): void {
    console.log(`Action ${event.action} clicked for model: ${event.cardId}`);

    switch (event.action) {
      case 'edit':
        this.router.navigate([`/libraries/models/edit/${event.cardId}`]);
        break;
      case 'delete':
        this.confirmDeleteModel(event.cardId);
        break;
      case 'duplicate':
        this.duplicateModel(event.cardId);
        break;
      default:
        console.warn(`Unknown action: ${event.action}`);
    }
  }

  toggleFilterBar(): void {
    this.isFilterBarVisible = !this.isFilterBarVisible;
    console.log('Filter bar visibility:', this.isFilterBarVisible);
  }

  onFilterChange(filters: { [key: string]: string }): void {
    console.log('Applied filters:', filters);

    if (Object.keys(filters).length === 0) {
      this.filteredModels = [...this.allModels];
    } else {
      this.filteredModels = this.filterService.filterData(
        this.allModels,
        filters,
        this.filterPropertyMap,
      );
    }
    this.currentPage = 1;
    this.updateDisplayedModels();

    console.log('Filtered models count:', this.filteredModels.length);
  }

  onPageChange(page: number): void {
    this.currentPage = page;
    this.updateDisplayedModels();
  }

  get showCreateCard(): boolean {
    return this.currentPage === 1 && !this.isLoading && !this.error;
  }

  private confirmDeleteModel(modelId: string): void {
    const model = this.allModels.find((m) => m.id === modelId);
    if (model && confirm(`Are you sure you want to delete "${model.title}"?`)) {
      console.log(`Deleting model: ${modelId}`);
    }
  }

  private duplicateModel(modelId: string): void {
    const model = this.allModels.find((m) => m.id === modelId);
    if (model) {
      console.log(`Duplicating model: ${model.title}`);
    }
  }
}
