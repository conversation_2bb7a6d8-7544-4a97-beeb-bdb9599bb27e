import { Component, <PERSON><PERSON>nit, <PERSON><PERSON><PERSON>roy, EventEmitter, Output,ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, FormControl} from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { CardComponent } from '../../../../shared/components/card/card.component';
import { FormFieldComponent } from '../../../../shared/components/form-field/form-field.component';
import { ChatInterfaceComponent } from '../../../../shared/components/chat-interface/chat-interface.component';
import { ChatMessage } from '../../../../shared/components/chat-window/chat-window.component';
import { ToolExecutionService } from '../../../../shared/services/tool-execution/tool-execution.service';
import { ToolsService } from '../../../../shared/services/tools.service';
import { Subscription } from 'rxjs';
import { PromptEnhanceService } from '../../../../shared/services/prompt-enhance.service';
import { CodeEditorComponent, CodeLanguage, CodeEditorTheme, EditorActionButton } from '../../../../shared/components/code-editor/code-editor.component';
import toolsText from '../constants/tools.json';
import { PlaygroundComponent } from 'projects/console/src/app/shared/components/playground/playground.component';
import { SelectOption } from 'projects/console/src/app/shared/components/select-dropdown/select-dropdown.component';
import { AvaTextareaComponent, AvaTextboxComponent, ButtonComponent } from '@ava/play-comp-library'

interface ExtractedParameter {
  name: string;
  type: string;
}

interface Tool {
  id: number;
  name: string;
}

interface ParameterCollectionState {
  parameters: ExtractedParameter[];
  currentParameterIndex: number;
  collectedInputs: { [key: string]: any };
  isCollecting: boolean;
}



@Component({
  selector: 'app-create-tools',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    CardComponent,
    FormFieldComponent,
    AvaTextboxComponent,
    AvaTextareaComponent,
    ButtonComponent,
    //AvaTextareaComponent,
    PlaygroundComponent,
    CodeEditorComponent,
  ],
  templateUrl: './create-tools.component.html',
  styleUrls: ['./create-tools.component.scss'],
})
export class CreateToolsComponent implements OnInit, OnDestroy {
  @ViewChild(CodeEditorComponent) codeEditor!: CodeEditorComponent;

  toolId: number | null = null;
  isEditMode: boolean = false;
  isExecuteMode: boolean = true;
  showChatInterface: boolean = true;
  selectedTool: string | null = null;
  toolForm: FormGroup;
  chatMessages: ChatMessage[] = [];
  isProcessingChat: boolean = false;

  parameterState: ParameterCollectionState = {
    parameters: [],
    currentParameterIndex: 0,
    collectedInputs: {},
    isCollecting: false,
  };


  private executionSubscription: Subscription = new Subscription();
  private waitingForRestartConfirmation: boolean = false;
  public placeholder: any = toolsText.TOOL_PLACEHOLDER.toolClassDef;
  public labels: any = toolsText.labels;
  @Output() promptChange = new EventEmitter<string>();
  public validationOutput: string = '';
  public showValidationOutput: boolean = false;
  public validationOutputEditorConfig = {
    title: 'Tool Compiler',
    language: 'json' as CodeLanguage,
    theme: 'light' as CodeEditorTheme,
    readOnly: true,
    height: '250px',
  };

  public editorActions: EditorActionButton[] = [
    { label: 'Select All', style: 'secondary', customClass: '', icon: '' },
    { label: 'Reset', style: 'secondary', customClass: '', icon: '' }
  ];

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private route: ActivatedRoute,
    private toolExecutionService: ToolExecutionService,
    private toolsService: ToolsService,
    private promptGenerateService: PromptEnhanceService,
  ) {
    this.toolForm = this.fb.group({
      name: [''],
      description: [''],
      toolClassName: [''],
      classDefinition: [''],
    });
  }

  ngOnInit(): void {
    const toolIdParam = this.route.snapshot.paramMap.get('id');
    const executeParam = this.route.snapshot.queryParamMap.get('execute');
    this.toolId = toolIdParam ? parseInt(toolIdParam, 10) : null;
    this.isEditMode = !!this.toolId;
    this.isExecuteMode = executeParam === 'true';

    if (this.isEditMode && this.toolId) {
      console.log(`Editing tool with ID: ${this.toolId}`);
      this.loadToolData(this.toolId);
      if (this.isExecuteMode) {
        this.initializeChatMessages();
      }
    }

    // Window resize will be handled by HostListener
  }

  ngOnDestroy(): void {
    if (this.executionSubscription) {
      this.executionSubscription.unsubscribe();
    }
  }

  private initializeChatMessages(): void {
    this.chatMessages = [
      {
        from: 'ai',
        text: "Hi! Welcome to the tool testing playground. I'll help you test your tool by collecting the required parameters.",
      },
    ];
  }

  onSave(): void {
    const toolData = {
      toolName: this.toolForm.get('name')?.value,
      toolClassName: this.toolForm.get('toolClassName')?.value,
      toolDescription: this.toolForm.get('description')?.value,
      toolClassDef: this.toolForm.get('classDefinition')?.value,
      createdBy: '<EMAIL>',
    };

    const returnPage = this.route.snapshot.queryParamMap.get('returnPage');
    const pageNumber = returnPage ? parseInt(returnPage) : 1;

    if (this.isEditMode && this.toolId) {
      const updateData = { ...toolData, toolId: this.toolId };

      this.toolsService.updateUserTool(updateData).subscribe(
        (response) => {
          this.router.navigate(['/libraries/tools'], {
            queryParams: { page: pageNumber },
          });
        },
        (error) => {
          console.error('Error updating tool', error);
        },
      );
    } else {
      console.log('Creating new tool');
      this.toolsService.addNewUserTool(toolData).subscribe(
        (response) => {
          this.router.navigate(['/libraries/tools']);
        },
        (error) => {
          console.error('Error creating tool', error);
        },
      );
    }
  }

  onExecute(): void {
    console.log('Executing tool:', this.toolForm.value);
    if (this.toolId) {
      if (this.isExecuteMode && this.showChatInterface) {
        this.extractParameters();
      } else {
        console.log('Entering execute mode, showing chat interface');
        this.isExecuteMode = true;
        this.showChatInterface = true;
        this.initializeChatMessages();
        setTimeout(() => {
          this.extractParameters();
        }, 500);
      }
    }
  }

  private extractParameters(): void {
    const toolClassDef = this.toolForm.get('classDefinition')?.value;
    const toolClassName = this.toolForm.get('toolClassName')?.value;
    const toolName = this.toolForm.get('name')?.value;
    if (!toolClassDef || !toolClassName) {
      this.addChatMessage(
        'ai',
        'Error: Tool class definition or class name is missing.',
      );
      return;
    }
    const useCase = 'PARAMETER_EXTRACTOR';
    const useCaseIdentifier =
      'PARAMETER_EXTRACTOR@ADD@GOPAL@TEST_GOPAL@GOPALTEST';

    this.isProcessingChat = true;
    this.addChatMessage('ai', 'Analyzing your tool to extract parameters...');

    this.promptGenerateService
      .modelApi(toolClassDef, useCase, false, useCaseIdentifier)
      .subscribe({
        next: (response: any) => {
          console.log('Parameter extraction response:', response);
          try {
            const parametersText = response?.response?.choices?.[0]?.text;
            if (!parametersText) {
              throw new Error('No parameters found in response');
            }
            const parametersObj = JSON.parse(parametersText);
            const parameters: ExtractedParameter[] = Object.keys(
              parametersObj,
            ).map((key) => ({
              name: key,
              type: parametersObj[key],
            }));

            if (parameters.length === 0) {
              this.isProcessingChat = false;
              this.addChatMessage(
                'ai',
                'No parameters found for this tool. The tool might not require any input parameters.',
              );
              return;
            }

            this.parameterState = {
              parameters: parameters,
              currentParameterIndex: 0,
              collectedInputs: {},
              isCollecting: true,
            };

            this.isProcessingChat = false;
            this.promptForNextParameter();
          } catch (error) {
            console.error('Error parsing parameters:', error);
            this.isProcessingChat = false;
            this.addChatMessage(
              'ai',
              'Error: Failed to parse extracted parameters. Please check your tool definition.',
            );
          }
        },
        error: (error) => {
          console.error('Parameter extraction error', error);
          this.isProcessingChat = false;
          this.addChatMessage(
            'ai',
            'Error: Failed to extract parameters from your tool. Please check your tool definition.',
          );
        },
      });
  }

  private promptForNextParameter(): void {
    if (
      this.parameterState.currentParameterIndex <
      this.parameterState.parameters.length
    ) {
      const currentParam =
        this.parameterState.parameters[
          this.parameterState.currentParameterIndex
        ];
      const message = `Please enter input for parameter "${currentParam.name}" (type: ${currentParam.type}):`;
      this.addChatMessage('ai', message);
    }
  }

  private addChatMessage(from: 'ai' | 'user', text: string): void {
    this.chatMessages = [...this.chatMessages, { from, text }];
  }

  handleChatMessage(message: string): void {
    if (!this.isExecuteMode) return;
    this.addChatMessage('user', message);

    if (this.waitingForRestartConfirmation) {
      if (message.toLowerCase().trim() === 'yes') {
        this.waitingForRestartConfirmation = false;
        this.resetParameterState();
        this.addChatMessage(
          'ai',
          'Restarting the parameter collection process...',
        );
        this.extractParameters();
      } else {
        this.waitingForRestartConfirmation = false;
        this.addChatMessage(
          'ai',
          'Okay, feel free to ask if you need any further assistance.',
        );
      }
    } else if (this.parameterState.isCollecting) {
      this.handleParameterInput(message);
    }
  }

  private handleParameterInput(input: string): void {
    const currentParam =
      this.parameterState.parameters[this.parameterState.currentParameterIndex];
    let processedInput: any = input;

    try {
      switch (currentParam.type) {
        case 'number':
          processedInput = parseFloat(input);
          if (isNaN(processedInput)) {
            this.addChatMessage(
              'ai',
              `Invalid number format. Please enter a valid number for "${currentParam.name}":`,
            );
            return;
          }
          break;
        case 'boolean':
          const lowerInput = input.toLowerCase();
          if (
            lowerInput === 'true' ||
            lowerInput === '1' ||
            lowerInput === 'yes'
          ) {
            processedInput = true;
          } else if (
            lowerInput === 'false' ||
            lowerInput === '0' ||
            lowerInput === 'no'
          ) {
            processedInput = false;
          } else {
            this.addChatMessage(
              'ai',
              `Invalid boolean format. Please enter true/false for "${currentParam.name}":`,
            );
            return;
          }
          break;
        case 'object':
          try {
            processedInput = JSON.parse(input);
          } catch {
            this.addChatMessage(
              'ai',
              `Invalid JSON format. Please enter a valid JSON object for "${currentParam.name}":`,
            );
            return;
          }
          break;
        case 'array':
          try {
            processedInput = JSON.parse(input);
            if (!Array.isArray(processedInput)) {
              throw new Error('Not an array');
            }
          } catch {
            this.addChatMessage(
              'ai',
              `Invalid array format. Please enter a valid JSON array for "${currentParam.name}":`,
            );
            return;
          }
          break;
      }
      this.parameterState.collectedInputs[currentParam.name] = processedInput;
      this.parameterState.currentParameterIndex++;
      if (
        this.parameterState.currentParameterIndex >=
        this.parameterState.parameters.length
      ) {
        this.executeToolWithParameters();
      } else {
        this.promptForNextParameter();
      }
    } catch (error) {
      console.error('Error processing parameter input:', error);
      this.addChatMessage(
        'ai',
        `Error processing input for "${currentParam.name}". Please try again:`,
      );
    }
  }

  private executeToolWithParameters(): void {
    this.parameterState.isCollecting = false;
    this.isProcessingChat = true;

    this.addChatMessage(
      'ai',
      'All parameters collected! Executing your tool...',
    );

    const payload = {
      class_definition: this.toolForm.get('classDefinition')?.value,
      class_name: this.toolForm.get('toolClassName')?.value,
      inputs: this.parameterState.collectedInputs,
    };

    this.toolsService.testTool(payload).subscribe({
      next: (response: any) => {
        console.log('Tool execution response:', response);
        this.isProcessingChat = false;

        if (response.status === 'success') {
          this.addChatMessage(
            'ai',
            `Tool executed successfully! Output: ${response.output}`,
          );
        } else {
          this.addChatMessage(
            'ai',
            `Tool execution failed: ${response.detail || 'Unknown error'}`,
          );
        }
        this.waitingForRestartConfirmation = true;
        setTimeout(() => {
          this.addChatMessage(
            'ai',
            'Would you like to test the tool again with different parameters? (Type "yes" to restart or anything else to continue)',
          );
        }, 1000);
      },
      error: (error) => {
        console.error('Tool execution error:', error);
        this.isProcessingChat = false;
        this.addChatMessage(
          'ai',
          `Tool execution failed: ${error?.error?.message || 'Unknown error occurred'}`,
        );
      },
    });
  }

  onExit(): void {
    if (this.isExecuteMode && this.isEditMode) {
      this.isExecuteMode = false;
      this.resetParameterState();
      console.log('Exited execution mode, returning to edit mode');
    } else {
      const returnPage = this.route.snapshot.queryParamMap.get('returnPage');
      const pageNumber = returnPage ? parseInt(returnPage) : 1;

      this.router.navigate(['/libraries/tools'], {
        queryParams: { page: pageNumber },
      });
    }
  }

  private resetParameterState(): void {
    this.parameterState = {
      parameters: [],
      currentParameterIndex: 0,
      collectedInputs: {},
      isCollecting: false,
    };
  }

  getControl(name: string): FormControl {
    return this.toolForm.get(name) as FormControl;
  }

  validateCode = (): void => {
    this.validateTool();
  };

  public validateTool(): void {
    this.showValidationOutput = false;
    this.validationOutput = '';
    const useCase = 'VALIDATE_TOOLS';
    const useCaseIdentifier = 'VALIDATE_TOOLS@ASCENDION@PLATFORM_ENGINEERING@AVA@DIGITAL_ASCENDER';
    const toolClassDef = this.toolForm.controls['classDefinition'].value;
    this.promptGenerateService.modelApi(toolClassDef, useCase, false, useCaseIdentifier).subscribe({
      next: (res: any) => {
        let responseText = res?.response?.choices?.[0]?.text;
        if (!responseText) {
          this.validationOutput = 'Unable to validate, please try again.';
          this.showValidationOutput = true;
          if (this.codeEditor) this.codeEditor.hideProcessingLoader();
          return;
        }
        // Remove markdown code block if present
        responseText = responseText.replace(/```json\n?/, '').replace(/```\n?$/, '');
        try {
          const parsed = JSON.parse(responseText);
          // Format as plain text
          let formatted = '';
          if (parsed.issues && Array.isArray(parsed.issues) && parsed.issues.length > 0) {
            parsed.issues.forEach((issue: any, idx: number) => {
              formatted += `Issue ${idx + 1}:\n`;
              formatted += `  Type: ${issue.type}\n`;
              formatted += `  Description: ${issue.description}\n`;
              formatted += `  Severity: ${issue.severity}\n`;
              formatted += `  Line Number: ${issue.line_number}\n`;
              formatted += `  Suggestion: ${issue.suggestion}\n\n`;
            });
          } else {
            formatted = 'No issues found.';
          }
          this.validationOutput = formatted;
        } catch (e) {
          this.validationOutput = responseText;
        }
        this.showValidationOutput = true;
        if (this.codeEditor) this.codeEditor.hideProcessingLoader();
      },
      error: (e) => {
        this.validationOutput = 'Error: ' + (e?.error?.message || 'Unknown error');
        this.showValidationOutput = true;
        if (this.codeEditor) this.codeEditor.hideProcessingLoader();
      }
    });
  }

  loadToolData(toolId: number): void {
    this.toolsService.getUserToolDetails(toolId).subscribe(
      (response) => {
        if (response && response.tools && response.tools.length > 0) {
          const tool = response.tools[0];

          this.toolForm.patchValue({
            name: tool.toolName || '',
            description: tool.toolDescription || '',
            toolClassName: tool.toolClassName || '',
            classDefinition: tool.toolClassDef || '',
          });

          // Also set the code in the editor if it's available
          if (this.codeEditor && tool.toolClassDef) {
            this.codeEditor.setValue(tool.toolClassDef);
          }
        }
      },
      (error) => {
        console.error('Error loading tool data:', error);
      },
    );
  }

 //Drop Down
  promptOptions: SelectOption[] = [
  { value: 'default', label: 'Choose Prompt' },
  { value: 'ruby-developer', label: 'Senior Ruby Developer' },
  { value: 'python-developer', label: 'Python Developer' },
  { value: 'data-scientist', label: 'Data Scientist' },
  { value: 'frontend-developer', label: 'Frontend Developer' },
];


  onPromptChanged(option: SelectOption) {
    console.log('Prompt changed in parent:', option);
    // your logic to handle selected prompt
  }

  onEditorAction(idx: number) {
    if (!this.codeEditor) return;
    if (idx === 0) this.codeEditor.selectAll();
    if (idx === 1) this.codeEditor.clear();
  }
}
