<p class="page-title">Tool Creator 🛠️</p>
<div class="create-tools-container">
  <form [formGroup]="toolForm">
    <div
      class="form-layout"
      [ngClass]="{ 'three-column-layout': isExecuteMode && showChatInterface }"
    >
      <!-- Left Column -->
      <div class="left-column">
        <!-- Tool Details Card -->
            <!-- <app-form-field
              label="{{ labels.toolName }}"
              id="toolName"
              [control]="getControl('name')"
            ></app-form-field> -->
            <ava-textbox
              [formControl]="getControl('name')"
              [label]="labels.toolName"
              id="toolName"
              name="toolName"
              placeholder="Enter tool name"
              variant="primary"
              size="md"
              [fullWidth]="true"
              [required]=true
            ></ava-textbox>

            <!-- <app-form-field
              label="{{ labels.toolClassName }}"
              id="toolClassName"
              [control]="getControl('toolClassName')"
            ></app-form-field> -->

            <!-- <app-form-field
              label="{{ labels.description }}"
              id="description"
              [control]="getControl('description')"
              type="textarea"
            ></app-form-field> -->
            <ava-textarea
              id="description"
              name="description"
              [label]="labels.description"
              [formControl]="getControl('description')"
              placeholder="Enter description"
              [rows]="4"
              variant="primary"
              size="md"
              [fullWidth]="true"
              [required]=true>
            </ava-textarea>
          </div>
        <!-- </app-card> -->
      <!-- </div> -->

      <!-- Middle Column (previously Right) -->
      <div class="middle-column">
        <!-- Content wrapper to match left column height -->
        <div class="middle-column-content">
          <!-- Tool Class Definition Card -->
          <app-card
            customClass="solid-card"
            [noPadding]="false"
            [isClickable]="false"
            [noHoverEffect]="true"
          >
            <div class="card-content code-editor">
              <!-- Code Editor Component -->
              <app-code-editor
                #codeEditor
                placeholder="{{labels.placeholder}}"
                title="{{labels.toolClassDefinition}}"
                language="python"
                [Control]="getControl('classDefinition')"
                customCssClass="tools-monaco-editor"
                (primaryButtonSelected)="validateCode()"
                footerText="{{labels.note}}"
                [actionButtons]="editorActions"
                (actionButtonClicked)="onEditorAction($event)"
              >
              </app-code-editor>

              <!-- Validation Output JSON Editor -->
              <div *ngIf="showValidationOutput" class="validation-output-section">
                <app-code-editor
                  [title]="validationOutputEditorConfig.title"
                  [language]="validationOutputEditorConfig.language"
                  [theme]="validationOutputEditorConfig.theme"
                  [readonly]="validationOutputEditorConfig.readOnly"
                  [height]="validationOutputEditorConfig.height"
                  [value]="validationOutput"
                  customCssClass="validation-json-editor">
                </app-code-editor>
              </div>

              <!-- Buttons at bottom of middle column -->
              <div class="middle-column-buttons">
                <!-- <button type="button" class="exit-button" (click)="onExit()">
                  {{ labels.exit }}
                </button> -->
                <ava-button
                  label="{{ labels.exit }}"
                  variant="secondary"
                  size="small"
                  
                  (userClick)="onExit()"
                >
                </ava-button>
                <!-- Show different buttons based on mode -->
                <ng-container *ngIf="!isEditMode">
                  <!-- <button type="button" class="save-button" (click)="onSave()">
                    {{ labels.save }}
                  </button> -->
                  <ava-button
                    label="{{ labels.save }}"
                    variant="primary"
                    size="small"
                    (userClick)="onSave()"
                  >
                  </ava-button>
                </ng-container>
                <ng-container *ngIf="isEditMode && !isExecuteMode">
                  <!-- <button
                    type="button"
                    class="execute-button"
                    (click)="onExecute()"
                  >
                    {{ labels.execute }}
                  </button> -->
                  <ava-button
                    label="{{ labels.execute }}"
                    variant="primary"
                    size="small"
                    gradient="linear-gradient(45deg,lightblue,darkblue)"
                    (userClick)="onExecute()"
                  >
                  </ava-button>
                </ng-container>
                <ng-container *ngIf="isEditMode && isExecuteMode">
                  <!-- <button
                    type="button"
                    class="execute-button"
                    (click)="onSave()"
                  >
                    {{ labels.save }}
                  </button> -->
                  <ava-button
                    label="{{ labels.save }}"
                    variant="primary"
                    size="small"
                    gradient="linear-gradient(45deg,lightblue,darkblue)"
                    (userClick)="onSave()"
                  >
                  </ava-button>
                </ng-container>
              </div>
            </div>
          </app-card>
        </div>
      </div>

      <div class="rightEnd-column" *ngIf="isExecuteMode && showChatInterface">
         <app-playground
           [promptOptions]="promptOptions"
            [messages]="chatMessages"
            [isLoading]="isProcessingChat"
            (promptChange)="onPromptChanged($event)"
            (messageSent)="handleChatMessage($event)"
        ></app-playground>
      </div>

      <!-- Right Column (new) - Chat Interface -->
      <!-- <div class="chat-column" *ngIf="isExecuteMode && showChatInterface">
        <div class="chat-column-content">
          <app-card
            customClass="solid-card"
            [noPadding]="false"
            [isClickable]="false"
            [noHoverEffect]="true"
          >
            <div class="card-content">
              <h3 class="section-title">{{labels.playground}}</h3>
              <h4 class="playground-title">{{toolForm.get('name')?.value }}</h4>
              
              <div class="chat-container">
                <app-chat-interface
                  [messages]="chatMessages"
                  [isLoading]="isProcessingChat"
                  (messageSent)="handleChatMessage($event)"
                >
                </app-chat-interface>
              </div>
            </div>
          </app-card>
        </div>
      </div> -->
    </div>
  </form>
</div>
