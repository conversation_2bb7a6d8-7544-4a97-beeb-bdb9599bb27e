import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, ActivatedRoute } from '@angular/router';
import { PageFooterComponent } from '../../../shared/components/page-footer/page-footer.component';
import { PaginationService } from '../../../shared/services/pagination.service';
import { ToolsService } from '../../../shared/services/tools.service';
import {
  AvaTextboxComponent,
  DropdownComponent,
  DropdownOption,
  TextCardComponent,
} from '@ava/play-comp-library';
import { LucideAngularModule } from 'lucide-angular';
import toolsText from './constants/tools.json';

@Component({
  selector: 'app-tools',
  standalone: true,
  imports: [
    CommonModule,
    PageFooterComponent,
    TextCardComponent,
    AvaTextboxComponent,
    DropdownComponent,
    LucideAngularModule,
  ],
  templateUrl: './tools.component.html',
  styleUrl: './tools.component.scss',
})
export class ToolsComponent implements OnInit {
  allTools: any[] = [];
  filteredTools: any[] = [];
  displayedTools: any[] = [];
  isLoading: boolean = false;
  error: string | null = null;
  currentPage: number = 1;
  itemsPerPage: number = 12;
  totalPages: number = 1;
  public labels: any = toolsText.labels;
  toolsOptions: DropdownOption[] = [
    { name: 'All', value: 'all' },
    { name: 'Type A', value: 'typeA' },
    { name: 'Type B', value: 'typeB' },
  ];
  selectedData: any = null;
  iconList = [
    { name: 'archive', iconName: 'archive', cursor: true },
    { name: 'trash', iconName: 'trash-2', cursor: true },
    { name: 'copy', iconName: 'copy', cursor: true },
    { name: 'play', iconName: 'play', cursor: true },
  ];

  constructor(
    private paginationService: PaginationService,
    private router: Router,
    private route: ActivatedRoute,
    private toolsService: ToolsService,
  ) {}

  ngOnInit(): void {
    this.getAllTools();
  }

  getAllTools(): void {
    this.isLoading = true;
    this.toolsService.getUserToolsList().subscribe((response: any) => {
      this.allTools = (response.tools || []).map((tool: any) => ({
        ...tool,
        id: tool.toolId.toString(),
        title: tool.toolName,
        name: tool.toolName || 'AAVA',
        description: tool.toolDescription,
        createdDate: tool.createTimestamp,
      }));
      this.filteredTools = [...this.allTools];
      this.updateDisplayedTools();
      this.isLoading = false;
    });
    const pageParam = this.route.snapshot.queryParamMap.get('page');
    if (pageParam) {
      this.currentPage = parseInt(pageParam, 10);
    }
  }

  updateDisplayedTools(): void {
    const result = this.paginationService.getPaginatedItems(
      this.filteredTools,
      this.currentPage,
      this.itemsPerPage,
    );
    this.displayedTools = result.displayedItems;
    this.totalPages = result.totalPages;
  }

  onCreateTool(): void {
    this.router.navigate(['/libraries/tools/create']);
  }

  onCardClicked(toolId: string): void {
    this.router.navigate(['/libraries/tools/edit', toolId], {
      queryParams: { returnPage: this.currentPage },
    });
  }

  onIconClicked(icon: any, toolId: string): void {
    switch (icon.name) {
      case 'trash':
        this.deleteTool(toolId);
        break;
      case 'edit':
        this.router.navigate([`/libraries/tools/edit/${toolId}`]);
        break;
      case 'copy':
        this.duplicateTool(toolId);
        break;
      default:
        break;
    }
  }

  deleteTool(toolId: string): void {
    // Implement delete logic
  }

  duplicateTool(toolId: string): void {
    // Implement duplicate logic
  }

  onSelectionChange(data: any) {
    this.selectedData = data;
    // Implement filter logic if needed
  }

  onPageChange(page: number): void {
    this.currentPage = page;
    this.updateDisplayedTools();
  }

  get showCreateCard(): boolean {
    return this.currentPage === 1 && !this.isLoading && !this.error;
  }
}
