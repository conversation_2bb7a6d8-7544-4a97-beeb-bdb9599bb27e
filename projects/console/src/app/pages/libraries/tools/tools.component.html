<div class="tools-container">
  <div class="tools-header">
    <div class="header-content">
      <div class="search-section">
        <ava-textbox placeholder="Search..."></ava-textbox>
      </div>
      <div class="action-buttons">
        <ava-dropdown
          dropdownTitle="choose tool"
          [options]="toolsOptions"
          (selectionChange)="onSelectionChange($event)"
        >
        </ava-dropdown>
      </div>
    </div>
  </div>

  <div class="cards-container">
    <!-- Create Tool Card - show only on first page -->
    <ava-text-card
      *ngIf="showCreateCard"
      [type]="'create'"
      [iconName]="'plus'"
      iconColor="#144692"
      [title]="labels.createNew"
      (cardClick)="onCreateTool()"
    >
    </ava-text-card>

    <!-- No results message -->
    <div class="no-results" *ngIf="filteredTools.length === 0">
      {{ labels.noTool }}
    </div>

    <!-- Tool Data Cards -->
    <ava-text-card
      *ngFor="let tool of displayedTools"
      [type]="'prompt'"
      [title]="tool.title"
      [name]="tool.name || 'AAVA'"
      [date]="tool.createdDate"
      [iconList]="iconList"
      iconColor="#144692"
      [userCount]="120"
      (iconClick)="onIconClicked($event, tool.id)"
      (cardClick)="onCardClicked(tool.id)"
    >
    </ava-text-card>
  </div>

  <!-- Page Footer with Pagination -->
  <app-page-footer
    *ngIf="filteredTools.length > 0"
    [totalItems]="filteredTools.length + (showCreateCard ? 1 : 0)"
    [currentPage]="currentPage"
    [itemsPerPage]="itemsPerPage"
    (pageChange)="onPageChange($event)"
  >
  </app-page-footer>
</div>
