<div class="select-container" [class.disabled]="disabled" [ngStyle]="{ width: dropdownWidth }">
  <label *ngIf="label" class="select-label">{{ label }}</label>

  <div
    class="select-field"
    (click)="toggleDropdown()"
    [class.open]="isOpen"
    [class.multi-select]="isMultiSelect"
    [ngStyle]="{ height: dropdownHeight }"
    tabindex="0"
    role="combobox"
    aria-haspopup="listbox"
    aria-controls="dropdown-listbox"
    aria-expanded="false"
    [attr.aria-expanded]="isOpen ? 'true' : 'false'"
    [attr.aria-activedescendant]="isOpen && activeIndex >= 0 ? 'dropdown-option-' + activeIndex : null"
    [attr.aria-disabled]="disabled"
  >
    <div class="selected-value">
      <span>{{ getSelectedLabel() }}</span>
    </div>

    <div class="select-icon">
      <svg
        width="12"
        height="12"
        viewBox="0 0 12 12"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        [class.open]="isOpen"
      >
        <path
          d="M2.5 4L6 7.5L9.5 4"
          stroke="currentColor"
          stroke-width="1.5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </svg>
    </div>
  </div>

  <div
    class="dropdown-menu"
    *ngIf="isOpen"
    id="dropdown-listbox"
    role="listbox"
    [attr.aria-multiselectable]="isMultiSelect"
  >
    <div
      *ngFor="let option of options; let i = index"
      class="dropdown-item"
      [class.selected]="isOptionSelected(option)"
      [class.active]="i === activeIndex"
      (click)="selectOption(option, $event)"
      id="dropdown-option-{{i}}"
      role="option"
      [attr.aria-selected]="isOptionSelected(option)"
      tabindex="-1"
    >
      <div class="option-content">
        <div *ngIf="isMultiSelect" class="checkbox-container">
          <div class="checkbox" [class.checked]="isOptionSelected(option)">
            <svg
              *ngIf="isOptionSelected(option)"
              width="10"
              height="10"
              viewBox="0 0 12 12"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M2 6L5 9L10 3"
                stroke="white"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </div>
        </div>
        {{ option.label }}
      </div>
    </div>
  </div>
</div>
