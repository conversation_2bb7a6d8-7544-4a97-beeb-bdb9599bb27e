import { Component, OnInit, OnDestroy, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, NgForm } from '@angular/forms';
import { Subscription } from 'rxjs';

import {
  PersonaDataService,
  PersonaData,
  PersonaCard,
} from '../../services/persona-data.service';

// Import AWE Components (adjust paths as needed)
import { AweCardComponent } from '../../components/awe-card/awe-card.component';
import { AweModalComponent } from '../../components/awe-modal/awe-modal.component';
import { HeadingComponent, IconsComponent, InputComponent, SliderComponent } from '@awe/play-comp-library';

@Component({
  selector: 'app-user-persona',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    AweCardComponent,
    AweModalComponent,
    HeadingComponent,
    IconsComponent,
    InputComponent,
    SliderComponent,
  ],
  templateUrl: './user-persona.component.html',
  styleUrls: ['./user-persona.component.scss'],
})
export class UserPersonaComponent implements OnInit, OnDestroy {
  // Output events
  @Output() personaSelected = new EventEmitter<string>();

  // Icons
  editIcon: string = '/icons/awe_edit.svg';
  deleteIcon: string = '/svgs/delete-icon2.svg';
  colonIcon: string = '/svgs/colon.svg';
  threeDotsIcon: string = '/icons/three-dot.svg';
  awe_delete: string = '/icons/awe-delete.svg';

  // Avatars
  salesAvatar: string = '/svgs/sales-avatar.svg';
  managerAvatar: string = '/svgs/manager-avatar.svg';
  developerAvatar: string = '/svgs/developer-avatar.svg';
  teacherAvatar: string = '/svgs/teacher-avatar.svg';
  designerAvatar: string = '/svgs/designer-avatar.svg';

  // Pagination
  currentPage: number = 1;
  itemsPerPage: number = 5;

  // Modal state
  isModalOpen = false;
  modalMode: 'add' | 'edit' | 'delete' = 'add';
  editablePersona: Partial<PersonaData> = {};
  personalityInput: string = '';
  regeneratePrompt: string = '';
  selectedPersonaForEdit: PersonaData | null = null;

  // Unified modal state
  selectedCardForEdit: PersonaCard | null = null;
  editData: any = {};
  // Dropdown state
  openDropdownId: string | null = null;
  personas: PersonaData[] = [];
  private subscription = new Subscription();
  // --- Delete Modal State ---
  isDeleteModalOpen = false;
  personaToDelete: PersonaData | null = null;
  deleteBadgeCount: number = 1; // You can set this dynamically as needed

  constructor(
    private personaDataService: PersonaDataService
  ) {}
  ngOnInit(): void {
    // Subscribe to personas data
    this.subscription.add(
      this.personaDataService.personas$.subscribe((personas) => {
        this.personas = personas;
      }),
    );
  }
  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }
  // --- Getters ---
  get currentPagePersonas(): PersonaData[] {
    const startIndex = (this.currentPage - 1) * this.itemsPerPage;
    return this.personas.slice(startIndex, startIndex + this.itemsPerPage);
  }

  get totalPages(): number {
    return Math.ceil(this.personas.length / this.itemsPerPage);
  }

  // // --- Dropdown Methods ---
  // toggleDropdown(personaId: string): void {
  //   this.openDropdownId = this.openDropdownId === personaId ? null : personaId;
  // }

  closeAllDropdowns(): void {
    this.openDropdownId = null;
  }

  // isDropdownOpen(personaId: string): boolean {
  //   return this.openDropdownId === personaId;
  // }

  // --- Modal Methods ---
  openAddModal(): void {
    this.modalMode = 'add';
    this.selectedPersonaForEdit = null;
    this.selectedCardForEdit = {
      id: 'new-persona-profile',
      title: 'Profile',
      type: 'profile',
      data: {}
    };
    this.resetEditablePersona();
    this.setupEditDataForAdd();
    this.isModalOpen = true;
    // this.closeAllDropdowns();
  }

  // openEditModal(persona: PersonaData): void {
  //   this.modalMode = 'edit';
  //   this.selectedPersonaForEdit = persona;
  //   this.editablePersona = { ...persona };
  //   this.personalityInput = persona.personality.join(', ');
  //   this.isModalOpen = true;
  //   // this.closeAllDropdowns();
  // }

  closeModal(): void {
    this.closeEditModal();
  }



  openDeleteModal(persona: PersonaData): void {
    this.isDeleteModalOpen = true;
    this.personaToDelete = persona;
    this.deleteBadgeCount = 1; // Or set to a relevant value, e.g., 1 or personas.length
  }

  closeDeleteModal(): void {
    this.isDeleteModalOpen = false;
    this.personaToDelete = null;
  }

  confirmDeletePersona(): void {
    if (this.personaToDelete) {
      this.personaDataService.deletePersona(this.personaToDelete.id);
    }
    this.closeDeleteModal();
  }

  // --- Unified Modal Methods ---
  setupEditDataForAdd(): void {
    this.editData = {
      name: '',
      role: '',
      age: '',
      education: '',
      status: '',
      location: '',
      techLiteracy: 'Medium',
      quote: '',
      avatar: '',
      personality: []
    };
  }

  closeEditModal(): void {
    this.isModalOpen = false;
    this.selectedCardForEdit = null;
    this.editData = {};
    this.regeneratePrompt = '';
  }

  saveCardData(): void {
    if (!this.selectedCardForEdit) return;

    if (this.modalMode === 'add') {
      const personalityArray = Array.isArray(this.editData.personality)
        ? this.editData.personality
        : [];

      const newPersonaData = this.personaDataService.convertUserPersonaToPersonaData({
        ...this.editData,
        personality: personalityArray,
        avatar: this.personaDataService.getDefaultAvatar(this.editData.role || ''),
      });
      this.personaDataService.addPersona(newPersonaData);
      this.currentPage = this.totalPages;
    }

    this.closeEditModal();
  }

  // --- Array Data Methods ---
  isArrayData(data: any): boolean {
    return Array.isArray(data);
  }

  addArrayItem(): void {
    if (!this.selectedCardForEdit) return;

    if (this.selectedCardForEdit.type === 'skills') {
      if (!Array.isArray(this.editData)) {
        this.editData = [];
      }
      this.editData.push({ name: '', level: 50 });
    } else if (Array.isArray(this.editData)) {
      this.editData.push('');
    }
  }

  removeArrayItem(index: number): void {
    if (Array.isArray(this.editData) && index >= 0 && index < this.editData.length) {
      this.editData.splice(index, 1);
    }
  }

  // --- Device Methods ---
  toggleDevice(deviceType: string): void {
    if (!Array.isArray(this.editData)) {
      this.editData = [];
    }

    const index = this.editData.indexOf(deviceType);
    if (index > -1) {
      this.editData.splice(index, 1);
    } else {
      this.editData.push(deviceType);
    }
  }

  // --- Utility Methods ---
  trackByIndex(index: number): number {
    return index;
  }

  onDragStart(_index: number): void {
    // Handle drag start for sliders if needed
  }

  onDragEnd(_index: number): void {
    // Handle drag end for sliders if needed
  }

  onRegenerate(): void {
    if (!this.regeneratePrompt.trim()) {
      console.log('Regenerate prompt is empty.');
      return;
    }

    console.log('Regenerating with prompt:', this.regeneratePrompt);
    // In a real application, you would add logic here to:
    // 1. Show a loading spinner.
    // 2. Call an API service with the prompt.
    // 3. Update the form fields with the data returned from the API.
    // 4. Hide the loading spinner.

    // For now, we'll just log it and clear the field.
    this.regeneratePrompt = '';
  }

  private resetEditablePersona(): void {
    this.editablePersona = {};
    this.personalityInput = '';
    this.regeneratePrompt = '';
  }
  // // --- Pagination Methods ---
  // previousPage(): void {
  //   if (this.currentPage > 1) this.currentPage--;
  // }

  // nextPage(): void {
  //   if (this.currentPage < this.totalPages) this.currentPage++;
  // }

  trackByPersona(_index: number, persona: PersonaData): string {
    return persona.id;
  }

  // Navigation method
  onPersonaClick(persona: PersonaData): void {
    this.personaDataService.setSelectedPersona(persona.id);
    this.personaSelected.emit(persona.id);
  }
}
