:host {
  display: block;
  font-family:
    "<PERSON><PERSON><PERSON>",
    "<PERSON>",
    -apple-system,
    BlinkMacSystemFont,
    "Segoe UI",
    <PERSON><PERSON>,
    "Helvetica Neue",
    <PERSON><PERSON>,
    sans-serif;
}

// .top-bar-gradient {
//   height: 4px;
//   background: linear-gradient(90deg, #8B5CF6 0%, #EC4899 50%, #F59E0B 100%);
//   width: 100%;
// }

.user-persona-page {
  background-color: #f9fafb;
  min-height: 100vh;
  position: relative;
}

.container-fluid {
  max-width: 1776px;
  margin: 0 auto;
  padding: 0 24px;
}

.header-section {
  margin-bottom: 32px;
  h1 {
    font-size: 28px;
    font-weight: 600;
  }
}

.btn-add-new {
  background-color: rgba(125, 99, 246, 1);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 10px 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
  &:hover {
    background-color: darken(#8a3ffc, 8%);
  }
}

// .personas-grid {
//   display: grid;
//   grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
//   gap: 24px;
//   justify-items: center;
// }

// .persona-card-wrapper {
//   // width: 100%;
//   // max-width: 350px;
// }

// --- FIXED: Card Styling ---
.persona-card {
  --awe-card-border: none;
  --awe-card-border-radius: 20px;
  --awe-card-box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.07), 0 2px 4px -2px rgba(0, 0, 0, 0.07);
  --awe-card-background: #ffffff;
  // position: relative;
  transition: all 0.2s ease-in-out;

  &:hover {
    --awe-card-box-shadow:
      0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1);
    transform: translateY(-5px);
  }
}

// Three dots dropdown styling
.card-actions {
  position: absolute;
  top: 16px;
  right: 16px;
  z-index: 10;
}

.three-dots-btn {
  background: none;
  border: none;
  padding: 4px;
  cursor: pointer;
  opacity: 0.5;
  transition: opacity 0.2s ease;
  border-radius: 4px;
}

:host ::ng-deep .awe-modal-close-button[_ngcontent-ng-c2102579181] {
  display: none;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  min-width: 120px;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.2s ease;

  &.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
  }
}

.dropdown-item {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 8px 12px;
  background: none;
  border: none;
  text-align: left;
  font-size: 14px;
  color: #374151;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #f3f4f6;
  }

  &.text-danger {
    color: #dc2626;
  }

  &:first-child {
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
  }

  &:last-child {
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
  }
}

.delete-btn {
  position: absolute;
  top: 16px;
  right: 16px;
  background: none;
  border: none;
  padding: 4px;
  cursor: pointer;
  z-index: 2;
  opacity: 0.5;
  transition: opacity 0.2s ease;

  img {
    width: 20px;
    height: 20px;
  }

  &:hover {
    opacity: 1;
  }
}

// --- FIXED: Card Content Sections ---
.profile-section {
  .avatar-wrapper {
    width: 90px;
    height: 90px;
    border-radius: 50%;
    background: linear-gradient(135deg, #fbcfe8, #f9a8d4);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 5px;
    box-shadow: 0 4px 12px rgba(252, 165, 165, 0.4);
  }

  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
  }

  .role-title {
    margin-top: 1rem;
    font-size: 1.5rem;
    font-weight: 600;
    color: #1d4ed8;
  }
}

.info-section {
  margin: 24px 0;
  display: flex;
  flex-direction: column;
  gap: 8px;

  .info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.875rem;
    padding: 10px 16px;
    border-radius: 12px;
    background-color: rgba(245, 245, 250, 1);

    .info-label {
      color: #6b7280;
    }
    .info-value {
      color: #111827;
      font-weight: 500;
    }
  }
}

.quote-section {
  background-color: rgba(245, 245, 250, 1);
  border-radius: 16px;
  padding: 1rem;
  position: relative;

  .quote-icon {
    position: absolute;
    top: 1rem;
    left: 1rem;
    width: 20px;
    height: 20px;
    opacity: 0.5;
  }

  .quote-text {
    font-size: 0.875rem;
    line-height: 1.5;
    color: #4338ca;
    padding-left: 28px;
    margin-bottom: 0;
  }
}

.personality-section {
  margin-top: 24px;

  .personality-title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 12px;
    color: #374151;
  }

  .personality-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;

    .personality-tag {
      padding: 6px 14px;
      border-radius: 16px;
      font-size: 0.75rem;
      font-weight: 500;

      &:nth-child(3n + 1) {
        background-color: #e0e7ff;
        color: rgba(101, 102, 205, 1);
      }
      &:nth-child(3n + 2) {
        background-color: #f3e8ff;
        color: rgba(101, 102, 205, 1);
      }
      &:nth-child(3n + 3) {
        background-color: #fce7f3;
        color: rgba(101, 102, 205, 1);
      }
    }
  }
}

// --- Pagination & Modal Styling (Unchanged) ---
.pagination-section {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  padding: 24px 0;
}

.pagination-arrow {
  background: white;
  border: 1px solid #e5e7eb;
  width: 32px;
  height: 32px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 20px;
  color: #6b7280;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;

  &:hover:not(:disabled) {
    border-color: #d1d5db;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  }

  &:disabled {
    opacity: 0.4;
    cursor: not-allowed;
    background-color: #f9fafb;
  }
}

.persona-edit-modal {
  --awe-modal-width: 550px;
  --awe-modal-bg: #f9fafb;
}

[awe-modal-body] {
  form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }
  .form-row,
  .form-row-full {
    display: grid;
    align-items: center;
    gap: 0 1rem;
  }
  .form-row {
    grid-template-columns: 100px 1fr;
  }
  .form-row-full {
    grid-template-columns: 1fr;
    .form-label {
      margin-bottom: 0.5rem;
    }
  }

  .form-label {
    font-weight: 500;
    font-size: 0.9rem;
    color: #4b5563;
  }
  .form-control {
    width: 100%;
    padding: 0.6rem 0.8rem;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    background-color: white;
    transition:
      border-color 0.2s ease,
      box-shadow 0.2s ease;
    &:focus {
      outline: none;
      border-color: #8a3ffc;
      box-shadow: 0 0 0 3px rgba(138, 63, 252, 0.15);
    }
  }
  .form-text {
    font-size: 0.75rem;
    color: #9ca3af;
    margin-top: 4px;
  }
}

.regenerate-section {
  margin-top: 1rem;
  .form-label {
    font-weight: 600;
    margin-bottom: 0.5rem;
  }
  .regenerate-input-wrapper {
    position: relative;
    textarea {
      border-color: #f472b6;
      padding-right: 40px;
      &:focus {
        border-color: #ec4899;
        box-shadow: 0 0 0 3px rgba(236, 72, 153, 0.15);
      }
    }
  }
  .regenerate-send-btn {
    position: absolute;
    right: 10px;
    bottom: 10px;
    background: none;
    border: none;
    font-size: 1.2rem;
    color: #be185d;
    cursor: pointer;
    transform: rotate(-45deg);
  }
}

[awe-modal-footer] {
  .btn-cancel {
    background-color: #e5e7eb;
    color: #4b5563;
    border: none;
    border-radius: 50px;
    padding: 10px 20px;
    font-weight: 500;
    transition: background-color 0.2s ease;
    &:hover {
      background-color: #d1d5db;
    }
  }
  .btn-update {
    background-color: #8a3ffc;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 10px 20px;
    font-weight: 500;
    transition: background-color 0.2s ease;
    &:hover:not(:disabled) {
      background-color: darken(#8a3ffc, 8%);
    }
    &:disabled {
      background-color: #c4b5fd;
      cursor: not-allowed;
    }
  }
  .btn-delete {
    background: transparent;
    border: none;
    padding: 8px;
    img {
      width: 20px;
      height: 20px;
      filter: grayscale(1) opacity(0.6);
    }
    &:hover img {
      filter: grayscale(0) opacity(1);
    }
  }
}

.delete-modal {
  button {
    border-radius: 50px;
    background: #fff;
    padding: 12px 24px;
    border: none;
    border-radius: 50px;
  }
  .btn-cancel {
    border: 1px solid var(--Primary-500, #7c3aed);
    background: var(--Neutral-Neutral-colors-Solid, #fff);
  }
  .btn-delete {
    background: var(--Primary-500, #7c3aed);
    border: 1px solid var(--Primary-500, #7c3aed);
    color: #fff;
  }
}


:host ::ng-deep awe-heading {
  margin-bottom: 0.5rem;
}

:host ::ng-deep .input-container .input-wrapper.expanded {
  height: 125px;
}

.inp-container {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  // align-items: stretch;
  gap: 10px;
  margin-bottom: 0;
  .label {
    width: auto;
    display: flex;
    align-self: center;
    align-items: center;
    font-size: 1rem;
    flex-grow: 0;
  }
  .input-wrapper {
    width: auto;
    flex-grow: 1;
  }
}
:host ::ng-deep .input-container {
  padding: 0;
}
