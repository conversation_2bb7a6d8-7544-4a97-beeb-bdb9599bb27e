import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ChangeDetectorRef, Component } from '@angular/core';
import {
  BodyTextComponent,
  ButtonComponent,
  CaptionComponent,
  HeadingComponent,
  IconsComponent,
  InputComponent,
} from '@awe/play-comp-library'; // Assuming this is your awe-caption
import { AweCardComponent } from '../../components/awe-card/awe-card.component'; // Adjust path
import { AweModalComponent } from '../../components/awe-modal/awe-modal.component';

export interface CanvasItem {
  id: string;
  title: string;
  icon: string;
  iconBg: string;
  data: string[];
}

@Component({
  selector: 'app-understanding',
  templateUrl: './understanding.component.html',
  styleUrls: ['./understanding.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    CaptionComponent,
    AweCardComponent,
    IconsComponent,
    AweModalComponent,
    HeadingComponent,
    BodyTextComponent,
    InputComponent,
    ButtonComponent,
  ],
})
export class UnderstandingComponent {
  // Action Icons (used in the projected header)
  trashIcon: string = '/icons/awe_trash.svg';
  editIcon: string = '/icons/awe_edit.svg';
  errorIcon: string = '/icons/awe_error.svg';
  // threeDotsIcon: string = '/icons/three-dot.svg';

  // Card Data Icons (used in the projected header)
  problemIcon: string = '/cards-icons/problem.svg';
  solutionIcon: string = '/cards-icons/solution.svg';
  keyPartnerIcon: string = '/cards-icons/key-pattern.svg';
  valuePropositionIcon: string = '/cards-icons/value-proposition.svg';
  customerSegmentsIcon: string = '/cards-icons/customer-segments.svg';
  keyMetricsIcon: string = '/cards-icons/key-metrics.svg';
  alternativesIcon: string = '/cards-icons/alternatives.svg';
  costStructureIcon: string = '/cards-icons/cost-structure.svg';
  revenueStreamsIcon: string = '/cards-icons/revenue-streams.svg';

  // Card Images (used in the projected header)
  solutionCardImg: string = '/cards-images/solution-card.png';
  keyPartnerCardImg: string = '/cards-images/key-partners.png';
  valuePropositionCardImg: string = '/cards-images/value-preposition.png';
  customerSegmentsCardImg: string = '/cards-images/ customer-segments.png';
  keyMetricsCardImg: string = '/cards-images/key-metrics-card.png';
  alternativesCardImg: string = '/cards-images/alternatives-card.png';
  costStructureCardImg: string = '/cards-images/cost-structure.png';
  revenueStreamsCardImg: string = '/cards-images/revenue-stream.png';

  button_bg: string =
    'linear-gradient(90deg, rgba(101, 102, 205, 0.40) -2.03%, rgba(249, 108, 171, 0.40) 109.39%);';
  loadingStates: Record<string, boolean> = {
    submit: false,
    save: false,
    delete: false,
    skeleton1: false,
    skeleton2: false,
  };

  // Main data source for the cards
  businessModelCanvas: CanvasItem[] = [
    {
      id: 'problem',
      title: 'Problem',
      icon: this.problemIcon,
      iconBg: '#ffebee',
      data: [
        'People are tired but bad at napping — breaks turn into oversleeping, alarms are harsh, and naps feel unproductive.',
      ],
    },
    {
      id: 'key-partners',
      title: 'Key partners',
      icon: this.keyPartnerCardImg,
      iconBg: '#e3f2fd',
      data: [
        ' Mattress and pillow companies',
        ' Mental health & wellness platforms',
        ' Universities and startups',
        ' Alarm clock rehabilitation centers',
      ],
    },
    {
      id: 'value-proposition',
      title: 'Value Proposition',
      icon: this.valuePropositionCardImg,
      iconBg: '#fff3e0',
      data: [
        'Helps users find the perfect time, duration, and location to nap',
        'Smart nap alerts that won’t jolt you awake like a banshee',
        'Shareable “Nap Achievements” to flex your sleep game',
      ],
    },
    {
      id: 'customer-segments',
      title: 'Customer Segments',
      icon: this.customerSegmentsCardImg,
      iconBg: '#f3e5f5',
      data: [
        'Overworked techies',
        'College students',
        'Remote workers',
        'Lazy geniuses',
      ],
    },
    {
      id: 'key-metrics',
      title: 'Key Metrics',
      icon: this.keyMetricsCardImg,
      iconBg: '#e8eaf6',
      data: ['Daily active users', 'Average Nap quality', 'Conversion rate'],
    },
    {
      id: 'problem',
      title: 'Solution',
      icon: this.solutionCardImg,
      iconBg: '#e8f5e9',
      data: [
        'Napify helps you nap smarter by suggesting ideal times and durations, waking you up gently, and tracking nap stats. ',
      ],
    },
    {
      id: 'alternatives',
      title: 'Alternatives',
      icon: this.alternativesCardImg,
      iconBg: '#ede7f6',
      data: ['Generic Alarms', 'Sleep Tracking Wearables', 'Manual Napping'],
    },
    {
      id: 'cost-structure',
      title: 'Cost Structure',
      icon: this.costStructureCardImg,
      iconBg: '#fce4ec',
      data: [
        'App development and maintenance',
        'Cloud storage (for all those dreamy naps)',
        'Marketing (especially targeting tired people)',
      ],
    },
    {
      id: 'revenue-streams',
      title: 'Revenue Streams',
      icon: this.revenueStreamsCardImg,
      iconBg: '#e0f2f1',
      data: [
        'Freemium model with paid “Nap Elite” tier (includes rain sounds, nap mask discounts)',
        'In-app purchases (pillow reviews, snore recordings)',
        'Sponsored naps from mattress brands',
      ],
    },
  ];
  // Modal State
  isEditModalOpen = false;
  selectedItemForEdit: CanvasItem | null = null;
  // Create a working copy for editing to avoid direct mutation until save
  editableItemData: string[] = [];
  regeneratePrompt: string = '';
  constructor(private cdRef: ChangeDetectorRef) {} // Inject ChangeDetectorRef

  get firstRowProblemCard(): CanvasItem[] {
    return [this.businessModelCanvas[0]]; // Problem,
  }
  get firstRowSolutionCard(): CanvasItem[] {
    return [this.businessModelCanvas[5]]; // Problem, Key Metrics
  }
  get keyPartnersData(): CanvasItem[] {
    return [
      this.businessModelCanvas[1],
      this.businessModelCanvas[2],
      // this.businessModelCanvas[3],
    ];
  }

  get keyMetricsAlternativesData(): CanvasItem[] {
    return [
      this.businessModelCanvas[4],
      this.businessModelCanvas[6],
    ];
  }

  get customerSegmentItems(): CanvasItem[]{
    return [this.businessModelCanvas[3]];
  }

  get costRevenueItems(): CanvasItem[] {
    return [this.businessModelCanvas[7], this.businessModelCanvas[8]]; // Cost Structure, Revenue Streams
  }



  // Action handlers
  onEdit(item: CanvasItem): void {
    console.log('Edit:', item.title);
    // Implement edit functionality
  }

  onDelete(item: CanvasItem): void {
    console.log('Delete:', item.title);
    // Implement delete functionality
  }

  // Modal Methods
  openEditModal(item: CanvasItem): void {
    // Create a deep copy for editing data points, or at least a shallow copy of the array
    this.selectedItemForEdit = item;
    this.editableItemData = [...item.data]; // Copy data array for editing
    this.regeneratePrompt = ''; // Reset prompt
    this.isEditModalOpen = true;
  }

  closeEditModal(): void {
    this.isEditModalOpen = false;
    this.selectedItemForEdit = null;
    this.editableItemData = [];
  }

  updateUnderstandingItem(event: Event): void {
    if (this.selectedItemForEdit) {
      // Find the original item in the businessModelCanvas and update its data
      const originalItem = this.businessModelCanvas.find(
        (canvasItem) => canvasItem.id === this.selectedItemForEdit!.id,
      );
      if (originalItem) {
        originalItem.data = [...this.editableItemData]; // Update with the edited data
        console.log('Updated Item:', originalItem);
      }
      // Potentially handle regeneratePrompt here
      if (this.regeneratePrompt) {
        console.log('Regenerate with prompt:', this.regeneratePrompt);
        this.loadingStates['submit'] = true;
        setTimeout(() => {
          this.loadingStates['submit'] = false;
          console.log('Submit completed:', event);
        }, 2000);
        // Call API or service to regenerate data based on prompt
        // For now, let's just clear it
        this.regeneratePrompt = '';
      }
      this.closeEditModal();
    }
  }

  // Methods for managing editableItemData in the modal
  addEditableDataItem(): void {
    this.editableItemData.push(''); // Add a new empty string to edit
    // Optional: Scroll to the new input or focus it
    this.cdRef.detectChanges(); // Ensure ngFor updates
    setTimeout(() => {
      const inputs = document.querySelectorAll('.edit-data-item-input');
      const lastInput = inputs[inputs.length - 1] as HTMLInputElement;
      if (lastInput) {
        lastInput.focus();
      }
    });
  }

  removeEditableDataItem(index: number): void {
    this.editableItemData.splice(index, 1);
  }

  // Required for ngFor with [(ngModel)] on primitive types like string
  trackByFn(index: number, _item: any): any {
    return index;
  }
}
