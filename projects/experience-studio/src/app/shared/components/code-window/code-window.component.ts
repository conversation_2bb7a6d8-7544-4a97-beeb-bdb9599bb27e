import {
  Component,
  HostListener,
  On<PERSON><PERSON>t,
  On<PERSON><PERSON>roy,
  ChangeDetectorRef,
  Input,
  NgZone,
  ViewChild,
  ElementRef,
  AfterViewInit,
  ChangeDetectionStrategy,
  inject,
  DestroyRef,
  signal,
} from '@angular/core';
import { Subscription, Subject, BehaviorSubject, Observable, combineLatest, firstValueFrom } from 'rxjs';
import { takeUntil, take, filter, timeout } from 'rxjs/operators';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { CommonModule } from '@angular/common';
import { Router, ActivatedRoute } from '@angular/router';
import { DomSanitizer } from '@angular/platform-browser';
import { HttpClient, HttpParams, HttpErrorResponse } from '@angular/common/http';

import { UIDesignCanvasService } from './services/ui-design-canvas.service';
import { UIDesignNode, UIDesignNodeService } from './services/ui-design-node.service';
import { UIDesignViewportService } from './services/ui-design-viewport.service';
import { CodeWindowStateService } from './services/code-window-state.service';
import { CodeWindowChatService } from './services/code-window-chat.service';
import { SafeSrcdocDirective } from '../../directives/safe-srcdoc.directive';
import {
  IconsComponent,
  LeftPanelComponent,
  RightPanelComponent,
  SplitScreenComponent,
} from '@awe/play-comp-library';
import { FileModel } from '../code-viewer/code-viewer.component';
import { SafeResourceUrl } from '@angular/platform-browser';
import { CodeGenerationService } from '../../services/code-generation.service';
import { CodeSharingService } from '../../services/code-sharing.service';

import { PollingService } from '../../services/polling.service';

import { EnhancedSSEService } from '../../services/enhanced-sse.service';
import { SSEDataProcessorService } from '../../services/sse-data-processor.service';
import { RegenerationCheckpointService } from '../../services/regeneration-checkpoint.service';
import { ThemeService } from '../../services/theme-service/theme.service';
import { AppStateService } from '../../services/app-state.service';
import { PromptBarService } from '../../services/prompt-bar-services/prompt-bar.service';
import { PromptSubmissionService } from '../../services/prompt-submission.service';
import { UserSignatureService } from '../../services/user-signature.service';
import { TextTransformationService } from '../../services/text-transformation.service';
import { StepperStateService } from '../../services/stepper-state.service';
import { ToastService } from '../../services/toast.service';
import { NewPollingResponseProcessorService } from '../../services/new-polling-response-processor.service';
import {
  VSCodeExportService,
  VSCodeExportResult,
} from '../../services/vscode-export/vscode-export.service';
import { FileTreePersistenceService } from '../../services/file-tree-persistence.service';
import { MonacoStateManagementService } from '../../services/monaco-state-management.service';
import { ArtifactsService } from '../../services/artifacts.service';
import { SSEConnectionDeduplicationService } from '../../services/sse-connection-deduplication.service';

import {
  GenerateUIDesignService,
  UIDesignResponseData,
} from '../../services/generate-ui-design.service';
import {
  UIDesignSelectionService,
  MultiSelectedNodeData,
} from '../../services/ui-design-selection.service';
import { UIDesignEditService } from '../../services/ui-design-edit.service';
import { UIDesignNodePositioningService } from './services/ui-design-node-positioning.service';
import { UIDesignVisualFeedbackService } from '../../services/ui-design-visual-feedback.service';
import { WireframeGenerationStateService } from '../../services/wireframe-generation-state.service';
import { GenerationStateService } from '../../services/generation-state.service';
import { TemplateLoadingService } from '../../services/template-loading.service';
import { NavigationCleanupService } from '../../services/navigation-cleanup.service';
import { FilenameNormalizationService } from '../../services/filename-normalization.service';
import { createLogger } from '../../utils';
import { UIDesignFilenameTransformerService } from '../../services/ui-design-filename-transformer.service';
import { WireframeNodeManagementService } from '../../services/wireframe-node-management.service';
import { UIDesignIntroService, IntroMessageState } from '../../services/ui-design-intro.service';
import { CodeGenerationIntroService } from '../../services/code-generation-intro.service';
import { ProjectLoadingService, ProjectLoadingResponse } from '../../services/project-loading.service';
import { ProjectLoadingParser } from '../../utils/project-loading-parser.util';
import {
  CodeWindowInitializationData,
  ParsedProjectData,
  ChatWindowMessage,
  ProjectLoadingState
} from '../../interfaces/project-loading.interfaces';

import { SequentialRegenerationService } from '../../services/sequential-regeneration.service';
import { FileOpeningService } from '../../services/file-opening.service';

import { RegenerationIntegrationService } from '../../services/regeneration-integration.service';
import { GenerationResult } from '../generation-accordion/generation-accordion.component';

import { CodeViewerComponent } from '../code-viewer/code-viewer.component';
import { LoadingAnimationComponent } from './loading-animation/loading-animation.component';
import { LayoutIdentifiedAnimationComponent } from './layout-animation/layout-identified-animation.component';
import { AnalyzingLayoutAnimationComponent } from '../analyzing-layout-animation/analyzing-layout-animation.component';
import { AnalyzingDesignTokensAnimationComponent } from '../analyzing-design-tokens-animation/analyzing-design-tokens-animation.component';
import { MarkdownModule } from 'ngx-markdown';
import { ChatWindowComponent } from '../chat-window/chat-window.component';
import { ErrorPageComponent } from '../error-page/error-page.component';
import { CanvasInfoComponent } from './components/canvas-info/canvas-info.component';
import { MobileFrameComponent, MobilePage } from '../mobile-frame/mobile-frame.component';
import { WebFrameComponent, WebPage } from '../web-frame/web-frame.component';

import {
  ProgressState,
  StatusType,
  FileData,
  DesignTokensData,
  ProjectInfo,
} from '../../models/polling-response.interface';
import {
  DesignTokenEditState,
  DesignTokenLoadingState
} from '../../models/design-tokens.model';

import JSZip from 'jszip';
import {
  ALL_DESIGN_TOKENS,
  isValidHexColor as validateHexColor,
} from '../../data/design-tokens.data';
import { environment } from '../../../../environments/environment';

type IconStatus = 'default' | 'active' | 'disable';

export interface UIDesignAPIResponse {
  pageName: string;
  content: string;
}

export interface WireframeAPIResponse {
  fileName: string;
  content: string;
}

export interface UIDesignPageData {
  fileName: string;
  content: string;
}

export interface PreviewTabState {
  isVisible: boolean;
  isEnabled: boolean;
  isLoading: boolean;
  hasError: boolean;
  errorMessage?: string;
  loadingMessage?: string;
  tooltipMessage: string;
}

export interface CodeTabState {
  isVisible: boolean;
  isEnabled: boolean;
  isLoading: boolean;
  hasError: boolean;
  errorMessage?: string;
  loadingMessage?: string;
  tooltipMessage: string;
}

export enum PreviewTabStatus {
  HIDDEN = 'hidden',
  DISABLED = 'disabled',
  LOADING = 'loading',
  ENABLED = 'enabled',
  ERROR = 'error'
}

interface DesignToken {
  id: string;
  name: string;
  value: string;
  type: 'color' | 'typography' | 'spacing' | 'size';
  category: string;
  editable: boolean;
}

@Component({
  selector: 'app-code-window',
  standalone: true,
  imports: [
    CommonModule,
    SplitScreenComponent,
    LeftPanelComponent,
    RightPanelComponent,
    IconsComponent,
    CodeViewerComponent,
    LoadingAnimationComponent,
    LayoutIdentifiedAnimationComponent,
    AnalyzingLayoutAnimationComponent,
    AnalyzingDesignTokensAnimationComponent,
    ChatWindowComponent,
    ErrorPageComponent,
    MarkdownModule,
    CanvasInfoComponent,
    MobileFrameComponent,
    WebFrameComponent,
    SafeSrcdocDirective,
  ],
  templateUrl: './code-window.component.html',
  styleUrls: ['./code-window.component.scss', './artifacts-view.scss', './code-viewer.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CodeWindowComponent implements OnInit, AfterViewInit, OnDestroy {
  private destroy$ = new Subject<void>();
  private logger = createLogger('CodeWindowComponent');

  private files$ = new BehaviorSubject<FileModel[]>([]);
  private isResizing$ = new BehaviorSubject<boolean>(false);
  private currentView$ = new BehaviorSubject<
    'loading' | 'editor' | 'preview' | 'overview' | 'logs' | 'artifacts'
  >('preview');
  private isPreviewLoading$ = new BehaviorSubject<boolean>(false);
  private previewIcon$ = new BehaviorSubject<string>('bi-code-slash');
  private previewError$ = new BehaviorSubject<boolean>(false);
  private errorDescription$ = new BehaviorSubject<string>('Please try again later.');
  private errorTerminalOutput$ = new BehaviorSubject<string>('');
  private currentTheme$ = new BehaviorSubject<'light' | 'dark'>('light');

  public isRegenerationInProgress$ = new BehaviorSubject<boolean>(false);
  private regenerationStartTime: number = 0;

  private isRegenerationCallInProgress = false;

  private currentCheckpointSession = signal<string | null>(null);
  private checkpointProcessedEvents = signal<number>(0);

  private regenerationTimeoutTimer: number | null = null;
  private readonly REGENERATION_TIMEOUT_MS = 1800000;

  private isCodeGenerationLoading$ = new BehaviorSubject<boolean>(false);

  codeRegenerationProgressDescription: string = '';

  private lastUserRequest: string = '';

  private generationVersionCounter: number = 0;
  private hasAddedInitialGenerationAccordion: boolean = false;
  private isProjectLoadingMode: boolean = false;

  private deployedUrl$ = new BehaviorSubject<string | null>('');
  private isPanelCollapsed$ = new BehaviorSubject<boolean>(false);
  private isLoading$ = new BehaviorSubject<boolean>(true);
  private isHistoryActive$ = new BehaviorSubject<boolean>(false);
  private isCodeActive$ = new BehaviorSubject<boolean>(false);
  private isPreviewActive$ = new BehaviorSubject<boolean>(true);

  private isArtifactsActive$ = new BehaviorSubject<boolean>(false);
  private isLeftPanelCollapsed$ = new BehaviorSubject<boolean>(false);
  private minWidth$ = new BehaviorSubject<string>('300px');
  private isExperienceStudioModalOpen$ = new BehaviorSubject<boolean>(false);
  private checkSessionStorageInterval: any;
  private shouldHideProjectName$ = new BehaviorSubject<boolean>(false);


  private isDownloadLoading$ = new BehaviorSubject<boolean>(false);


  private isCloneLoading$ = new BehaviorSubject<boolean>(false);

  private isUIDesignMode$ = new BehaviorSubject<boolean>(false);
  private uiDesignNodes$ = new BehaviorSubject<UIDesignNode[]>([]);
  private selectedUIDesignNode$ = new BehaviorSubject<UIDesignNode | null>(null);

  // Project preview mode properties
  private isProjectPreviewMode$ = new BehaviorSubject<boolean>(false);
  private projectData$ = new BehaviorSubject<any>(null);
  private isProjectDataLoading$ = new BehaviorSubject<boolean>(false);

  // Project loading mode properties
  private isProjectLoadingMode$ = new BehaviorSubject<boolean>(false);
  private projectLoadingData$ = new BehaviorSubject<ParsedProjectData | null>(null);
  private projectLoadingState$ = new BehaviorSubject<ProjectLoadingState>('idle');
  private projectLoadingError$ = new BehaviorSubject<string | null>(null);
  private pendingRegenerationMessages: ChatWindowMessage[] = [];
  public artifactsLogs: any[] = []; // Logs for artifacts tab
  private isUIDesignFullScreenOpen$ = new BehaviorSubject<boolean>(false);
  private uiDesignViewMode$ = new BehaviorSubject<'mobile' | 'web'>('mobile');
  private isUIDesignModalFullScreen$ = new BehaviorSubject<boolean>(false);
  private isUIDesignCodeViewerOpen$ = new BehaviorSubject<boolean>(false);

  private isUIDesignGenerating$ = new BehaviorSubject<boolean>(false);
  private uiDesignError$ = new BehaviorSubject<string | null>(null);
  private uiDesignApiInProgress$ = new BehaviorSubject<boolean>(false);
  private isWireframeGenerationComplete$ = new BehaviorSubject<boolean>(false);

  private isUIDesignRegenerating$ = new BehaviorSubject<boolean>(false);
  private uiDesignLoadingNodes$ = new BehaviorSubject<UIDesignNode[]>([]);

  private showCanvasTooltip$ = new BehaviorSubject<boolean>(true);
  private isEditingUIDesign$ = new BehaviorSubject<boolean>(false);

  private introMessageState$ = new BehaviorSubject<IntroMessageState>({
    isLoading: false,
    text: '',
    isTyping: false,
    hasError: false,
    shouldReplaceText: false,
    introAPICompleted: false,
    mainAPIInProgress: false,
    showLoadingIndicator: false,
    loadingPhase: 'intro',
    messageType: 'generation',
  });

  private activeAIMessageIds = new Set<string>();
  private currentActiveMessageId: string | null = null;

  private regenerationSessionCounter = 0;
  private activeRegenerationSessions = new Map<
    string,
    {
      aiMessageId: string;
      timestamp: number;
      isActive: boolean;
      sessionId?: string;
      messageId?: string;
      prompt?: string;
      selectedNodes?: MultiSelectedNodeData[];
    }
  >();

  private showUIDesignOverviewTab$ = new BehaviorSubject<boolean>(false);
  private uiDesignPages$ = new BehaviorSubject<MobilePage[]>([]);
  private currentUIDesignPageIndex$ = new BehaviorSubject<number>(0);

  private isUIDesignLoading$ = new BehaviorSubject<boolean>(false);

  private useSSE = true;
  private sseSubscription: Subscription | null = null;
  private sseDataProcessorSubscription: Subscription | null = null;

  readonly shouldHideProjectName = this.shouldHideProjectName$.asObservable();
  readonly isUIDesignMode = this.isUIDesignMode$.asObservable();
  readonly uiDesignNodes = this.uiDesignNodes$.asObservable();
  readonly selectedUIDesignNode = this.selectedUIDesignNode$.asObservable();
  readonly isUIDesignFullScreenOpen = this.isUIDesignFullScreenOpen$.asObservable();
  readonly uiDesignViewMode = this.uiDesignViewMode$.asObservable();
  readonly isUIDesignModalFullScreen = this.isUIDesignModalFullScreen$.asObservable();
  readonly isUIDesignCodeViewerOpen = this.isUIDesignCodeViewerOpen$.asObservable();

  readonly isUIDesignGenerating = this.isUIDesignGenerating$.asObservable();
  readonly uiDesignError = this.uiDesignError$.asObservable();
  readonly uiDesignApiInProgress = this.uiDesignApiInProgress$.asObservable();
  readonly isWireframeGenerationComplete = this.isWireframeGenerationComplete$.asObservable();
  readonly isUIDesignRegenerating = this.isUIDesignRegenerating$.asObservable();
  readonly uiDesignLoadingNodes = this.uiDesignLoadingNodes$.asObservable();
  readonly isUIDesignLoading = this.isUIDesignLoading$.asObservable();

  // Project preview mode observables
  readonly isProjectPreviewMode = this.isProjectPreviewMode$.asObservable();
  readonly projectData = this.projectData$.asObservable();
  readonly isProjectDataLoading = this.isProjectDataLoading$.asObservable();

  readonly showCanvasTooltip = this.showCanvasTooltip$.asObservable();
  readonly isEditingUIDesign = this.isEditingUIDesign$.asObservable();

  readonly introMessageState = this.introMessageState$.asObservable();

  // Observables for viewport selection and web pages
  readonly webPages$ = new BehaviorSubject<WebPage[]>([]);
  readonly currentWebPageIndex$ = new BehaviorSubject<number>(0);
  currentViewportMode!: Observable<'mobile' | 'web'>;

  readonly showUIDesignOverviewTab = this.showUIDesignOverviewTab$.asObservable();
  readonly uiDesignPages = this.uiDesignPages$.asObservable();
  readonly currentUIDesignPageIndex = this.currentUIDesignPageIndex$.asObservable();

  private userSelectedTab: boolean = false;

  readonly files = this.files$.asObservable();
  readonly isResizing = this.isResizing$.asObservable();
  readonly currentView = this.currentView$.asObservable();
  readonly isPreviewLoading = this.isPreviewLoading$.asObservable();
  readonly previewIcon = this.previewIcon$.asObservable();
  readonly previewError = this.previewError$.asObservable();
  readonly errorDescription = this.errorDescription$.asObservable();
  readonly errorTerminalOutput = this.errorTerminalOutput$.asObservable();
  readonly currentTheme = this.currentTheme$.asObservable();
  readonly deployedUrl = this.deployedUrl$.asObservable();
  readonly isPanelCollapsed = this.isPanelCollapsed$.asObservable();
  readonly isLoading = this.isLoading$.asObservable();
  readonly isHistoryActive = this.isHistoryActive$.asObservable();
  readonly isCodeActive = this.isCodeActive$.asObservable();
  readonly isPreviewActive = this.isPreviewActive$.asObservable();

  readonly isArtifactsActive = this.isArtifactsActive$.asObservable();
  readonly isLeftPanelCollapsed = this.isLeftPanelCollapsed$.asObservable();
  readonly minWidth = this.minWidth$.asObservable();
  readonly isExperienceStudioModalOpen = this.isExperienceStudioModalOpen$.asObservable();

  projectName: string | null = null;
  isProjectNameLoading: boolean = true;

  isArtifactsTabEnabled: boolean = false;

  artifactsData: any[] = [];

  isArtifactsTabEnabledWithLogs: boolean = false;

  isLogsFooterExpanded: boolean = false;

  readonly tabTransitionInProgress = signal(false);
  readonly currentTabState = signal<{
    activeTab: string;
    isTransitioning: boolean;
    lastError: string | null;
  }>({
    activeTab: 'preview',
    isTransitioning: false,
    lastError: null
  });

  private destroyRef!: DestroyRef;

  private loadedArtifacts: Set<string> = new Set();

  private persistentArtifacts: Map<string, any> = new Map();

  isArtifactsTabVisible: boolean = true;

  hasLayoutAnalyzed: boolean = false;
  hasDesignSystem: boolean = false;
  selectedArtifactFile: any = null;

  isAnalyzingLayout: boolean = true;
  hasLayoutBeenDetected: boolean = false;

  private typewriterTimeouts: { [key: string]: any } = {};
  private artifactTypingSpeed: number = 3;

  artifactTypewriterStates: {
    [key: string]: {
      visibleContent: string;
      isTyping: boolean;
      fullContent: string;
    };
  } = {};

  designSystemData: any = null;
  designTokens: DesignToken[] = [];
  layoutAnalyzedData: any[] = [];

  private designTokenEditState$ = new BehaviorSubject<DesignTokenEditState>({
    isEditMode: false,
    editButtonText: 'Edit',
    hasUnsavedChanges: false,
    originalValues: new Map<string, string>()
  });

  private designTokenLoadingState$ = new BehaviorSubject<DesignTokenLoadingState>({
    isLoading: false,
    showAnalyzingAnimation: false,
    hasReceivedTokens: false,
    loadingMessage: 'Analyzing Design Tokens...'
  });

  readonly designTokenEditState = this.designTokenEditState$.asObservable();
  readonly designTokenLoadingState = this.designTokenLoadingState$.asObservable();

  currentProgressState: string = '';

  lastProgressDescription: string = '';
  pollingStatus: string = 'PENDING';

  currentProgress: ProgressState | null = null;
  currentStatus: StatusType | null = null;
  newProgressDescription: string = '';
  newLogContent: string = '';
  newArtifactData: any = null;
  newFileList: string[] = [];
  newCodeFiles: FileData[] = [];
  newPreviewUrl: string = '';
  isNewPreviewEnabled: boolean = false;

  isPromptBarEnabled: boolean = false;

  private hasNewPollingResponseUrl = false;

  private isIframeReady$ = new BehaviorSubject<boolean>(false);
  private isUrlValidated$ = new BehaviorSubject<boolean>(false);
  private urlValidationError$ = new BehaviorSubject<string>('');
  private isUrlAvailable$ = new BehaviorSubject<boolean>(false);

  previewUrl: string = '';

  currentProjectInfo: ProjectInfo | null = null;

  private autoSwitchToLogsTimer: any;

  layoutExampleImages: string[] = [
    'assets/images/01.png',
    'assets/images/02.png',
    'assets/images/03.png',
    'assets/images/04.png',
    'assets/images/05.png',
    'assets/images/06.png',
    'assets/images/07.png',
    'assets/images/08.png',
  ];

  layoutMapping: { [key: string]: string } = {
    HB: 'Header + Body (No Sidebars, No Footer)',
    HBF: 'Header + Body + Footer (No Sidebars)',
    HLSB: 'Header + Left Sidebar + Body (No Footer)',
    HLSBF: 'Header + Left Sidebar + Body + Footer',
    HBRS: 'Header + Body + Right Sidebar (No Footer)',
    HBRSF: 'Header + Body + Right Sidebar + Footer',
    HLSBRS: 'Header + Left Sidebar + Body + Right Sidebar (No Footer)',
    HLSBRSF: 'Header + Left Sidebar + Body + Right Sidebar + Footer',
  };

  layoutData: string[] = ['HB', 'HBF'];

  isLayoutLoading: boolean = true;

  private detectedLayoutFromPrevMetadata: string | null = null;
  private previousProgressState: string | null = null;
  private shouldShowLayoutArtifact = false;

  public isFailedState = false;
  private previewTabName$ = new BehaviorSubject<string>('Preview');

  private previewTabState$ = new BehaviorSubject<PreviewTabState>({
    isVisible: true,
    isEnabled: false,
    isLoading: false,
    hasError: false,
    tooltipMessage: 'Preview will be available once the application is deployed'
  });
  private previewTabStatus$ = new BehaviorSubject<PreviewTabStatus>(PreviewTabStatus.DISABLED);

  private codeTabState$ = new BehaviorSubject<CodeTabState>({
    isVisible: true,
    isEnabled: false,
    isLoading: false,
    hasError: false,
    tooltipMessage: 'Code will be available once generated'
  });

  selectedImageDataUri: string = '';

  lightMessages: {
    id?: string;
    text: string;
    from: 'user' | 'ai';
    theme: 'light';
    hasSteps?: boolean;
    imageDataUri?: string;
    isError?: boolean;
    errorDetails?: string;
    isErrorExpanded?: boolean;
    showIntroMessage?: boolean;
    originalText?: string;

    showLoadingIndicator?: boolean;
    loadingPhase?: 'intro' | 'main' | 'completed' | 'error';
    mainAPIInProgress?: boolean;

    isTyping?: boolean;
  }[] = [];

  darkMessages: {
    text: string;
    from: 'user' | 'ai';
    theme: 'dark';
  }[] = [];

  lightPrompt: string = '';
  darkPrompt: string = '';

  projectId: string | null = null;
  jobId: string | null = null;
  isPolling = false;
  isCodeGenerationComplete = false;
  isCodeTabEnabled = false;


  isViewingSeedProjectTemplate = false;
  isPreviewTabEnabled = false;
  isLogsTabEnabled = false;
  image = 'assets/images/history_card.png';
  title = 'Version 1 Application';
  appName: string | null = null;

  hasLogs: boolean = false;
  logMessages: string[] = [];
  formattedLogMessages: any[] = [];
  isStreamingLogs: boolean = false;
  isTypingLog: boolean = false;
  currentLogIndex: number = 0;
  private logStreamTimer: any;

  private processedLogHashes: Set<string> = new Set();
  private lastProgressState: string = '';
  private expandedCodeLogs: Set<string> = new Set();
  private logsTabAutoEnabled: boolean = false;

  loadingMessages = [
    'Analyzing your requirements and design specifications',
    'Identifying key UI/UX patterns from your requirements',
    'Mapping component relationships and dependencies',
    'Generating component architecture and file structure',
    'Planning optimal folder organization for scalability',
    'Creating responsive UI components with modern best practices',
    'Implementing accessibility standards for inclusive design',
    'Building reusable component library for consistency',
    'Implementing interactive elements and event handlers',
    'Adding form validation and user input handling',
    'Optimizing code for performance and maintainability',
    'Implementing lazy loading for improved initial load time',
    'Adding CSS styling and layout configurations',
    'Creating responsive breakpoints for all device sizes',
    'Implementing data flow and state management',
    'Setting up API integration and data fetching logic',
    'Configuring error handling and fallback states',
    'Ensuring cross browser compatibility and responsive design',
    'Implementing animations and transitions for better UX',
    'Optimizing assets and resources for faster loading',
    'Finalizing code with proper documentation and comments',
    'Running code quality checks and optimizations',
    'Preparing preview deployment for your application',
    'Setting up build configuration for optimal performance',
    'Finalizing application bundle for deployment',
  ];

  private transformLoadingMessages(): void {

    this.loadingMessages = this.textTransformationService.transformMessages(this.loadingMessages);
  }

  generatedCode: any;
  private subscription: Subscription | null = null;

  selectedElement: any = null;
  showEditorIcon = false;
  urlSafe?: SafeResourceUrl;

  showPreview: boolean = false;
  previewImage: { url: string; name: string } | null = null;

  @ViewChild(ChatWindowComponent) chatWindow!: ChatWindowComponent;

  @ViewChild(CodeViewerComponent) codeViewer!: CodeViewerComponent;

  @ViewChild('uiDesignCanvas', { static: false }) uiDesignCanvas!: ElementRef;

  @ViewChild('canvasContent', { static: false }) canvasContent!: ElementRef;

  private resizeTimeout: any;
  private canvasResizeObserver?: ResizeObserver;
  private wheelEventListener?: (event: WheelEvent) => void;

  readonly isIframeReady = this.isIframeReady$.asObservable();
  readonly isUrlValidated = this.isUrlValidated$.asObservable();
  readonly urlValidationError = this.urlValidationError$.asObservable();
  readonly isUrlAvailable = this.isUrlAvailable$.asObservable();

  constructor(
    private codeSharingService: CodeSharingService,
    private codeGenerationService: CodeGenerationService,
    private themeService: ThemeService,
    private cdr: ChangeDetectorRef,
    public sanitizer: DomSanitizer,
    private pollingService: PollingService,

    private enhancedSSEService: EnhancedSSEService,
    private sseDataProcessor: SSEDataProcessorService,
    private newPollingResponseProcessor: NewPollingResponseProcessorService,

    private appStateService: AppStateService,
    private ngZone: NgZone,
    private promptService: PromptBarService,
    private promptSubmissionService: PromptSubmissionService,
    private router: Router,
    private userSignatureService: UserSignatureService,
    private textTransformationService: TextTransformationService,
    private stepperStateService: StepperStateService,
    private toastService: ToastService,
    private vscodeExportService: VSCodeExportService,
    private fileTreePersistenceService: FileTreePersistenceService,
    private monacoStateManagementService: MonacoStateManagementService,
    private artifactsService: ArtifactsService,
    private sseConnectionDeduplicationService: SSEConnectionDeduplicationService,
    private codeWindowStateService: CodeWindowStateService,
    private codeWindowChatService: CodeWindowChatService,
    private uiDesignNodeService: UIDesignNodeService,

    private route: ActivatedRoute,
    private uiDesignCanvasService: UIDesignCanvasService,
    private uiDesignViewportService: UIDesignViewportService,
    private generateUIDesignService: GenerateUIDesignService,
    private uiDesignSelectionService: UIDesignSelectionService,
    private uiDesignEditService: UIDesignEditService,
    private uiDesignNodePositioningService: UIDesignNodePositioningService,
    public uiDesignVisualFeedbackService: UIDesignVisualFeedbackService,
    public wireframeGenerationStateService: WireframeGenerationStateService,
    private generationStateService: GenerationStateService,
    private templateLoadingService: TemplateLoadingService,
    private navigationCleanupService: NavigationCleanupService,
    private filenameNormalizationService: FilenameNormalizationService,
    private uiDesignFilenameTransformerService: UIDesignFilenameTransformerService,
    private wireframeNodeManagementService: WireframeNodeManagementService,
    private uiDesignIntroService: UIDesignIntroService,
    private codeGenerationIntroService: CodeGenerationIntroService,

    private sequentialRegenerationService: SequentialRegenerationService,
    private regenerationCheckpointService: RegenerationCheckpointService,
    private fileOpeningService: FileOpeningService,
    private regenerationIntegrationService: RegenerationIntegrationService,

    private http: HttpClient,
    private projectLoadingService: ProjectLoadingService
  ) {

    this.destroyRef = inject(DestroyRef);

    this.currentTheme$.next(this.themeService.getCurrentTheme());

    this.setupAutomaticErrorHandling();

    this.transformLoadingMessages();
  }

  navigateToHome(): void {
    try {

      this.cleanupSSEConnections();


      this.pollingService.resetLogs();
      if (this.isPolling) {
        this.pollingService.stopPolling();
        this.isPolling = false;
      }


      this.resetPollingProcessors();


      this.cleanupUIStateServices();


      this.cleanupEditorAndFileState();


      this.cleanupChatState();


      this.resetComponentState();


      this.cleanupGlobalState();


      this.toastService.clear();


      this.toastService.info('Returning to home page');
      this.router.navigate(['/experience']);

    } catch (error) {


      this.toastService.error('Some cleanup operations failed, but navigation will continue');
      this.router.navigate(['/experience']);
    }
  }


  private cleanupSSEConnections(): void {
    try {

      if (this.enhancedSSEService.isMonitoring()) {
        this.enhancedSSEService.stopMonitoring();
      }


      this.sseConnectionDeduplicationService.closeAllConnections('Navigation to home');

    } catch (error) {
    }
  }


  private cleanupUIStateServices(): void {
    try {

      this.uiDesignNodeService.clearAll();


      this.uiDesignCanvasService.clear();
      this.uiDesignCanvasService.resetViewport();


      this.codeWindowStateService.resetComponentState();

    } catch (error) {
    }
  }


  private cleanupEditorAndFileState(): void {
    try {

      this.monacoStateManagementService.reset();


      this.fileTreePersistenceService.reset();
      this.fileTreePersistenceService.clearPersistedState();

    } catch (error) {
    }
  }


  private cleanupChatState(): void {
    try {

      this.codeWindowChatService.clearChatHistory();


      if (this.chatWindow) {
        this.chatWindow.clearAllMessages();
      }


      this.stepperStateService.triggerStepperReset();

    } catch (error) {
    }
  }


  private cleanupGlobalState(): void {
    try {

      this.appStateService.resetProjectState();


      this.promptSubmissionService.resetSubmissionState();


      this.codeSharingService.resetState();


      this.promptService.setImage(null);
      this.promptService.setPrompt('');


      this.artifactsService.resetState();


      this.clearPersistedStorageData();

    } catch (error) {
    }
  }


  private resetPollingProcessors(): void {
    try {

      this.newPollingResponseProcessor.reset(false);


      this.newPollingResponseProcessor.clearProcessedArtifacts(false);


      if (this.sseDataProcessor) {
        this.sseDataProcessor.reset();
      }

    } catch (error) {
    }
  }


  private clearPersistedStorageData(): void {
    try {

      const sessionStorageKeys = [
        'codeWindowState',
        'artifactsData',
        'designSystemData',
        'layoutAnalyzedData',
        'projectInfo',
        'previewUrl',
        'deployedUrl',
        'codeFiles',
        'fileTree',
        'monacoState',
        'editorState',
        'uiDesignNodes',
        'canvasState'
      ];

      sessionStorageKeys.forEach(key => {
        try {
          sessionStorage.removeItem(key);
        } catch (error) {

        }
      });


      const localStorageKeys = [
        'codeWindowPreferences',
        'artifactsPreferences',
        'designTokens',
        'layoutPreferences'
      ];

      localStorageKeys.forEach(key => {
        try {
          localStorage.removeItem(key);
        } catch (error) {

        }
      });

    } catch (error) {
    }
  }

  private resetComponentState(): void {

    this.files$.next([]);
    this.isCodeGenerationComplete = false;
    this.isPreviewLoading$.next(true);
    this.previewError$.next(false);
    this.layoutData = [];
    this.generatedCode = null;

    this.logMessages = [];
    this.formattedLogMessages = [];
    this.hasLogs = false;
    this.isStreamingLogs = false;
    this.isTypingLog = false;
    this.currentLogIndex = 0;

    this.processedLogHashes.clear();
    this.lastProgressState = '';
    this.expandedCodeLogs.clear();
    this.logsTabAutoEnabled = false;

    this.selectedImageDataUri = '';
    this.currentProgressState = '';
    this.lastProgressDescription = '';
    this.pollingStatus = 'PENDING';
    this.userSelectedTab = false;
    this.isResizing$.next(false);
    this.isPanelCollapsed$.next(false);

    this.isPromptBarEnabled = false;
    this.isLoading$.next(true);
    this.isHistoryActive$.next(false);
    this.isLeftPanelCollapsed$.next(false);
    this.isExperienceStudioModalOpen$.next(false);

    this.isPreviewActive$.next(true);
    this.isCodeActive$.next(false);

    this.isArtifactsActive$.next(false);
    this.isPreviewTabEnabled = true;
    this.isLogsTabEnabled = true;
    this.isCodeTabEnabled = false;
    this.isArtifactsTabEnabled = false;
    this.isArtifactsTabEnabledWithLogs = false;


    if (this.persistentArtifacts && this.persistentArtifacts.size > 0) {
      this.resetArtifactsWithPersistence();
    } else {
      this.resetArtifactsCompletely();
    }
    this.selectedArtifactFile = null;

    if (!this.persistentArtifacts || this.persistentArtifacts.size === 0) {
      this.designSystemData = null;
      this.designTokens = [];
    }


    this.projectName = null;
    this.isProjectNameLoading = true;
    this.shouldHideProjectName$.next(false);


    this.resetPreviewStateCompletely();


    this.isAnalyzingLayout = true;
    this.hasLayoutBeenDetected = false;
    this.layoutAnalyzedData = [];
    this.detectedLayoutFromPrevMetadata = null;
    this.shouldShowLayoutArtifact = false;


    this.hasDesignSystem = false;
    this.hasLayoutAnalyzed = false;


    this.designTokenEditState$.next({
      isEditMode: false,
      editButtonText: 'Edit',
      hasUnsavedChanges: false,
      originalValues: new Map<string, string>()
    });

    this.designTokenLoadingState$.next({
      isLoading: false,
      showAnalyzingAnimation: false,
      hasReceivedTokens: false,
      loadingMessage: 'Analyzing Design Tokens...'
    });


    this.previewTabState$.next({
      isVisible: true,
      isEnabled: false,
      isLoading: false,
      hasError: false,
      tooltipMessage: 'Preview will be available once the application is deployed'
    });

    this.previewTabName$.next('Preview');
    this.isFailedState = false;


    this.artifactTypewriterStates = {};
    Object.keys(this.typewriterTimeouts).forEach(key => {
      clearTimeout(this.typewriterTimeouts[key]);
    });
    this.typewriterTimeouts = {};

    this.isAnalyzingLayout = true;
    this.hasLayoutBeenDetected = false;

    this.detectedLayoutFromPrevMetadata = null;
    this.previousProgressState = null;
    this.shouldShowLayoutArtifact = false;

    this.isFailedState = false;
    this.previewTabName$.next('Preview');

    this.setPreviewTabDisabled('Preview will be available once the application is deployed');

    this.setCodeTabDisabled('Code will be available once generated');


    this.isViewingSeedProjectTemplate = false;

    this.hasNewPollingResponseUrl = false;

    this.currentView$.next('preview');

    this.lightMessages = [];
    this.darkMessages = [];
    this.lightPrompt = '';
    this.darkPrompt = '';

    this.selectedImageDataUri = '';

    this.previewError$.next(false);
    this.errorDescription$.next(
      'Even the best systems have bad days sometimes. Please try again later.'
    );
    this.errorTerminalOutput$.next('');

    this.projectId = null;
    this.jobId = null;
    this.appName = null;
    this.projectName = null;
    this.isProjectNameLoading = true;

    this.selectedElement = null;
    this.showEditorIcon = false;

    if (this.autoSwitchToLogsTimer) {
      clearTimeout(this.autoSwitchToLogsTimer);
      this.autoSwitchToLogsTimer = null;
    }

    if (this.logStreamTimer) {
      clearInterval(this.logStreamTimer);
      this.logStreamTimer = null;
    }

    if (this.designTokensUpdateTimer) {
      clearTimeout(this.designTokensUpdateTimer);
      this.designTokensUpdateTimer = null;
    }
  }

  openPreviewInNewTab(): void {
    const currentUrl = this.deployedUrl$.value;
    if (currentUrl) {
      window.open(currentUrl, '_blank');
      this.toastService.info('Opening preview in new tab');
    } else {
      this.toastService.warning('Preview URL is not available yet');
    }
  }

  debugUrlState(): void {
  }

  showImagePreview(imageUrl: string, imageName: string = 'Image'): void {
    this.previewImage = { url: imageUrl, name: imageName };
    this.showPreview = true;
  }

  closeImagePreview(): void {
    this.showPreview = false;
    this.previewImage = null;
  }

  private processArtifactData(artifactData: any): void {
    if (!artifactData) {
      return;
    }

    switch (artifactData.type) {
      case 'readme':
        this.processProjectOverviewArtifact(artifactData);
        break;
      case 'layout':
        this.processLayoutAnalyzedArtifact(artifactData);


        this.storePersistentArtifact('layout_analyzed', artifactData);
        break;
      case 'design-tokens':
        this.processDesignSystemArtifact(artifactData);


        this.storePersistentArtifact('design_system', artifactData);
        break;
      default:
    }

  }

  private storePersistentArtifact(key: string, artifactData: any): void {
    this.persistentArtifacts.set(key, {
      ...artifactData,
      timestamp: Date.now(),
      persistent: true
    });
  }


  private resetArtifactsCompletely(): void {

    this.artifactsData = [];
    this.loadedArtifacts.clear();
    this.hasLayoutAnalyzed = false;
    this.hasDesignSystem = false;


    this.persistentArtifacts.clear();


    this.isArtifactsTabVisible = true;
    this.selectedArtifactFile = null;


    this.layoutAnalyzedData = [];


    this.designSystemData = null;
    this.designTokens = [];
  }


  private resetPreviewStateCompletely(): void {

    this.deployedUrl$.next(null);
    this.urlSafe = undefined;
    this.previewUrl = '';
    this.newPreviewUrl = '';


    this.hasNewPollingResponseUrl = false;
    this.isNewPreviewEnabled = false;


    this.isPreviewLoading$.next(true);
    this.previewError$.next(false);
    this.errorDescription$.next('Please try again later.');
    this.errorTerminalOutput$.next('');


    this.previewIcon$.next('bi-code-slash');


    this.isPreviewTabEnabled = true;


    this.isIframeReady$.next(false);
    this.isUrlValidated$.next(false);
    this.isUrlAvailable$.next(false);
    this.urlValidationError$.next('');
  }

  private resetArtifactsWithPersistence(): void {

    this.artifactsData = [];
    this.loadedArtifacts.clear();
    this.hasLayoutAnalyzed = false;
    this.hasDesignSystem = false;


    if (this.persistentArtifacts.size > 0) {
      this.restorePersistentArtifacts();
    }
  }

  private initializePersistentArtifacts(): void {

    if (!this.persistentArtifacts) {
      this.persistentArtifacts = new Map();
    }

  }


  private restorePersistentArtifacts(): void {
    for (const [key, artifactData] of this.persistentArtifacts.entries()) {
      switch (key) {
        case 'layout_analyzed':
          this.processLayoutAnalyzedArtifact(artifactData);

          this.hasLayoutAnalyzed = true;
          break;
        case 'design_system':

          if (artifactData && typeof artifactData === 'object') {
            this.designSystemData = artifactData;
            this.hasDesignSystem = true;
          }
          break;
      }
    }
  }

  private enableArtifactsTabIfNeeded(): void {

    this.removeDuplicateArtifacts();

    const hasArtifacts = this.artifactsData.length > 0;
    const hasLogsAvailable = this.hasLogs || this.formattedLogMessages.length > 0;

    this.addLogsFileEntry(hasLogsAvailable);

    this.isArtifactsTabEnabled = hasArtifacts || hasLogsAvailable;
    this.isArtifactsTabEnabledWithLogs = hasArtifacts || hasLogsAvailable;

    if (hasLogsAvailable && !hasArtifacts) {
    }

    this.ensureLogsFileAtBottom();

    this.cdr.detectChanges();
  }

  private removeDuplicateArtifacts(): void {
    const seen = new Set<string>();
    const originalLength = this.artifactsData.length;

    this.artifactsData = this.artifactsData.filter(artifact => {
      if (seen.has(artifact.name)) {
        return false;
      }
      seen.add(artifact.name);
      return true;
    });

    if (this.artifactsData.length !== originalLength) {

      this.ensureLogsFileAtBottom();

      this.cdr.detectChanges();
    }
  }

  private addLogsFileEntry(hasLogsAvailable: boolean): void {
    const logsFileName = 'Application Logs';
    const existingLogsIndex = this.artifactsData.findIndex(artifact => artifact.name === logsFileName);

    if (existingLogsIndex !== -1) {
      this.artifactsData.splice(existingLogsIndex, 1);

      if (this.selectedArtifactFile && this.selectedArtifactFile.name === logsFileName) {
        this.selectedArtifactFile = this.artifactsData.length > 0 ? this.artifactsData[0] : null;
      }
    }

    if (hasLogsAvailable) {
    }
  }

  private setupAutomaticErrorHandling(): void {

    this.previewError$
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(hasError => {
        if (hasError) {
          this.handleAutomaticErrorTabSwitch();
        }
      });

    this.enhancedSSEService.connectionError$
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((error: any) => {
        if (error) {
          this.previewError$.next(true);

          const errorMessage = this.extractErrorMessageFromSSEData(error) ||
                              error.message ||
                              'An error occurred during SSE connection';
          this.errorDescription$.next(errorMessage);
        }
      });
  }


  private extractErrorMessageFromSSEData(errorData: any): string | null {
    if (!errorData) return null;


    if (errorData.log && typeof errorData.log === 'string') {
      try {

        const logData = JSON.parse(errorData.log);
        if (logData.message) {
          return logData.message;
        }
        return errorData.log;
      } catch {

        return errorData.log;
      }
    }


    if (errorData.errorMessage && typeof errorData.errorMessage === 'string') {
      return errorData.errorMessage.trim();
    }


    if (errorData.progress_description && typeof errorData.progress_description === 'string') {
      return errorData.progress_description.trim();
    }

    return null;
  }


  private extractErrorMessageFromSSELog(): string | null {
    try {

      const sseState = this.sseDataProcessor.getCurrentState();

      if (!sseState) {
        return null;
      }


      if (sseState.status === 'FAILED' && sseState.progress === 'BUILD') {

        if (sseState.progressDescription && sseState.progressDescription.trim() !== '') {
          return sseState.progressDescription;
        }
      }


      if (sseState.status === 'FAILED') {
        if (sseState.progressDescription && sseState.progressDescription.trim() !== '') {
          return sseState.progressDescription;
        }
      }

      return null;
    } catch (error) {
      return null;
    }
  }

  private handleAutomaticErrorTabSwitch(): void {

    this.tabTransitionInProgress.set(true);

    this.currentTabState.set({
      activeTab: 'preview',
      isTransitioning: true,
      lastError: this.errorDescription$.value
    });

    this.ngZone.run(() => {
      this.currentView$.next('preview');
      this.isPreviewActive$.next(true);
      this.isCodeActive$.next(false);
      this.isArtifactsActive$.next(false);

      setTimeout(() => {
        this.tabTransitionInProgress.set(false);
        this.currentTabState.update(state => ({
          ...state,
          isTransitioning: false
        }));
        this.cdr.detectChanges();
      }, 200);
    });
  }


  private handleAutomaticCodeTabSwitch(): void {

    if (this.userSelectedTab) {
      return;
    }


    if (this.tabTransitionInProgress()) {
      return;
    }


    if (this.isUIDesignMode$.value) {
      return;
    }


    this.tabTransitionInProgress.set(true);

    this.currentTabState.set({
      activeTab: 'code',
      isTransitioning: true,
      lastError: null
    });

    this.ngZone.run(() => {
      this.currentView$.next('editor');
      this.isCodeActive$.next(true);
      this.isPreviewActive$.next(false);
      this.isArtifactsActive$.next(false);

      setTimeout(() => {
        this.tabTransitionInProgress.set(false);
        this.currentTabState.update(state => ({
          ...state,
          isTransitioning: false
        }));
        this.cdr.detectChanges();
      }, 200);
    });
  }


  private handleAutomaticPreviewTabSwitch(): void {

    if (this.userSelectedTab) {
      return;
    }


    if (this.tabTransitionInProgress()) {
      return;
    }


    if (this.isUIDesignMode$.value) {
      return;
    }


    this.tabTransitionInProgress.set(true);

    this.currentTabState.set({
      activeTab: 'preview',
      isTransitioning: true,
      lastError: null
    });

    this.ngZone.run(() => {
      this.currentView$.next('preview');
      this.isPreviewActive$.next(true);
      this.isCodeActive$.next(false);
      this.isArtifactsActive$.next(false);

      setTimeout(() => {
        this.tabTransitionInProgress.set(false);
        this.currentTabState.update(state => ({
          ...state,
          isTransitioning: false
        }));
        this.cdr.detectChanges();
      }, 200);
    });
  }


  private handleAutomaticFailedTabSwitch(): void {

    if (this.userSelectedTab) {
      return;
    }


    if (this.tabTransitionInProgress()) {
      return;
    }


    this.tabTransitionInProgress.set(true);

    this.currentTabState.set({
      activeTab: 'preview',
      isTransitioning: true,
      lastError: this.errorDescription$.value
    });

    this.ngZone.run(() => {
      this.currentView$.next('preview');
      this.isPreviewActive$.next(true);
      this.isCodeActive$.next(false);
      this.isArtifactsActive$.next(false);

      setTimeout(() => {
        this.tabTransitionInProgress.set(false);
        this.currentTabState.update(state => ({
          ...state,
          isTransitioning: false
        }));
        this.cdr.detectChanges();
      }, 200);
    });
  }

  getFilteredArtifactsData(): any[] {
    return this.artifactsData.filter(artifact => artifact.name !== 'Application Logs');
  }

  private startTabTransition(targetTab: string): void {
    this.tabTransitionInProgress.set(true);
    this.currentTabState.update(state => ({
      ...state,
      isTransitioning: true,
      activeTab: targetTab
    }));
  }

  private completeTabTransition(): void {
    setTimeout(() => {
      this.tabTransitionInProgress.set(false);
      this.currentTabState.update(state => ({
        ...state,
        isTransitioning: false
      }));
      this.cdr.detectChanges();
    }, 150);
  }

  toggleLogsFooter(): void {
    this.isLogsFooterExpanded = !this.isLogsFooterExpanded;
    this.cdr.detectChanges();
  }

  private ensureLogsFileAtBottom(): void {

  }

  getArtifactsTabTooltip(): string {
    const hasArtifacts = this.artifactsData.length > 0;
    const hasLogsAvailable = this.hasLogs || this.formattedLogMessages.length > 0;

    if (hasArtifacts && hasLogsAvailable) {
      return 'View project artefacts and application logs';
    } else if (hasArtifacts) {
      return 'View project artefacts';
    } else if (hasLogsAvailable) {
      return 'View application logs and debugging information';
    } else {
      return 'Artefacts and logs will be available when generated';
    }
  }

  private shouldPreventTabSwitching(tab: string): boolean {

    if (tab === 'artifacts' && this.isArtifactsTabEnabledWithLogs) {
      return false;
    }

    if (this.currentProgressState &&
        (this.currentProgressState.includes('BUILD') ||
         this.currentProgressState.includes('DEPLOY') ||
         this.currentProgressState.includes('COMPLETED'))) {
      return false;
    }

    if (tab === 'preview') {
      return false;
    }

    if (tab === 'code' && this.currentCodeTabState.isEnabled) {
      return false;
    }

    if (tab === 'overview' && this.isUIDesignMode$.value) {
      return false;
    }

    return true;
  }

  private processProjectOverviewArtifact(artifactData: any): void {
    const artifactName = 'Project Overview';

    const existingIndex = this.artifactsData.findIndex(item => item.name === artifactName);

    const artifactItem = {
      name: artifactName,
      type: 'markdown',
      content: artifactData.content || artifactData.data || 'Project overview content',
    };

    if (existingIndex !== -1) {

      this.artifactsData[existingIndex] = artifactItem;
    } else {

      this.artifactsData.unshift(artifactItem);

      this.ensureLogsFileAtBottom();
    }

    if (this.selectedArtifactFile && this.selectedArtifactFile.name === artifactName) {
      const existingState = this.artifactTypewriterStates[artifactName];

      if (
        !existingState ||
        (!existingState.isTyping && existingState.fullContent !== artifactItem.content)
      ) {
        this.startArtifactTypewriter(artifactName, artifactItem.content);
      }
    }

    this.loadedArtifacts.add(artifactName);

    this.enableArtifactsTabIfNeeded();

    if (!this.selectedArtifactFile) {
      this.selectArtifactFile(artifactItem);
    }
  }

  private preventDuplicateLayoutAnalyzed(): boolean {
    const layoutAnalyzedItems = this.artifactsData.filter(item => item.name === 'Layout Analyzed');
    if (layoutAnalyzedItems.length > 1) {

      const firstIndex = this.artifactsData.findIndex(item => item.name === 'Layout Analyzed');
      this.artifactsData = this.artifactsData.filter(
        (item, index) => item.name !== 'Layout Analyzed' || index === firstIndex
      );

      this.cdr.detectChanges();
      return true;
    }
    return false;
  }

  private processLayoutAnalyzedArtifact(artifactData: any): void {
    const artifactName = 'Layout Analyzed';

    const existingLayoutArtifact = this.artifactsData.find(item => item.name === artifactName);
    if (existingLayoutArtifact) {
      return;
    }

    const currentProgress = this.newPollingResponseProcessor.getCurrentProgress();

    if (currentProgress === 'LAYOUT_ANALYZED' || currentProgress === 'Layout Analyzed') {

      this.detectedLayoutFromPrevMetadata = artifactData.layoutKey || artifactData.layoutCode;
      this.shouldShowLayoutArtifact = false;

      let layoutKey = artifactData.layoutKey || artifactData.layoutCode;
      if (!layoutKey || !this.layoutMapping[layoutKey]) {
        layoutKey = 'HB';
      }
      this.layoutData = [layoutKey];

      return;
    }

    if (!this.detectedLayoutFromPrevMetadata) {
      return;
    }

    const existingIndex = this.artifactsData.findIndex(item => item.name === artifactName);

    let layoutKey = this.detectedLayoutFromPrevMetadata;

    if (!layoutKey || !this.layoutMapping[layoutKey]) {
      layoutKey = 'HB';
    } else {
    }

    this.layoutData = [layoutKey];

    const layoutImageUrl = `assets/images/layout-${layoutKey}.png`;
    const layoutName = this.layoutMapping[layoutKey];

    const artifactItem = {
      name: artifactName,
      type: 'image',
      content: layoutImageUrl,
    };

    if (existingIndex !== -1) {

      this.artifactsData[existingIndex] = artifactItem;
    } else {

      this.artifactsData.push(artifactItem);
    }

    this.ensureLogsFileAtBottom();

    this.layoutAnalyzedData = [
      {
        key: layoutKey,
        name: layoutName,
        imageUrl: layoutImageUrl,
      },
    ];

    this.loadedArtifacts.add(artifactName);
    this.hasLayoutAnalyzed = true;
    this.shouldShowLayoutArtifact = true;

    this.stopLayoutAnalyzing();

    this.enableArtifactsTabIfNeeded();

  }

  private processDesignSystemArtifact(artifactData: any): void {
    const artifactName = 'Design System';

    const existingIndex = this.artifactsData.findIndex(item => item.name === artifactName);

    const artifactItem = {
      name: artifactName,
      type: 'component',
      content: 'Design system tokens and components',
      tokens: artifactData.tokens,
    };

    if (existingIndex !== -1) {

      this.artifactsData[existingIndex] = artifactItem;
    } else {

      this.artifactsData.push(artifactItem);
    }

    this.ensureLogsFileAtBottom();

    this.loadedArtifacts.add(artifactName);
    this.hasDesignSystem = true;

    this.enableArtifactsTabIfNeeded();

    if (artifactData.tokens) {

      if (artifactData.isNewStructure && artifactData.tokens.colors) {
        this.designSystemData = artifactData.tokens;

        this.stopDesignTokenLoading();
      } else {
        this.designSystemData = artifactData.tokens;
      }

      if (artifactData.isFallback) {

        this.startDesignTokenLoading();
      }

      this.initializeDesignTokens();
    } else {

      this.startDesignTokenLoading();
      this.initializeDesignTokens();
    }
  }


  ngOnInit() {

    this.validateAndResetRegenerationState('Component initialization');

    this.sequentialRegenerationService.reset();
    this.regenerationIntegrationService.completeRegeneration();

    this.resetComponentState();

    this.initializePersistentArtifacts();

    this.initializeDefaultPrompt();

    this.initializeAppState();

    // Initialize viewport mode observable
    this.currentViewportMode = this.generateUIDesignService.applicationTarget$;

    if (this.artifactsData.length > 0 && !this.selectedArtifactFile) {
      const designSystemFile = this.artifactsData.find(file => file.name === 'Design System');
      if (designSystemFile) {
        this.selectArtifactFile(designSystemFile);
      } else {
        this.selectArtifactFile(this.artifactsData[0]);
      }
    }

    this.startDesignTokenLoading();
    this.initializeDesignTokens();

    this.initializeUIDesignMode();

    this.subscribeToUIDesignService();

    this.subscribeToFileOpeningService();

    this.subscribeToTemplateLoadingService();

    this.setupRegenerationIntegration();

    // Initialize project loading mode and data subscriptions in proper sequence
    this.initializeProjectLoadingModeAndDataSubscriptions();

    this.isProjectNameLoading = true;
    setTimeout(() => {
      this.isProjectNameLoading = false;
      this.cdr.detectChanges();
    }, 10000);

  }

  private initializeUIDesignMode(): void {

    this.route.data.pipe(takeUntil(this.destroy$)).subscribe(data => {
      const isUIDesignMode = data['cardType'] === 'Generate UI Design';
      const isProjectPreviewMode = data['isProjectPreview'] === true;
      const isProjectLoadingMode = data['isProjectLoading'] === true;
      const wasUIDesignMode = this.isUIDesignMode$.value;

      if (wasUIDesignMode && !isUIDesignMode) {
        this.cleanupUIDesignMode();
      }

      this.isUIDesignMode$.next(isUIDesignMode);
      this.isProjectPreviewMode$.next(isProjectPreviewMode);

      if (isUIDesignMode) {
        this.setupUIDesignMode();
      } else if (isProjectPreviewMode) {
        this.setupProjectPreviewMode();
      } else if (isProjectLoadingMode) {
        // For project loading mode, we'll handle it in initializeProjectLoadingMode
        // This ensures proper separation of concerns
      }
    });
  }

  /**
   * Initialize project loading mode and data subscriptions in proper sequence
   * This ensures that project loading mode is determined before initializing SSE/polling
   */
  private initializeProjectLoadingModeAndDataSubscriptions(): void {
    this.route.data.pipe(takeUntil(this.destroy$)).subscribe(data => {
      const isProjectLoadingMode = data['isProjectLoading'] === true;
      console.log('🔍 Route data check - isProjectLoadingMode:', isProjectLoadingMode);

      if (isProjectLoadingMode) {
        console.log('📁 Project Loading Mode - Skipping SSE/polling initialization');
        this.isProjectLoadingMode$.next(true);
        this.loadProjectData();
        // Skip SSE/polling initialization for project loading mode
      } else {
        console.log('⚡ Active Generation Mode - Initializing SSE/polling');
        // Initialize SSE/polling subscriptions only for active generation mode
        this.initializeDataSubscriptions();
        this.initializeAppState();
      }
    });

    // Also check for project ID in route params
    this.route.params.pipe(takeUntil(this.destroy$)).subscribe(params => {
      if (params['projectId'] && this.isProjectLoadingMode$.value) {
        this.loadProjectDataById(params['projectId']);
      }
    });
  }

  /**
   * Initialize data subscriptions (SSE/Polling) only for active generation mode
   * This method is called only when NOT in project loading mode
   */
  private initializeDataSubscriptions(): void {
    this.subscribeToNewPollingProcessor();
    this.subscribeToSSEDataProcessor();
  }

  /**
   * Load project data from route params
   */
  private loadProjectData(): void {
    this.route.params.pipe(takeUntil(this.destroy$)).subscribe(params => {
      if (params['projectId']) {
        this.loadProjectDataById(params['projectId']);
      }
    });
  }



  /**
   * Load project data by project ID
   * @param projectId The project ID to load
   */
  private loadProjectDataById(projectId: string): void {
    console.log('📡 Loading project data for ID:', projectId);
    this.projectLoadingState$.next('loading');
    this.projectLoadingError$.next(null);

    this.projectLoadingService.loadProjectData(projectId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response: ProjectLoadingResponse) => {
          try {
            console.log('✅ Project data loaded successfully:', response);

            // Parse the project loading response
            const parsedData = ProjectLoadingParser.parseProjectLoadingResponse(response);
            console.log('✅ Project data parsed successfully:', {
              projectName: parsedData.projectName,
              userPrompt: parsedData.userPrompt,
              conversationCount: parsedData.conversationMessages.length
            });

            this.projectLoadingData$.next(parsedData);

            // Initialize components with parsed data
            this.initializeComponentsWithProjectData(parsedData);

            this.projectLoadingState$.next('loaded');
            console.log('✅ Project loading completed successfully');
          } catch (error) {
            console.error('❌ Failed to parse project data:', error);
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            this.projectLoadingError$.next(`Failed to parse project data: ${errorMessage}`);
            this.projectLoadingState$.next('error');
          }
        },
        error: (error) => {
          console.error('Failed to load project data:', error);
          this.projectLoadingError$.next(error.message || 'Failed to load project data');
          this.projectLoadingState$.next('error');
        }
      });
  }

  /**
   * Initialize components with parsed project data
   * @param parsedData Parsed project data
   */
  private initializeComponentsWithProjectData(parsedData: ParsedProjectData): void {
    console.log('🔧 Initializing components with project data:', {
      projectName: parsedData.projectName,
      userPrompt: parsedData.userPrompt,
      conversationCount: parsedData.conversationMessages.length,
      commitIds: parsedData.commitIds
    });

    // Set project loading mode flag to control accordion timing
    this.isProjectLoadingMode = true;

    // Reset accordion and version counters for project loading
    // The accordion will be added manually after stepper completes
    this.hasAddedInitialGenerationAccordion = false;
    this.generationVersionCounter = 0;

    console.log('🔄 Project loading initialized - reset accordion flags', {
      hasAddedInitialGenerationAccordion: this.hasAddedInitialGenerationAccordion,
      generationVersionCounter: this.generationVersionCounter,
      isProjectLoadingMode: this.isProjectLoadingMode
    });

    // Set project ID and job ID for regeneration functionality
    this.setupProjectIdentifiersForRegeneration(parsedData);

    // Create initialization data for components
    const initData = ProjectLoadingParser.createCodeWindowInitializationData(parsedData);

    // Initialize chat window with conversation messages FIRST
    // This must happen before any other initialization that might overwrite messages
    this.initializeChatWindowWithProjectData(initData.chatMessages, parsedData.userPrompt);

    // Initialize stepper with project data
    this.initializeStepperWithProjectData(initData.stepperData);

    // Initialize code viewer with proper file extraction logic
    this.initializeCodeViewerWithProjectData(parsedData);

    // Initialize artifacts with extracted data
    this.initializeArtifactsWithProjectData(parsedData);

    // Load version accordion data if commit IDs exist
    if (parsedData.commitIds.length > 0) {
      this.loadVersionAccordionData(parsedData.commitIds);
    }

    // Set up repository integration
    this.setupRepositoryIntegration(initData.repositoryData);

    // Update UI state for project loading mode
    this.updateUIForProjectLoadingMode(parsedData);
  }

  /**
   * Initialize code viewer with project data using proper extraction logic
   * @param parsedData Parsed project data
   */
  private initializeCodeViewerWithProjectData(parsedData: ParsedProjectData): void {
    let filesToDisplay: any[] = [];
    let sourceDescription = '';

    // Check if we have regeneration data
    if (parsedData.hasRegenerationData && parsedData.regenerationFiles.length > 0) {
      console.log(`Project loading: Found ${parsedData.regenerationCount} regenerations with ${parsedData.regenerationFiles.length} files`);

      // Use regeneration files (from conversation ui_metadata)
      filesToDisplay = parsedData.regenerationFiles.map(file => ({
        name: file.fileName,
        type: 'file' as const,
        content: file.content,
        fileName: file.fileName,
        language: file.language,
        path: file.path
      }));

      sourceDescription = `Regeneration files (${parsedData.regenerationCount} regenerations)`;

    } else if (parsedData.initialGenerationFiles.length > 0) {
      console.log(`Project loading: No regenerations found, using initial generation files (${parsedData.initialGenerationFiles.length} files)`);

      // Use initial generation files (from metadata BUILD event)
      filesToDisplay = parsedData.initialGenerationFiles.map(file => ({
        name: file.fileName,
        type: 'file' as const,
        content: file.content,
        fileName: file.fileName,
        language: file.language,
        path: file.path
      }));

      sourceDescription = 'Initial generation files (from BUILD event)';

    } else {
      console.warn('Project loading: No code files found in either regeneration or initial generation data');
      sourceDescription = 'No code files available';
    }

    // Update code viewer if we have files
    if (filesToDisplay.length > 0) {
      this.files$.next(filesToDisplay);
      this.isCodeTabEnabled = true;
      this.isCodeGenerationComplete = true;

      console.log(`Code viewer initialized with ${filesToDisplay.length} files from: ${sourceDescription}`);

      // Enable code tab and switch to it if no other tab is active
      if (!this.userSelectedTab) {
        this.currentView$.next('editor');
      }

      this.cdr.detectChanges();
    } else {
      console.log('Project loading: No files to display in code viewer');
    }
  }

  /**
   * Initialize artifacts with project data and logs
   * @param parsedData Parsed project data
   */
  private initializeArtifactsWithProjectData(parsedData: ParsedProjectData): void {
    console.log('🎨 Initializing artifacts with project data');

    // Initialize logs from metadata SSE events
    this.initializeLogsFromProjectData(parsedData);

    if (parsedData.artifactsData && parsedData.artifactsData.length > 0) {
      console.log(`Project loading: Found ${parsedData.artifactsData.length} clean artifacts`);

      // Set artifacts data for the artifacts component (only proper artifacts)
      this.artifactsData = parsedData.artifactsData.map(artifact => {
        const mappedArtifact = {
          name: artifact.name,
          type: artifact.type === 'text' ? 'markdown' : artifact.type, // Convert text to markdown for display
          content: artifact.content,
          source: artifact.source,
          progress: artifact.progress,
          log: artifact.log,
          // Include additional properties for specific artifact types
          ...(artifact.tokens && { tokens: artifact.tokens }), // For design system
          ...(artifact.layoutKey && { layoutKey: artifact.layoutKey }), // For layout
          ...(artifact.imageUrl && { imageUrl: artifact.imageUrl }) // For layout
        };

        console.log(`🎨 Mapped artifact for UI:`, {
          name: mappedArtifact.name,
          type: mappedArtifact.type,
          hasTokens: !!mappedArtifact.tokens,
          tokenDetails: mappedArtifact.tokens ? {
            colorCount: mappedArtifact.tokens.colors?.length || 0,
            colors: mappedArtifact.tokens.colors?.slice(0, 3) // Show first 3 colors
          } : null,
          hasLayoutKey: !!mappedArtifact.layoutKey,
          layoutKey: mappedArtifact.layoutKey
        });

        return mappedArtifact;
      });

      // Handle layout-specific setup for Layout Analyzed artifacts
      const layoutArtifact = parsedData.artifactsData.find(artifact => artifact.name === 'Layout Analyzed');
      console.log('🎨 Layout artifact found:', layoutArtifact);

      if (layoutArtifact) {
        if (layoutArtifact.layoutKey) {
          console.log('✅ Setting up layout with key:', layoutArtifact.layoutKey);
          this.setupLayoutAnalyzedData(layoutArtifact.layoutKey);
        } else {
          console.warn('⚠️ Layout artifact found but no layoutKey:', layoutArtifact);
        }
      } else {
        console.warn('⚠️ No Layout Analyzed artifact found in:', parsedData.artifactsData.map(a => a.name));
      }

      // Handle design system-specific setup for Design System artifacts
      const designSystemArtifact = parsedData.artifactsData.find(artifact => artifact.name === 'Design System');
      if (designSystemArtifact && designSystemArtifact.tokens) {
        console.log('🎨 Setting up design system data:', designSystemArtifact.tokens);
        this.designSystemData = designSystemArtifact.tokens;
        this.hasDesignSystem = true;
        this.initializeDesignTokens();
      }

      // Log clean artifacts for debugging
      parsedData.artifactsData.forEach((artifact, index) => {
        console.log(`✅ Clean Artifact ${index + 1}:`, {
          name: artifact.name,
          type: artifact.type,
          source: artifact.source,
          progress: artifact.progress,
          contentPreview: typeof artifact.content === 'string'
            ? artifact.content.substring(0, 100) + '...'
            : '[Object]',
          hasTokens: !!artifact.tokens,
          tokensPreview: artifact.tokens ? {
            colorCount: artifact.tokens.colors?.length || 0,
            fontCount: artifact.tokens.fonts?.length || 0
          } : null,
          hasLayoutKey: !!artifact.layoutKey,
          layoutKey: artifact.layoutKey
        });
      });

      // Enable artifacts tab with logs
      this.isArtifactsTabEnabled = true;
      this.isArtifactsTabEnabledWithLogs = true;
      this.isArtifactsActive$.next(true);

      // Select the first artifact by default
      if (this.artifactsData.length > 0 && !this.selectedArtifactFile) {
        this.selectArtifactFile(this.artifactsData[0]);
      }

      // Force change detection to ensure artifacts are displayed
      this.cdr.detectChanges();

      console.log('✅ Artifacts tab enabled with clean artifacts and logs');
    } else {
      console.log('⚠️ No artifacts found in metadata - enabling logs only');

      // Even if no artifacts, enable the tab for logs
      this.isArtifactsTabEnabled = true;
      this.isArtifactsTabEnabledWithLogs = true;
      this.isArtifactsActive$.next(true);
    }
  }

  /**
   * Setup layout analyzed data for artifacts display
   * @param layoutKey Layout key from artifact
   */
  private setupLayoutAnalyzedData(layoutKey: string): void {
    console.log('🎨 Setting up layout analyzed data for key:', layoutKey);

    // Set up layout data for the artifacts component
    this.layoutData = [layoutKey];

    // Create layout analyzed data structure (matching layout animation component keys)
    const layoutMapping: { [key: string]: string } = {
      'HB': 'Header & Body',
      'HBF': 'Header, Body & Footer',
      'HLSB': 'Header, Left Sidebar & Body',
      'HLSBF': 'HLSB & Footer',
      'HBRS': 'Header, Body & Right Sidebar',
      'HBRSF': 'HBRS & Footer',
      'HLSBRS': 'Dual Sidebar',
      'HLSBRSF': 'Dual Sidebar & Footer'
    };

    const layoutName = layoutMapping[layoutKey] || 'Custom Layout';
    const imageUrl = `assets/images/layout-${layoutKey}.png`;

    this.layoutAnalyzedData = [{
      key: layoutKey,
      name: layoutName,
      imageUrl: imageUrl
    }];

    this.hasLayoutAnalyzed = true;
    this.hasLayoutBeenDetected = true; // This is crucial for shouldShowAnalyzingLayout()

    // Force layout component to update
    this.stopLayoutAnalyzing();

    // Trigger change detection
    this.cdr.detectChanges();

    console.log('✅ Layout analyzed data setup complete:', {
      layoutKey: layoutKey,
      layoutName: layoutName,
      layoutData: this.layoutData,
      layoutAnalyzedData: this.layoutAnalyzedData,
      hasLayoutAnalyzed: this.hasLayoutAnalyzed
    });
  }

  /**
   * Initialize logs from project metadata for artifacts tab
   * @param parsedData Parsed project data
   */
  private initializeLogsFromProjectData(parsedData: ParsedProjectData): void {
    console.log('📝 Initializing logs from project metadata');

    if (parsedData.logEntries && parsedData.logEntries.length > 0) {
      // Convert log entries to the format expected by artifacts component (formattedLogMessages)
      const formattedLogs = parsedData.logEntries.map((logEntry, index) => ({
        id: `project-log-${index}`,
        type: logEntry.status === 'COMPLETED' ? 'info' :
              logEntry.status === 'FAILED' ? 'error' : 'info',
        timestamp: this.formatTimestamp(new Date()), // Format timestamp properly
        content: logEntry.log || logEntry.progress_description || 'Processing...',
        rawLog: `${logEntry.progress}: ${logEntry.log || logEntry.progress_description}`,
        visibleContent: logEntry.log || logEntry.progress_description || 'Processing...',
        progress: logEntry.progress,
        status: logEntry.status
      }));

      // Set formatted log messages for artifacts component
      this.formattedLogMessages = formattedLogs;
      this.hasLogs = true;

      console.log(`✅ Initialized ${formattedLogs.length} formatted log entries for artifacts tab`);

      // Log sample for debugging
      if (formattedLogs.length > 0) {
        console.log('📝 Sample log entry:', formattedLogs[0]);
      }
    } else {
      console.log('⚠️ No log entries found in project metadata');
      this.formattedLogMessages = [];
      this.hasLogs = false;
    }
  }

  /**
   * Format timestamp for log display
   * @param date Date to format
   * @returns Formatted timestamp string
   */
  private formatTimestamp(date: Date): string {
    return date.toLocaleTimeString('en-US', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  }

  /**
   * Initialize chat window with project data following the correct sequence:
   * 1. Initial user prompt
   * 2. Stepper (after prompt)
   * 3. Version accordion (after stepper completes)
   * 4. Regeneration messages (after version accordion)
   * @param chatMessages Chat messages from project data
   * @param userPrompt Initial user prompt
   */
  private initializeChatWindowWithProjectData(chatMessages: ChatWindowMessage[], userPrompt: string): void {
    console.log('🔄 Initializing chat window with project data in correct sequence');
    console.log('User prompt:', userPrompt);
    console.log('Chat messages:', chatMessages);

    // Clear existing messages first
    this.lightMessages = [];

    // Step 1: Set the initial user prompt if available (project_description as initial prompt)
    if (userPrompt && userPrompt.trim() !== '') {
      const initialPrompt = {
        id: `user-prompt-${Date.now()}`,
        text: userPrompt,
        from: 'user' as const,
        theme: 'light' as const
      };

      this.lightMessages.push(initialPrompt);
      console.log('✅ Step 1: Added project description as initial user prompt to chat window:', initialPrompt);
    } else {
      console.warn('⚠️ No user prompt (project_description) available for chat window initialization');
    }

    // Store regeneration messages for later addition (after stepper and version accordion complete)
    this.pendingRegenerationMessages = chatMessages;
    console.log(`📝 Stored ${chatMessages.length} regeneration messages for later addition`);

    // Trigger change detection to update the chat window UI with initial prompt
    this.cdr.detectChanges();

    // Also update the chat window component if it exists
    if (this.chatWindow) {
      // The chat window should automatically pick up the lightMessages changes
      setTimeout(() => {
        this.cdr.detectChanges();
        console.log('🔄 Chat window UI updated with initial prompt');
      }, 100);
    }
  }

  /**
   * Initialize stepper with project data
   * @param stepperData Stepper initialization data
   */
  private initializeStepperWithProjectData(stepperData: any): void {
    console.log('🔄 Initializing stepper with project data:', stepperData);

    // For project loading mode, we need to simulate SSE events from metadata
    // Extract SSE events from parsed project data
    const parsedData = this.projectLoadingData$.value;
    if (!parsedData?.metadata) {
      console.warn('⚠️ No metadata found for stepper initialization');
      return;
    }

    try {
      // Parse SSE events from metadata
      const sseEvents = ProjectLoadingParser.parseSSEEventsFromMetadata(parsedData.metadata);
      console.log('📊 Parsed SSE events for stepper:', sseEvents.length);

      if (sseEvents.length > 0) {
        // Start stepper simulation immediately
        this.simulateSSEEventsForStepper(sseEvents);
        console.log('✅ Stepper simulation started for project loading mode');
      } else {
        console.warn('⚠️ No valid SSE events found in metadata for stepper');
      }

    } catch (error) {
      console.error('❌ Failed to initialize stepper with project data:', error);
    }
  }

  /**
   * Simulate SSE events for stepper component in project loading mode
   * @param sseEvents Array of parsed SSE events from metadata
   */
  private simulateSSEEventsForStepper(sseEvents: any[]): void {
    console.log('🎭 Simulating SSE events for stepper component');

    if (sseEvents.length === 0) {
      console.warn('⚠️ No SSE events to simulate for stepper');
      return;
    }

    // Reset stepper state before starting
    this.stepperStateService.triggerStepperReset();

    // Set initial state to show stepper immediately
    this.currentProgressState = 'INITIALIZING';
    this.lastProgressDescription = 'Loading project data...';
    this.pollingStatus = 'IN_PROGRESS';
    this.cdr.detectChanges();

    console.log(`🚀 Starting stepper simulation with ${sseEvents.length} events`);

    // Process each SSE event with a delay to simulate real-time flow
    sseEvents.forEach((event, index) => {
      setTimeout(() => {
        console.log(`📡 Processing SSE event ${index + 1}/${sseEvents.length}:`, {
          progress: event.progress,
          status: event.status,
          description: event.progress_description?.substring(0, 100)
        });

        // Update the current progress state (used by chat-window stepper)
        this.currentProgressState = event.progress;
        this.lastProgressDescription = event.progress_description || event.log || 'Processing...';
        this.pollingStatus = event.status || 'IN_PROGRESS';

        // Trigger change detection to update stepper
        this.cdr.detectChanges();

        // If this is the final event, mark as completed and add regeneration messages
        if (event.is_final || index === sseEvents.length - 1) {
          setTimeout(() => {
            console.log('✅ Stepper simulation completed');
            this.pollingStatus = 'COMPLETED';
            this.cdr.detectChanges();

            // Step 3: Add version accordion first, then regeneration messages after stepper completes
            this.addVersionAccordionThenRegenerationMessages();
          }, 500);
        }

      }, (index + 1) * 1200); // 1.2s delay between events for better visibility
    });
  }

  /**
   * Add version accordion first, then regeneration messages after stepper completes
   * This follows the correct sequence: initial prompt → stepper → version accordion → regeneration messages
   */
  private addVersionAccordionThenRegenerationMessages(): void {
    console.log('📝 Adding version accordion first, then regeneration messages after stepper completion');

    // Step 3a: Add the first version accordion (initial generation)
    this.addInitialGenerationAccordionForProjectLoading();

    // Step 3b: Add regeneration messages after accordion is added
    setTimeout(() => {
      this.addRegenerationMessagesAfterAccordion();
    }, 800); // Small delay to ensure accordion is rendered
  }

  /**
   * Add initial generation accordion specifically for project loading
   * This prevents the automatic accordion addition and ensures proper timing
   */
  private addInitialGenerationAccordionForProjectLoading(): void {
    console.log('📋 Adding initial generation accordion for project loading', {
      hasAddedInitialGenerationAccordion: this.hasAddedInitialGenerationAccordion,
      generationVersionCounter: this.generationVersionCounter,
      isProjectLoadingMode: this.isProjectLoadingMode
    });

    // Check if accordion was already added to prevent duplicates
    if (this.hasAddedInitialGenerationAccordion) {
      console.log('⚠️ Initial generation accordion already added, skipping duplicate');
      return;
    }

    // Additional check: if version counter is already > 0, accordion was likely already added
    if (this.generationVersionCounter > 0) {
      console.log('⚠️ Generation version counter > 0, accordion likely already added, skipping duplicate');
      this.hasAddedInitialGenerationAccordion = true; // Set flag to prevent future attempts
      return;
    }

    // Set flag BEFORE adding accordion to prevent race conditions
    this.hasAddedInitialGenerationAccordion = true;

    // Add the accordion manually with proper timing
    this.addGenerationAccordionToChat();

    console.log('✅ Initial generation accordion added for project loading', {
      newVersionCounter: this.generationVersionCounter
    });
  }

  /**
   * Add regeneration messages to chat window after version accordion is complete
   * This follows the correct sequence: initial prompt → stepper → version accordion → regeneration messages
   */
  private addRegenerationMessagesAfterAccordion(): void {
    console.log('📝 Adding regeneration messages after version accordion completion');

    if (this.pendingRegenerationMessages.length === 0) {
      console.log('ℹ️ No regeneration messages to add');
      return;
    }

    // Convert and add conversation messages to chat window
    this.pendingRegenerationMessages.forEach((message, index) => {
      const chatMessage = {
        id: message.id || `regen-msg-${index}-${Date.now()}`,
        text: message.content, // ChatWindowMessage uses 'content', chat window expects 'text'
        from: message.type === 'user' ? 'user' as const : 'ai' as const, // Map 'assistant' to 'ai'
        theme: 'light' as const
      };

      this.lightMessages.push(chatMessage);
      console.log('✅ Added regeneration message:', chatMessage);
    });

    console.log(`✅ Added ${this.pendingRegenerationMessages.length} regeneration messages to chat window`);

    // Clear pending messages
    this.pendingRegenerationMessages = [];

    // Trigger change detection to update the chat window UI
    this.cdr.detectChanges();

    // Also update the chat window component if it exists
    if (this.chatWindow) {
      setTimeout(() => {
        this.cdr.detectChanges();
        console.log('🔄 Chat window UI updated with regeneration messages');

        // Reset project loading mode flag after everything is complete
        this.isProjectLoadingMode = false;
        console.log('✅ Project loading sequence complete - reset project loading mode');
      }, 100);
    } else {
      // Reset project loading mode flag even if no chat window
      this.isProjectLoadingMode = false;
      console.log('✅ Project loading sequence complete - reset project loading mode');
    }
  }

  /**
   * Load version accordion data from commit IDs
   * @param commitIds Array of commit IDs
   */
  private loadVersionAccordionData(commitIds: string[]): void {
    // Get current project ID from route params
    const projectId = this.route.snapshot.params['projectId'];

    if (!projectId) {
      console.error('No project ID available for loading version data');
      return;
    }

    console.log(`🔄 Loading version data for ${commitIds.length} commits using /project/version endpoint`);

    // Load version data for each commit ID using the correct API endpoint
    commitIds.forEach(commitId => {
      console.log(`📡 Calling /project/version with payload: { project_id: "${projectId}", commit_id: "${commitId}" }`);

      this.projectLoadingService.getProjectVersion(projectId, commitId)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (versionResponse) => {
            console.log('✅ Project version loaded for commit:', commitId, versionResponse);
            // TODO: Integrate with version accordion component to display the version data
            // This would populate the version accordion with the version data
          },
          error: (error) => {
            console.error('❌ Failed to load project version for commit:', commitId, error);
            this.toastService.error(`Failed to load version data for commit ${commitId.substring(0, 8)}`);
          }
        });
    });
  }

  /**
   * Set up repository integration
   * @param repositoryData Repository integration data
   */
  private setupRepositoryIntegration(repositoryData: any): void {
    // Set up clone URL and deployed URL for repository integration
    console.log('Setting up repository integration:', repositoryData);
  }

  /**
   * Update UI state for project loading mode
   * @param parsedData Parsed project data
   */
  private updateUIForProjectLoadingMode(parsedData: ParsedProjectData): void {
    console.log('🎨 Updating UI for project loading mode:', {
      projectName: parsedData.projectName,
      hasCodeFiles: parsedData.initialGenerationFiles.length > 0,
      hasArtifacts: parsedData.artifactsData.length > 0,
      hasDeployedUrl: !!parsedData.deployedUrl,
      conversationCount: parsedData.conversationMessages.length
    });

    // Detect project phase and enable prompt bar accordingly
    this.handleProjectPhaseDetectionAndPromptBar(parsedData);

    // Set project name
    this.projectName = parsedData.projectName;

    // Configure tab availability based on project data
    this.configureTabAvailability(parsedData);

    // Automatically select the most appropriate tab based on available content
    this.selectOptimalTabForProjectLoading(parsedData);

    console.log('✅ UI state updated for project loading mode');
  }

  /**
   * Configure tab availability based on project data
   * @param parsedData Parsed project data
   */
  private configureTabAvailability(parsedData: ParsedProjectData): void {
    console.log('🔧 Configuring tab availability:', {
      hasDeployedUrl: !!parsedData.deployedUrl,
      codeFilesCount: parsedData.initialGenerationFiles.length,
      artifactsCount: parsedData.artifactsData.length
    });

    // Configure preview tab
    if (parsedData.deployedUrl && parsedData.deployedUrl.trim() !== '') {
      this.setPreviewTabEnabled('View deployed application');

      // Set the deployed URL for the iframe
      this.deployedUrl$.next(parsedData.deployedUrl);
      this.urlSafe = this.sanitizer.bypassSecurityTrustResourceUrl(parsedData.deployedUrl);

      // Enable iframe states
      this.isIframeReady$.next(true);
      this.isUrlValidated$.next(true);
      this.isUrlAvailable$.next(true);
      this.isPreviewTabEnabled = true;
      this.previewError$.next(false);

      console.log('✅ Preview tab configured with deployed URL:', parsedData.deployedUrl);
    } else {
      this.setPreviewTabDisabled('Preview not available');
      console.log('⚠️ Preview tab disabled - no deployed URL');
    }

    // Configure code tab
    if (parsedData.initialGenerationFiles.length > 0) {
      this.setCodeTabEnabled(`View ${parsedData.initialGenerationFiles.length} generated files`);

      // Convert project loading FileData to polling response FileData format
      const convertedCodeFiles = parsedData.initialGenerationFiles.map(file => ({
        path: file.path,
        code: file.content // Convert 'content' to 'code' property
      }));

      // Set code files data for the code viewer
      this.newCodeFiles = convertedCodeFiles;

      // Also update the code viewer with the converted files
      this.updateCodeViewer(convertedCodeFiles);

      console.log('✅ Code tab configured with files:', parsedData.initialGenerationFiles.length);
    } else {
      this.setCodeTabDisabled('No code files available');
      console.log('⚠️ Code tab disabled - no code files');
    }

    // Configure artifacts tab
    if (parsedData.artifactsData.length > 0) {
      this.isArtifactsTabEnabled = true;
      this.isArtifactsTabEnabledWithLogs = true;

      // Set artifacts data for the component
      this.artifactsData = parsedData.artifactsData;
      console.log('✅ Artifacts tab enabled with artifacts:', parsedData.artifactsData.length);
    } else {
      this.isArtifactsTabEnabled = false;
      this.isArtifactsTabEnabledWithLogs = false;
      console.log('⚠️ Artifacts tab disabled - no artifacts');
    }
  }

  /**
   * Select the most appropriate tab for project loading based on available content
   * Priority: Preview (if URL available) > Code (if files available) > Artifacts (if available)
   * @param parsedData Parsed project data
   */
  private selectOptimalTabForProjectLoading(parsedData: ParsedProjectData): void {
    console.log('🎯 Selecting optimal tab for project loading');

    let selectedTab: 'preview' | 'editor' | 'artifacts' = 'preview'; // Default fallback
    let selectedTabReason = 'default fallback';

    // Priority 1: Preview tab if deployed URL is available
    if (parsedData.deployedUrl && parsedData.deployedUrl.trim() !== '') {
      selectedTab = 'preview';
      selectedTabReason = 'deployed URL available';

      // Activate preview tab states
      this.isPreviewActive$.next(true);
      this.isCodeActive$.next(false);
      this.isArtifactsActive$.next(false);
    }
    // Priority 2: Code tab if code files are available but no preview
    else if (parsedData.initialGenerationFiles.length > 0) {
      selectedTab = 'editor';
      selectedTabReason = 'code files available';

      // Activate code tab states
      this.isPreviewActive$.next(false);
      this.isCodeActive$.next(true);
      this.isArtifactsActive$.next(false);
    }
    // Priority 3: Artifacts tab if artifacts are available but no code/preview
    else if (parsedData.artifactsData.length > 0) {
      selectedTab = 'artifacts';
      selectedTabReason = 'artifacts available';

      // Activate artifacts tab states
      this.isPreviewActive$.next(false);
      this.isCodeActive$.next(false);
      this.isArtifactsActive$.next(true);
    }
    // Fallback: Preview tab (even if no content)
    else {
      selectedTab = 'preview';
      selectedTabReason = 'fallback (no content available)';

      // Activate preview tab states (will show appropriate message)
      this.isPreviewActive$.next(true);
      this.isCodeActive$.next(false);
      this.isArtifactsActive$.next(false);

      // Enable preview tab even without URL to show fallback message
      this.setPreviewTabEnabled('Preview tab (no content available yet)');
    }

    // Set the current view
    this.currentView$.next(selectedTab);

    // Ensure proper activation with a slight delay for UI updates
    setTimeout(() => {
      this.cdr.detectChanges();
      console.log(`✅ Tab selected for project loading: ${selectedTab} (${selectedTabReason})`);
    }, 100);
  }


  private handleProjectPhaseDetectionAndPromptBar(parsedData: ParsedProjectData): void {
    const isFirstGeneration = parsedData.conversationMessages.length === 0;

    console.log('🔍 Project phase detection:', {
      isFirstGeneration,
      conversationCount: parsedData.conversationMessages.length,
      hasMetadata: !!parsedData.metadata
    });

    if (isFirstGeneration) {
      // First generation project - check for DEPLOY completion
      this.handleFirstGenerationPromptBarEnablement(parsedData);
    } else {
      // Regeneration project - enable prompt bar immediately
      this.handleRegenerationPromptBarEnablement(parsedData);
    }
  }

  /**
   * Handle prompt bar enablement for first generation projects
   * @param parsedData Parsed project data
   */
  private handleFirstGenerationPromptBarEnablement(parsedData: ParsedProjectData): void {
    try {
      const metadata = JSON.parse(parsedData.metadata || '[]');

      // Look for DEPLOY progress with COMPLETED status
      const deployCompleted = metadata.some((event: any) =>
        event.progress === 'DEPLOY' && event.status === 'COMPLETED'
      );

      console.log('🚀 First generation prompt bar check:', {
        deployCompleted,
        metadataEvents: metadata.length
      });

      if (deployCompleted) {
        this.enablePromptBarForProjectLoading('First generation completed - ready for edits');
      } else {
        this.disablePromptBarForProjectLoading('First generation not yet completed');
      }
    } catch (error) {
      console.error('❌ Error parsing metadata for first generation check:', error);
      this.disablePromptBarForProjectLoading('Error checking generation status');
    }
  }

  /**
   * Handle prompt bar enablement for regeneration projects
   * @param parsedData Parsed project data
   */
  private handleRegenerationPromptBarEnablement(parsedData: ParsedProjectData): void {
    console.log('🔄 Regeneration project detected - enabling prompt bar immediately');
    this.enablePromptBarForProjectLoading(`Regeneration project loaded - ready for additional edits (${parsedData.conversationMessages.length} previous regenerations)`);
  }

  /**
   * Enable prompt bar for project loading with specific message
   * @param reason Reason for enabling
   */
  private enablePromptBarForProjectLoading(reason: string): void {
    console.log('✅ Enabling prompt bar for project loading:', reason);

    // Set code generation as complete to enable prompt bar
    this.isCodeGenerationComplete = true;
    this.isCodeGenerationLoading$.next(false);

    // Enable prompt bar functionality
    this.isAiResponding = false;

    // Update UI state to show prompt bar is ready
    this.cdr.detectChanges();
  }

  /**
   * Disable prompt bar for project loading with specific message
   * @param reason Reason for disabling
   */
  private disablePromptBarForProjectLoading(reason: string): void {
    console.log('⚠️ Disabling prompt bar for project loading:', reason);

    // Keep code generation as incomplete to disable prompt bar
    this.isCodeGenerationComplete = false;
    this.isCodeGenerationLoading$.next(true);

    // Disable prompt bar functionality
    this.isAiResponding = true;

    // Update UI state
    this.cdr.detectChanges();
  }

  /**
   * Setup project identifiers for regeneration functionality
   * @param parsedData Parsed project data
   */
  private setupProjectIdentifiersForRegeneration(parsedData: ParsedProjectData): void {
    console.log('🔧 Setting up project identifiers for regeneration:', {
      projectId: parsedData.projectId
    });

    // Set project ID from parsed data
    this.projectId = parsedData.projectId;

    // For project loading, we need to extract job ID from the latest generation
    // This could be from metadata or we might need to use the project ID as job ID
    // For now, we'll use the project ID as job ID since that's what the regeneration API expects
    this.jobId = parsedData.projectId;

    // Update app state service with project identifiers
    this.appStateService.updateProjectState({
      projectId: parsedData.projectId,
      jobId: parsedData.projectId, // Using project ID as job ID for project loading
      prompt: parsedData.userPrompt,
      type: 'Generate Application', // Set as Generate Application for regeneration flow
      codeGenerated: true // Mark as code generated since we're loading an existing project
    });

    console.log('✅ Project identifiers set up for regeneration:', {
      projectId: this.projectId,
      jobId: this.jobId
    });
  }

  private shouldBlockUIDesignData(dataType: string): boolean {

    const isActualUIDesignWorkflow = this.isUIDesignMode$.value && this.isUIDesignWorkflowActive();

    if (!isActualUIDesignWorkflow) {
      return false;
    }

    const allowedDataTypes = ['logs', 'artifacts', 'status', 'progress'];

    return !allowedDataTypes.includes(dataType);
  }

  private isUIDesignWorkflowActive(): boolean {

    const hasUIDesignCanvas = !!document.querySelector('.ui-design-canvas');
    const hasUIDesignOverview = this.showUIDesignOverviewTab$.value;

    return hasUIDesignCanvas || hasUIDesignOverview;
  }

  private setupUIDesignMode(): void {

    this.isLogsTabEnabled = false;
    this.isArtifactsTabEnabled = false;
    this.isArtifactsTabEnabledWithLogs = false;
    this.isCodeActive$.next(false);

    this.isArtifactsActive$.next(false);

    this.currentView$.next('preview');
    this.isPreviewActive$.next(true);

    this.isPolling = false;
    this.pollingStatus = 'PENDING';
    this.currentProgressState = '';
    this.lastProgressDescription = '';

    this.isCodeGenerationComplete = false;
    this.files$.next([]);


    this.resetArtifactsWithPersistence();

    this.isUIDesignMode$.next(true);
    this.isUIDesignGenerating$.next(false);
    this.uiDesignError$.next(null);
    this.uiDesignApiInProgress$.next(false);
    this.isWireframeGenerationComplete$.next(false);
    this.isUIDesignRegenerating$.next(false);

    this.clearAllLoadingNodes();

    this.wireframeGenerationStateService.reset();

    this.initializeUIDesignChatMessages();

    this.initializeUIDesignSelection();

    this.initializeUIDesignCanvas();

    this.startUIDesignGeneration();

    setTimeout(() => {
      this.setupAutoCanvasCentering();
    }, 200);

  }

  private cleanupUIDesignMode(): void {

    this.isUIDesignMode$.next(false);
    this.isUIDesignGenerating$.next(false);
    this.uiDesignError$.next(null);
    this.uiDesignApiInProgress$.next(false);
    this.isUIDesignRegenerating$.next(false);

    this.clearAllLoadingNodes();

    this.uiDesignIntroService.resetIntroState();

    this.uiDesignNodes$.next([]);
    this.selectedUIDesignNode$.next(null);
    this.isUIDesignFullScreenOpen$.next(false);

    this.uiDesignSelectionService.reset();
    this.showCanvasTooltip$.next(true);
    this.isEditingUIDesign$.next(false);

    this.isLogsTabEnabled = true;
    this.isArtifactsTabEnabled = true;

    this.enableArtifactsTabIfNeeded();

  }

  /**
   * Setup project preview mode
   */
  private setupProjectPreviewMode(): void {
    // Set loading state
    this.isProjectDataLoading$.next(true);

    // Get project data from router state
    const navigation = this.router.getCurrentNavigation();
    const projectData = navigation?.extras?.state?.['projectData'];

    if (projectData) {
      // Process and display project data
      this.processProjectData(projectData);
    } else {
      // Try to get project ID from route params and fetch data
      this.route.params.pipe(takeUntil(this.destroy$)).subscribe(params => {
        const projectId = params['projectId'];
        if (projectId) {
          this.fetchProjectDataById(projectId);
        } else {
          this.handleProjectPreviewError('No project data or ID available');
        }
      });
    }
  }

  /**
   * Process project data for preview
   */
  private processProjectData(projectData: any): void {
    try {
      this.projectData$.next(projectData);

      // Set up the preview with project data
      this.setupProjectPreview(projectData);

      // Clear loading state
      this.isProjectDataLoading$.next(false);

      this.cdr.markForCheck();
    } catch (error) {
      this.handleProjectPreviewError('Failed to process project data');
    }
  }

  /**
   * Setup project preview display
   */
  private setupProjectPreview(projectData: any): void {
    // Set project name
    this.projectName = projectData.project_name || 'Project Preview';

    // Configure UI for project preview
    this.currentView$.next('preview');
    this.isPreviewActive$.next(true);
    this.isCodeActive$.next(false);
    this.isArtifactsActive$.next(false);

    // Show shimmer loading initially
    this.isLoading$.next(true);

    // Simulate loading completion after processing
    setTimeout(() => {
      this.isLoading$.next(false);
      this.cdr.markForCheck();
    }, 1500);
  }

  /**
   * Fetch project data by ID (fallback)
   */
  private fetchProjectDataById(projectId: string): void {
    // This would typically call a service to fetch project data
    // For now, we'll handle the error case
    this.handleProjectPreviewError(`Project data not available for ID: ${projectId}`);
  }

  /**
   * Handle project preview errors
   */
  private handleProjectPreviewError(errorMessage: string): void {
    this.isProjectDataLoading$.next(false);
    this.previewError$.next(true);
    this.errorDescription$.next(errorMessage);
    this.toastService.error(errorMessage);
    this.cdr.markForCheck();
  }

  private initializeUIDesignSelection(): void {

    this.uiDesignSelectionService.reset();
    this.isEditingUIDesign$.next(false);

    this.uiDesignSelectionService.showCanvasTooltip
      .pipe(takeUntil(this.destroy$))
      .subscribe(showTooltip => {
        this.showCanvasTooltip$.next(showTooltip);
      });

    this.uiDesignSelectionService.selectedNodes
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        this.updateNodeSelectionVisuals();
      });

    this.uiDesignSelectionService.selectedNode
      .pipe(takeUntil(this.destroy$))
      .subscribe(selectedNode => {

        if (selectedNode) {
        }
      });

    this.uiDesignSelectionService.isEditingInProgress
      .pipe(takeUntil(this.destroy$))
      .subscribe(isEditing => {
        this.isEditingUIDesign$.next(isEditing);
      });

  }

  private subscribeToFileOpeningService(): void {

    this.fileOpeningService.tabSwitchRequests
      .pipe(takeUntil(this.destroy$))
      .subscribe(request => {
        this.onTabClick(request.tab);
      });

  }


  private subscribeToTemplateLoadingService(): void {

    this.templateLoadingService.templateFiles$
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (templateFiles) => {
          try {
            if (templateFiles && templateFiles.length > 0) {
              
              const fileModels: FileModel[] = [];
              const invalidFiles: any[] = [];

              for (const file of templateFiles) {
                try {

                  const validationResult = this.validateTemplateFile(file);

                  if (validationResult.isValid) {
                    const fileModel: FileModel = {
                      name: file.name,
                      type: 'file' as const,
                      content: file.content || '',
                      fileName: file.fileName || file.name
                    };
                    fileModels.push(fileModel);
                  } else {
                    invalidFiles.push({
                      file,
                      reason: validationResult.reason
                    });

                  }
                } catch (fileError) {
                  invalidFiles.push({
                    file,
                    reason: `Processing error: ${fileError instanceof Error ? fileError.message : String(fileError)}`
                  });

                }
              }

              if (fileModels.length > 0) {

                try {
                  this.files$.next(fileModels);


                  this.isCodeGenerationComplete = true;


                  this.isViewingSeedProjectTemplate = true;


                  this.setCodeTabEnabled('View seed project template');
                  this.isCodeTabEnabled = true;


                  this.handleAutomaticCodeTabSwitch();


                  this.userSelectedTab = true;

                  this.cdr.detectChanges();


                  if (invalidFiles.length > 0) {

                  }

                  this.cdr.markForCheck();
                } catch (loadingError) {
                                    this.setCodeTabError(
                    'Template loading failed',
                    'Failed to load files into Monaco Editor'
                  );
                }
              } else {

                this.setCodeTabDisabled('Code will be available during build phase');
              }
            } else {
              this.setCodeTabDisabled('Code will be available during build phase');
            }
          } catch (error) {

            this.setCodeTabDisabled('Code will be available during build phase');
          }
        },
        error: (error) => {
          
          this.setCodeTabDisabled('Code will be available during build phase');
        }
      });
  }


  private validateTemplateFile(file: any): { isValid: boolean; reason?: string } {

    if (!file) {
      return { isValid: false, reason: 'File object is null or undefined' };
    }


    if (!file.name && !file.fileName) {
      return { isValid: false, reason: 'File has no name or fileName property' };
    }


    const fileName = file.name || file.fileName;
    if (typeof fileName !== 'string' || fileName.trim() === '') {
      return { isValid: false, reason: 'File name is not a valid string' };
    }


    if (file.content === null || file.content === undefined) {
      return { isValid: false, reason: 'File content is null or undefined' };
    }


    if (typeof file.content !== 'string') {
      return { isValid: false, reason: 'File content is not a string' };
    }


    if (fileName.length > 255) {
      return { isValid: false, reason: 'File name is too long (>255 characters)' };
    }


    if (file.content.length > 50 * 1024 * 1024) {
      return { isValid: false, reason: 'File content is too large (>50MB)' };
    }

    return { isValid: true };
  }


  private getFileTypesSummary(files: FileModel[]): Record<string, number> {
    const summary: Record<string, number> = {};

    files.forEach(file => {
      const fileName = file.fileName || file.name || '';
      let extension = fileName.split('.').pop()?.toLowerCase() || 'no-extension';


      const extensionGroups: Record<string, string> = {
        'js': 'javascript',
        'jsx': 'javascript',
        'ts': 'typescript',
        'tsx': 'typescript',
        'css': 'styles',
        'scss': 'styles',
        'sass': 'styles',
        'less': 'styles',
        'html': 'markup',
        'htm': 'markup',
        'xml': 'markup',
        'json': 'config',
        'yaml': 'config',
        'yml': 'config',
        'toml': 'config',
        'ini': 'config',
        'md': 'documentation',
        'txt': 'documentation',
        'rst': 'documentation'
      };

      const category = extensionGroups[extension] || extension;
      summary[category] = (summary[category] || 0) + 1;
    });

    return summary;
  }

  private setupRegenerationIntegration(): void {

    this.regenerationCheckpointService.getCheckpointProgress()
      .pipe(takeUntil(this.destroy$))
      .subscribe(progress => {
        if (progress) {

          this.checkpointProcessedEvents.update(count => count + 1);
        }
      });

    this.regenerationCheckpointService.getSessionStateChanges()
      .pipe(takeUntil(this.destroy$))
      .subscribe(sessionState => {
        if (sessionState) {
        }
      });

    this.regenerationCheckpointService.getEventProcessingErrors()
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
      });

    this.regenerationIntegrationService.fileUpdates$
      .pipe(takeUntil(this.destroy$))
      .subscribe(update => {

        if (update.replaceAll) {

        } else {

        }
      });

    this.regenerationIntegrationService.accordionCreate$
      .pipe(takeUntil(this.destroy$))
      .subscribe(accordionData => {
        this.createRegenerationAccordion(accordionData);
      });

    this.regenerationIntegrationService.uiStateUpdates$
      .pipe(takeUntil(this.destroy$))
      .subscribe(stateUpdate => {
        this.handleRegenerationUIStateUpdate(stateUpdate);
      });

  }

  private subscribeToUIDesignService(): void {

    this.generateUIDesignService.uiDesignResponse$
      .pipe(takeUntil(this.destroy$))
      .subscribe(responseData => {
        if (responseData) {
          this.handleUIDesignResponseForOverview(responseData);
        }
      });

    this.generateUIDesignService.showOverviewTab$.pipe(takeUntil(this.destroy$)).subscribe(show => {
      this.showUIDesignOverviewTab$.next(show);
      if (show) {

        this.currentView$.next('overview');
      }
    });
  }

  private handleUIDesignResponseForOverview(responseData: any): void {

    try {

      const pages: MobilePage[] = [];

      if (responseData.pages && Array.isArray(responseData.pages)) {
        responseData.pages.forEach((page: any) => {
          if (page.fileName && page.content) {
            pages.push({
              fileName: page.fileName,
              content: page.content,
            });
          }
        });
      }

      if (pages.length > 0) {
        this.uiDesignPages$.next(pages);
        this.currentUIDesignPageIndex$.next(0);
        // Sync web pages
        const webPages = this.convertMobilePagesToWebPages(pages);
        this.webPages$.next(webPages);
        this.currentWebPageIndex$.next(0);
        this.showUIDesignOverviewTab$.next(true);
      } else {
      }
    } catch (error) {
    }
  }

  private initializeUIDesignChatMessages(): void {

    this.lightMessages = this.lightMessages.filter(msg => {
      const isTemporaryLoadingMessage =
        msg.text.includes('Generating') || msg.text.includes('Processing');
      const isAIMessage =
        msg.from === 'ai' &&
        msg.id &&
        (msg.id.startsWith('ai-generation-') || msg.id.startsWith('ai-regeneration-'));

      return !isTemporaryLoadingMessage || isAIMessage;
    });

    this.cleanupLegacyEditMessages();

    let userPrompt = '';

    const uiDesignPrompt = this.generateUIDesignService.getPromptData();
    if (uiDesignPrompt && uiDesignPrompt.trim()) {
      userPrompt = uiDesignPrompt;
    }

    if (!userPrompt) {
      const projectState = this.appStateService.getState().project;
      if (projectState.prompt && projectState.prompt.trim()) {
        userPrompt = projectState.prompt;
      }
    }

    if (!userPrompt) {
      try {

        this.promptService.promptData$.pipe(take(1)).subscribe(promptData => {
          if (promptData && typeof promptData === 'object' && 'prompt' in promptData) {
            const prompt = (promptData as any).prompt;
            if (prompt && typeof prompt === 'string' && prompt.trim()) {
              userPrompt = prompt;
            }
          }
        });
      } catch (error) {
      }
    }

    if (userPrompt) {

      const existingUserMessage = this.lightMessages.find(
        msg => msg.from === 'user' && msg.text === userPrompt
      );

      if (!existingUserMessage) {

        const userMessageId = `user-prompt-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;

        this.lightMessages.push({
          id: userMessageId,
          text: userPrompt,
          from: 'user',
          theme: 'light',
        });
      } else {
      }

      const aiMessageId = `ai-generation-${Date.now()}`;

      const existingAIMessage = this.lightMessages.find(
        msg =>
          msg.from === 'ai' &&
          msg.id &&
          msg.id.startsWith('ai-generation-') &&
          this.activeAIMessageIds.has(msg.id)
      );

      if (!existingAIMessage) {

        this.lightMessages.push({
          id: aiMessageId,
          text: '',
          from: 'ai',
          theme: 'light',
          showIntroMessage: true,
          showLoadingIndicator: true,
          loadingPhase: 'intro',
          mainAPIInProgress: true,
        });

        this.activeAIMessageIds.add(aiMessageId);
        this.currentActiveMessageId = aiMessageId;

      } else {
        this.currentActiveMessageId = existingAIMessage.id || null;
      }

    } else {

    }

    this.isUIDesignLoading$.next(true);

    this.cdr.detectChanges();
  }

  shouldShowUIDesignLoadingIndicator(): boolean {
    const isUIDesignMode = this.isUIDesignMode$.value;
    const isGenerating = this.isUIDesignGenerating$.value;
    const isRegenerating = this.isUIDesignRegenerating$.value;
    const isApiInProgress = this.uiDesignApiInProgress$.value;

    return isUIDesignMode && (isGenerating || isRegenerating || isApiInProgress);
  }

  shouldShowCodeGenerationLoadingIndicator(): boolean {
    const isUIDesignMode = this.isUIDesignMode$.value;
    const isCodeGenerationLoading = this.isCodeGenerationLoading$.value;

    return !isUIDesignMode && isCodeGenerationLoading;
  }

  private subscribeToRegenerationProgress(): void {

    this.subscription?.add(
      this.newPollingResponseProcessor.progressDescription$
        .pipe(takeUntil(this.destroy$))
        .subscribe((progressDescription: string) => {
          if (progressDescription && typeof progressDescription === 'string' && progressDescription.trim() !== '') {

            this.codeRegenerationProgressDescription = this.getRegenerationProgressText(progressDescription);
            this.cdr.detectChanges();
          }
        })
    );

  }

  private getRegenerationProgressText(rawProgress: string): string {

    return rawProgress || 'Processing your request...';

  }


  private getLastMessage(): any {
    return this.lightMessages.length > 0 ? this.lightMessages[this.lightMessages.length - 1] : null;
  }


  private prepareForRegeneration(): void {
    if (this.projectId && this.jobId) {

      this.enhancedSSEService.prepareForRegeneration(this.projectId, this.jobId);

      const sessionKey = this.regenerationCheckpointService.startCheckpointSession(
        this.projectId,
        this.jobId
      );

      this.currentCheckpointSession.set(sessionKey);
      this.checkpointProcessedEvents.set(0);

    }
  }


  private createRegenerationSuccessResult(codeFiles: FileData[]): void {

    if (!codeFiles || codeFiles.length === 0) {
      return;
    }


    this.addRegenerationGenerationAccordionToChat(codeFiles);

    this.cdr.detectChanges();
  }

  private createRegenerationErrorResult(errorMessage: string): void {

    this.generationVersionCounter++;

    const projectName = this.projectName || 'Untitled Project';

    const generationResult: GenerationResult = {
      type: 'error',
      version: this.generationVersionCounter,
      projectName: projectName,
      errorMessage: errorMessage || 'An unexpected error occurred during regeneration.',
      timestamp: new Date()
    };

    if (this.chatWindow) {
      this.chatWindow.addGenerationResult(generationResult);
    } else {
    }

    this.cdr.detectChanges();
  }


  private isIntroMessage(message: any): boolean {
    if (!message || !message.text) {
      return false;
    }

    const text = message.text.toLowerCase();

    const introPatterns = [
      'preparing your code',
      'getting ready to',
      'setting up code',
      'initializing code',
      'analyzing your request',
      'understanding your changes',
      'reviewing your code',
      'processing your edit request',
      'examining the codebase',
      'evaluating your modifications'
    ];

    const isIntro = introPatterns.some(pattern => text.includes(pattern));

    const completionPatterns = [
      'edit completed',
      'regeneration completed',
      'successfully updated',
      'processing complete'
    ];

    const isCompletion = completionPatterns.some(pattern => text.includes(pattern));

    return isIntro && !isCompletion;
  }

  private addCompletionMessageWithTypewriter(message: string): void {

    const messageId = `ai-completion-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
    const completionMessage = {
      id: messageId,
      text: '',
      from: 'ai' as const,
      theme: 'light' as const,
      isTyping: true,
      showLoadingIndicator: false,
      loadingPhase: 'completed' as const,
      mainAPIInProgress: false
    };

    this.lightMessages.push(completionMessage);
    this.cdr.markForCheck();

    this.startTypewriterEffectForMessage(message, messageId);
  }

  private startTypewriterEffectForMessage(fullText: string, messageId: string): void {

    const ULTRA_FAST_TYPING_SPEED = 3;
    let charIndex = 0;

    const typeNextCharacter = () => {
      const targetMessage = this.lightMessages.find(msg => msg.id === messageId);
      if (!targetMessage) {
        return;
      }

      if (charIndex >= fullText.length) {
        targetMessage.isTyping = false;
        this.cdr.markForCheck();
        return;
      }

      targetMessage.text = fullText.substring(0, charIndex + 1);
      charIndex++;

      this.cdr.markForCheck();

      setTimeout(typeNextCharacter, ULTRA_FAST_TYPING_SPEED);
    };

    typeNextCharacter();
  }


  private startUIDesignGeneration(): void {

    this.isUIDesignGenerating$.next(true);
    this.uiDesignError$.next(null);
    this.uiDesignApiInProgress$.next(false);

    this.uiDesignNodes$.next([]);

    this.createLoadingNodes();

    const uiDesignData = this.generateUIDesignService.getUIDesignData();

    if (uiDesignData) {

      this.initiateParallelUIDesignAPICalls(uiDesignData);
    } else {
      this.showUIDesignError('No UI Design data found. Please go back and submit a prompt.');
    }
  }

  private createLoadingNodes(): void {

    const loadingNodes: UIDesignNode[] = [
      {
        id: 'ui-design-loading-node',
        type: 'ui-design',
        data: {
          title: 'Generating UI Design...',
          htmlContent: this.sanitizer.bypassSecurityTrustHtml(''),
          rawContent: '',
          width: 300,
          height: 400,
          isLoading: true,
        },
        position: { x: 0, y: 0 },
        selected: false,
        dragging: false,
        visible: true,
      },
    ];

    this.uiDesignNodes$.next(loadingNodes);
  }

  private initiateParallelUIDesignAPICalls(uiDesignData: any): void {

    this.uiDesignApiInProgress$.next(true);
    this.uiDesignError$.next(null);

    this.wireframeGenerationStateService.startGeneration();

    // Get image data from app state service
    const currentAppState = this.appStateService.getProjectState();
    const imageDataUri = currentAppState.imageDataUri;
    const images = imageDataUri ? [imageDataUri] : [];

    // Build API request with image data
    const apiRequest = this.generateUIDesignService.buildAPIRequest(images);

    const mainAPICall = this.generateUIDesignService.generateUIDesign(apiRequest);

    const userRequest = uiDesignData.prompt || '';

    this.uiDesignIntroService
      .executeParallelGeneration(userRequest, mainAPICall, this.currentActiveMessageId || undefined)
      .subscribe({
        next: result => {

          if (result.mainAPISuccess) {
            this.handleUIDesignSuccess(result.mainAPIResult);

            this.uiDesignIntroService.completeTextReplacement();
          } else {
            this.handleUIDesignFailure(new Error('Main API failed'));
          }
        },
        error: error => {
          this.handleUIDesignFailure(error);
        },
      });

    this.uiDesignIntroService.introMessageState$
      .pipe(takeUntil(this.destroy$))
      .subscribe((state: IntroMessageState) => {
        this.introMessageState$.next(state);

        if (state.shouldReplaceText && state.targetMessageId && state.text) {
          this.handleChatMessageTextReplacement(state);
        }

        this.cdr.markForCheck();
      });

  }

  private handleChatMessageTextReplacement(state: IntroMessageState): void {

    if (!state.targetMessageId || !state.text) {
      return;
    }

    if (!this.activeAIMessageIds.has(state.targetMessageId)) {
      return;
    }

    const targetMessage = this.lightMessages.find(msg => msg.id === state.targetMessageId);
    if (!targetMessage) {
      return;
    }

    if (!targetMessage.originalText) {
      targetMessage.originalText = targetMessage.text || '';
    }

    targetMessage.text = state.text;
    targetMessage.showLoadingIndicator = state.showLoadingIndicator;
    targetMessage.loadingPhase = state.loadingPhase;
    targetMessage.mainAPIInProgress = state.mainAPIInProgress;

    this.cdr.markForCheck();
  }


  private cleanupRegenerationSession(sessionId: string): void {
    if (this.activeRegenerationSessions.has(sessionId)) {
      const session = this.activeRegenerationSessions.get(sessionId);
      if (session) {

        this.activeAIMessageIds.delete(session.aiMessageId);

        if (this.currentActiveMessageId === session.aiMessageId) {
          this.currentActiveMessageId = null;
        }

        this.activeRegenerationSessions.delete(sessionId);

      }
    }
  }

  private restoreOriginalChatMessageText(): void {
    if (!this.currentActiveMessageId) {
      return;
    }

    const targetMessage = this.lightMessages.find(msg => msg.id === this.currentActiveMessageId);
    if (targetMessage && targetMessage.originalText) {

      targetMessage.text = targetMessage.originalText;
      delete targetMessage.originalText;

      targetMessage.showLoadingIndicator = false;
      targetMessage.loadingPhase = 'completed';
      targetMessage.mainAPIInProgress = false;

      if (this.currentActiveMessageId.startsWith('ai-regeneration-')) {

        const sessionId = this.currentActiveMessageId.replace('ai-regeneration-', '');
        this.cleanupRegenerationSession(sessionId);
      } else {

        this.activeAIMessageIds.delete(this.currentActiveMessageId);
        this.currentActiveMessageId = null;
      }

      this.cdr.markForCheck();
    }
  }


  private handleUIDesignSuccess(response: any): void {

    this.isUIDesignGenerating$.next(false);
    this.uiDesignApiInProgress$.next(false);
    this.uiDesignError$.next(null);
    this.isUIDesignLoading$.next(false);

    this.restoreOriginalChatMessageText();

    try {
      // Handle new API response structure with wireframes array
      if (this.isNewUIDesignResponse(response)) {
        this.processNewUIDesignResponse(response);
      } else if (this.isUIDesignResponse(response)) {
        // Fallback to legacy response processing for backward compatibility
        this.processUIDesignResponse(response);
      } else {
        this.showUIDesignError('Invalid response format from wireframe generation API');
      }
    } catch (error) {
      this.showUIDesignError('Failed to process wireframe generation response');
    }
  }

  private handleUIDesignFailure(error: any): void {

    this.isUIDesignGenerating$.next(false);
    this.uiDesignApiInProgress$.next(false);
    this.isWireframeGenerationComplete$.next(false);
    this.isUIDesignLoading$.next(false);

    const errorMessage = error?.message || error?.error?.message || 'Wireframe generation failed';
    this.uiDesignError$.next(errorMessage);

    this.wireframeGenerationStateService.setError(errorMessage);

    this.showUIDesignError(errorMessage);
  }

  private showUIDesignError(message: string): void {

    this.clearAllLoadingNodes();

    this.uiDesignSelectionService.setEditingInProgress(false);
    this.isUIDesignRegenerating$.next(false);
    this.isUIDesignLoading$.next(false);

    this.toastService.error(message);

    const errorNode: UIDesignNode = {
      id: 'error-node',
      type: 'ui-design',
      data: {
        title: 'Error',
        htmlContent: this.sanitizer.bypassSecurityTrustHtml(`
          <div style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 100%; padding: 20px; text-align: center;">
            <div style="font-size: 48px; color: #ef4444; margin-bottom: 16px;">⚠️</div>
            <div style="font-size: 16px; color: #374151; margin-bottom: 8px;">Generation Failed</div>
            <div style="font-size: 14px; color: #6b7280;">${message}</div>
          </div>
        `),
        rawContent: '',
        width: 400,
        height: 250,
        isLoading: false,
      },
      position: { x: -200, y: -125 },
      selected: false,
      dragging: false,
      visible: true,
    };

    this.uiDesignNodes$.next([errorNode]);
  }

  private initializeUIDesignCanvas(): void {

  }

  onUIDesignNodeDoubleClick(node: UIDesignNode): void {
    this.selectedUIDesignNode$.next(node);
    this.isUIDesignFullScreenOpen$.next(true);

    const userApplicationTarget = this.generateUIDesignService.getApplicationTarget();
    this.uiDesignViewMode$.next(userApplicationTarget || 'mobile');

  }

  closeUIDesignFullScreen(): void {
    this.isUIDesignFullScreenOpen$.next(false);
    this.selectedUIDesignNode$.next(null);
  }

  switchUIDesignViewMode(mode: 'mobile' | 'web'): void {
    this.uiDesignViewMode$.next(mode);
  }

  toggleUIDesignModalFullScreen(): void {
    const isCurrentlyFullScreen = this.isUIDesignModalFullScreen$.value;
    this.isUIDesignModalFullScreen$.next(!isCurrentlyFullScreen);
  }

  exitUIDesignModalFullScreen(): void {
    this.isUIDesignModalFullScreen$.next(false);
  }

  openUIDesignInNewTab(): void {
    const selectedNode = this.selectedUIDesignNode$.value;
    if (!selectedNode?.data?.rawContent) {
      return;
    }

    try {

      const newWindow = window.open('', '_blank');

      if (newWindow) {

        newWindow.document.write(selectedNode.data.rawContent);
        newWindow.document.close();

        const title =
          selectedNode.data.displayTitle || selectedNode.data.title || 'UI Design Preview';
        newWindow.document.title = title;

      } else {

        this.openUIDesignWithBlobUrl(
          selectedNode.data.rawContent
        );
      }
    } catch (error) {

      this.openUIDesignWithBlobUrl(
        selectedNode.data.rawContent
      );
    }
  }

  private openUIDesignWithBlobUrl(content: string): void {
    try {

      const blob = new Blob([content], { type: 'text/html' });
      const blobUrl = URL.createObjectURL(blob);

      const newWindow = window.open(blobUrl, '_blank');

      if (newWindow) {

        setTimeout(() => {
          URL.revokeObjectURL(blobUrl);
        }, 1000);

      } else {
      }
    } catch (error) {
    }
  }

  openUIDesignCodeViewer(): void {
    const selectedNode = this.selectedUIDesignNode$.value;
    if (!selectedNode?.data?.rawContent) {
      this.toastService.warning('No code content available to view');
      return;
    }

    this.updateCodeData();

    this.isUIDesignCodeViewerOpen$.next(true);
  }

  copyUIDesignCodeToClipboard(): void {
    const selectedNode = this.selectedUIDesignNode$.value;
    if (!selectedNode?.data?.rawContent) {
      this.toastService.warning('No code content available to copy');
      return;
    }

    try {
      navigator.clipboard.writeText(selectedNode.data.rawContent).then(() => {
        this.toastService.success('Code copied to clipboard');
      }).catch(() => {
        this.toastService.error('Failed to copy code to clipboard');
      });
    } catch (error) {
      this.toastService.error('Failed to copy code to clipboard');
    }
  }

  downloadUIDesignCode(): void {
    const selectedNode = this.selectedUIDesignNode$.value;
    if (!selectedNode?.data?.rawContent) {
      this.toastService.warning('No code content available to download');
      return;
    }

    try {

      const title = selectedNode.data.displayTitle || selectedNode.data.title || 'ui-design';
      const filename = `${title.toLowerCase().replace(/[^a-z0-9]/g, '-')}.html`;

      const blob = new Blob([selectedNode.data.rawContent], { type: 'text/html' });

      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      link.style.display = 'none';

      document.body.appendChild(link);
      link.click();

      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      this.toastService.success(`Code downloaded as ${filename}`);
    } catch (error) {
      this.toastService.error('Failed to download code');
    }
  }

  downloadUIDesignScreenshot(): void {
    const selectedNode = this.selectedUIDesignNode$.value;
    if (!selectedNode?.data?.rawContent) {
      this.toastService.warning('No content available for screenshot');
      return;
    }

    try {
      // Create a temporary iframe to capture the screenshot
      const iframe = document.createElement('iframe');
      iframe.style.position = 'absolute';
      iframe.style.left = '-9999px';
      iframe.style.width = '393px';
      iframe.style.height = '852px';
      iframe.srcdoc = selectedNode.data.rawContent;

      document.body.appendChild(iframe);

      iframe.onload = () => {
        try {
          // Use html2canvas or similar library to capture screenshot
          // For now, we'll show a message that screenshot functionality needs implementation
          this.toastService.info('Screenshot functionality will be implemented soon');
          document.body.removeChild(iframe);
        } catch (error) {
          this.toastService.error('Failed to capture screenshot');
          document.body.removeChild(iframe);
        }
      };
    } catch (error) {
      this.toastService.error('Failed to prepare screenshot');
    }
  }

  private _codeLines: string[] = [];
  private _lastProcessedContent: string = '';

  public codeLineNumbers: number[] = [];
  public highlightedCode: string = '';
  public codeFileName: string = '';

  getCodeFileName(): string {
    return this.codeFileName;
  }

  private updateCodeData(): void {
    const selectedNode = this.selectedUIDesignNode$.value;
    if (!selectedNode?.data?.rawContent) {
      this.codeLineNumbers = [];
      this.highlightedCode = '';
      this.codeFileName = 'ui-design.html';
      return;
    }

    const currentContent = selectedNode.data.rawContent;

    if (this._lastProcessedContent !== currentContent) {
      this._codeLines = currentContent.split('\n');
      this.codeLineNumbers = Array.from({ length: this._codeLines.length }, (_, i) => i + 1);
      this.highlightedCode = this.generateHighlightedCode(currentContent);

      const title = selectedNode.data.displayTitle || selectedNode.data.title || 'ui-design';
      this.codeFileName = `${title.toLowerCase().replace(/[^a-z0-9]/g, '-')}.html`;

      this._lastProcessedContent = currentContent;

    }
  }

  private generateHighlightedCode(content: string): string {
    if (!content) {
      return '';
    }

    let code = content
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#39;');


    code = code

      .replace(/(&lt;\/?)([a-zA-Z][a-zA-Z0-9]*)(.*?)(&gt;)/g,
        '<span class="html-tag">$1</span><span class="html-tag-name">$2</span><span class="html-attributes">$3</span><span class="html-tag">$4</span>')

      .replace(/(\w+)(=)(&quot;[^&]*&quot;)/g,
        '<span class="html-attr-name">$1</span><span class="html-operator">$2</span><span class="html-attr-value">$3</span>')

      .replace(/(&lt;!--.*?--&gt;)/g, '<span class="html-comment">$1</span>')

      .replace(/(&lt;!DOCTYPE.*?&gt;)/gi, '<span class="html-doctype">$1</span>');

    return code;
  }


  trackByLineNumber(_index: number, lineNumber: number): number {
    return lineNumber;
  }


  onCodeScroll(event: Event): void {
    const codeContent = event.target as HTMLElement;
    const lineNumbers = codeContent.parentElement?.querySelector('.line-numbers') as HTMLElement;

    if (lineNumbers) {
      lineNumbers.scrollTop = codeContent.scrollTop;
    }
  }


  closeUIDesignCodeViewer(): void {
    this.isUIDesignCodeViewerOpen$.next(false);


    this._codeLines = [];
    this._lastProcessedContent = '';


    this.codeLineNumbers = [];
    this.highlightedCode = '';
    this.codeFileName = '';

  }


  processUIDesignResponse(response: string | UIDesignAPIResponse[] | WireframeAPIResponse[]): void {


    this.clearAllLoadingNodes();

    try {
      let pages: any[] = [];


      if (typeof response === 'string') {
        try {
          pages = JSON.parse(response);
        } catch (parseError) {
          this.showUIDesignError('Failed to parse response data');
          return;
        }
      } else if (Array.isArray(response)) {
        pages = response;
      } else {
        this.showUIDesignError('Invalid response format');
        return;
      }


      if (!Array.isArray(pages) || pages.length === 0) {
        this.showUIDesignError('No pages found in response');
        return;
      }


      const uiDesignPages: UIDesignPageData[] = pages.map((page, index) => {
        let pageName: string;
        let content: string;


        if ('fileName' in page) {

          pageName = this.extractPageNameFromFileName(page.fileName);
          content = page.content;
        } else if ('pageName' in page) {

          pageName = page.pageName?.trim() || `Page ${index + 1}`;
          content = page.content;
        } else {

          pageName = `Page ${index + 1}`;
          content = page.content || '<html><body><p>No content available</p></body></html>';
        }

        return {
          fileName: pageName,
          content: content || '<html><body><p>No content available</p></body></html>',
        };
      });


      this.createUIDesignNodes(uiDesignPages);
    } catch (error) {
      this.showUIDesignError('Failed to process response data');
    }
  }


  private extractPageNameFromFileName(fileName: string): string {
    if (!fileName) {
      return 'Untitled Page';
    }

    try {

      const transformResult = this.uiDesignFilenameTransformerService.transformFileName(fileName);


      const displayTitle = transformResult.displayTitle || 'Untitled Page';

      return displayTitle;
    } catch (error) {
      return 'Untitled Page';
    }
  }


  private async createUIDesignNodes(pages: UIDesignPageData[]): Promise<void> {

    try {


      this.clearAllLoadingNodes();


      const currentNodes = this.uiDesignNodes$.value;


      const wireframePages = pages.map(page => ({
        fileName: page.fileName,
        content: page.content,
        pageName: page.fileName,
      }));


      const positionCalculator = (count: number, _existingNodes: any[]) => {
        const positioningResult =
          this.uiDesignNodePositioningService.calculateInitialGenerationPositions(count);
        return positioningResult.positions;
      };


      const result = await this.wireframeNodeManagementService.processRegenerationResponse(
        wireframePages,
        currentNodes,
        positionCalculator
      );


      this.uiDesignNodes$.next(result.updatedNodes);


      this.isUIDesignMode$.next(true);
      this.isHistoryActive$.next(false);


      if (result.updatedNodes.length > 0) {
        const nodePositions = result.updatedNodes.map(node => node.position);
        const optimalViewport =
          this.uiDesignNodePositioningService.calculateOptimalViewport(nodePositions);


        setTimeout(() => {
          this.centerCanvasOnNodesWithViewport(optimalViewport);
        }, 100);
      }


      const mobilePages: MobilePage[] = pages.map(page => ({
        fileName: page.fileName,
        content: page.content,
      }));


      const responseData = {
        pages: mobilePages,
        jobId: 'ui-design-' + Date.now(),
        projectId: 'ui-design-project-' + Date.now(),
      };

      this.generateUIDesignService.setUIDesignResponse(responseData);


      this.updateUIDesignChatMessagesWithSummary(result.summary);


      this.uiDesignSelectionService.setNodesCreated(true);


      this.isWireframeGenerationComplete$.next(true);
      this.wireframeGenerationStateService.completeGeneration(result.updatedNodes.length);
    } catch (error) {
      this.showUIDesignError(
        'Failed to create design nodes: ' +
          (error instanceof Error ? error.message : 'Unknown error')
      );
    }
  }


  private updateUIDesignChatMessagesWithSummary(summary: any): void {

    this.lightMessages = this.lightMessages.filter(msg => {
      const isTemporaryLoadingMessage =
        msg.text.includes('Generating') || msg.text.includes('Processing');
      const isAIMessage =
        msg.from === 'ai' &&
        msg.id &&
        (msg.id.startsWith('ai-generation-') || msg.id.startsWith('ai-regeneration-'));


      return !isTemporaryLoadingMessage || isAIMessage;
    });


    let message = 'Wireframe processing completed successfully! ';

    if (summary.created > 0 && summary.updated > 0) {
      message += `Created ${summary.created} new page${summary.created > 1 ? 's' : ''} and updated ${summary.updated} existing page${summary.updated > 1 ? 's' : ''}.`;
    } else if (summary.created > 0) {
      message += `Created ${summary.created} new page${summary.created > 1 ? 's' : ''}.`;
    } else if (summary.updated > 0) {
      message += `Updated ${summary.updated} existing page${summary.updated > 1 ? 's' : ''}.`;
    } else {
      message += `Processed ${summary.totalProcessed} page${summary.totalProcessed > 1 ? 's' : ''}.`;
    }

    message +=
      ' Single-click to select a page for editing, or double-click to view in full-screen mode.';


    const messageId = `ai-success-summary-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;

    this.lightMessages.push({
      id: messageId,
      text: '',
      from: 'ai',
      theme: 'light',
    });


    this.startTypewriterEffectForMessage(message, messageId);

    this.cdr.markForCheck();
  }


  handleUIDesignResponse(response: any): void {


    if (this.isUIDesignResponse(response)) {
      this.processUIDesignResponse(response);
    } else {
    }
  }


  /**
   * Check if response matches the new API structure with wireframes array
   */
  private isNewUIDesignResponse(response: any): boolean {
    try {
      const isValid = (
        response &&
        typeof response === 'object' &&
        Array.isArray(response.wireframes) &&
        typeof response.project_id === 'string' &&
        typeof response.commit_hash === 'string' &&
        response.wireframes.every((wireframe: any) =>
          wireframe &&
          typeof wireframe.content === 'string' &&
          typeof wireframe.fileName === 'string'
        )
      );

      if (isValid) {
        console.log('✅ New API response structure detected:', {
          wireframesCount: response.wireframes.length,
          projectId: response.project_id,
          commitHash: response.commit_hash.substring(0, 8) + '...'
        });
      }

      return isValid;
    } catch (error) {
      console.warn('⚠️ Error validating new UI design response structure:', error);
      return false;
    }
  }

  /**
   * Process the new API response structure with wireframes array
   */
  private processNewUIDesignResponse(response: any): void {
    this.clearAllLoadingNodes();

    try {
      console.log('🔄 Processing new UI design response structure:', {
        wireframesCount: response.wireframes?.length,
        projectId: response.project_id,
        commitHashPreview: response.commit_hash?.substring(0, 8) + '...'
      });

      // Store project_id and commit_hash in the service
      if (response.project_id) {
        this.generateUIDesignService.setProjectId(response.project_id);
        console.log('✅ Project ID stored:', response.project_id);
      }

      if (response.commit_hash) {
        this.generateUIDesignService.setCommitHash(response.commit_hash);
        console.log('✅ Commit hash stored:', response.commit_hash.substring(0, 8) + '...');
      }

      // Extract wireframes array
      const wireframes = response.wireframes;

      if (!Array.isArray(wireframes) || wireframes.length === 0) {
        console.error('❌ No wireframes found in new API response');
        this.showUIDesignError('No wireframes found in response');
        return;
      }

      // Validate wireframe structure
      const invalidWireframes = wireframes.filter(w => !w.content || !w.fileName);
      if (invalidWireframes.length > 0) {
        console.error('❌ Invalid wireframe structure detected:', invalidWireframes);
        this.showUIDesignError('Invalid wireframe data in response');
        return;
      }

      // Convert wireframes to the expected format for processing
      const uiDesignPages: UIDesignPageData[] = wireframes.map((wireframe) => ({
        fileName: wireframe.fileName,
        content: wireframe.content,
        pageName: this.extractPageNameFromFileName(wireframe.fileName),
      }));

      console.log('✅ Converted wireframes to UI design pages:', {
        pagesCount: uiDesignPages.length,
        fileNames: uiDesignPages.map(p => p.fileName)
      });

      // Process the pages using existing logic
      this.createUIDesignNodes(uiDesignPages);

    } catch (error) {
      console.error('❌ Error processing new wireframe generation response:', error);
      this.showUIDesignError('Failed to process new wireframe generation response: ' +
        (error instanceof Error ? error.message : 'Unknown error'));
    }
  }

  private isUIDesignResponse(response: any): boolean {

    if (Array.isArray(response)) {
      return response.every(item => {
        if (typeof item !== 'object' || !item.content) {
          return false;
        }


        const hasPageName = 'pageName' in item;


        const hasFileName = 'fileName' in item;


        return hasPageName || hasFileName;
      });
    }


    if (typeof response === 'string') {
      try {
        const parsed = JSON.parse(response);
        return this.isUIDesignResponse(parsed);
      } catch {
        return false;
      }
    }

    return false;
  }


  private isUIDesignArtifact(artifactData: any): boolean {

    if (!artifactData || !artifactData.content) {
      return false;
    }


    return this.isUIDesignResponse(artifactData.content);
  }


  onCanvasMouseDown(event: MouseEvent): void {
    if (event.button === 0) {

      this.uiDesignCanvasService.startDragging(event.clientX, event.clientY);
    }
  }


  onCanvasMouseMove(event: MouseEvent): void {
    const viewport = this.uiDesignCanvasService.getViewport();

    if (viewport.isDragging) {
      const deltaX = event.clientX - viewport.lastMouseX;
      const deltaY = event.clientY - viewport.lastMouseY;

      this.uiDesignCanvasService.panCanvas(deltaX, deltaY);
      this.uiDesignCanvasService.updateViewport({
        lastMouseX: event.clientX,
        lastMouseY: event.clientY,
      });
    }
  }


  onCanvasMouseUp(_event: MouseEvent): void {
    this.uiDesignCanvasService.stopDragging();
  }


  onUIDesignNodeSelect(node: UIDesignNode, event?: MouseEvent): void {
    if (!this.isUIDesignMode$.value) {
      return;
    }


    const isMultiSelect = event && (event.ctrlKey || event.metaKey || event.shiftKey);


    const selectionData: MultiSelectedNodeData = {
      nodeId: node.id,
      fileName: node.data.displayTitle || node.data.title,
      htmlContent: node.data.htmlContent as string,
      rawContent: node.data.rawContent,
      selectedImages: [],
      metadata: {
        nodePosition: node.position,
        nodeDimensions: { width: node.data.width, height: node.data.height },
        selectionTimestamp: Date.now(),
      },
    };


    this.uiDesignSelectionService.selectMultipleNodes(
      node.id,
      selectionData,
      isMultiSelect || false
    );


    this.uiDesignVisualFeedbackService.toggleNodeSelection(node.id, isMultiSelect || false);

  }


  private updateNodeSelectionVisuals(): void {


    this.cdr.markForCheck();

  }


  onClearNodeSelection(): void {

    this.uiDesignSelectionService.clearSelection();


    this.uiDesignVisualFeedbackService.clearSelection();


    const currentNodes = this.uiDesignNodes$.value;
    const updatedNodes = currentNodes.map(node => ({
      ...node,
      selected: false,
    }));

    this.uiDesignNodes$.next(updatedNodes);
    this.cdr.markForCheck();

  }


  private generateRegenerationSessionId(): string {
    this.regenerationSessionCounter++;
    return `regen-session-${Date.now()}-${this.regenerationSessionCounter}-${Math.random().toString(36).substring(2, 11)}`;
  }


  private handleSpecialCommands(prompt: string): boolean {
    const trimmedPrompt = prompt.trim().toLowerCase();

    if (trimmedPrompt.startsWith('/intro')) {
      this.handleIntroCommand(prompt);
      return true;
    }

    if (trimmedPrompt.startsWith('/wireframe-generation')) {
      this.handleWireframeGenerationCommand(prompt);
      return true;
    }

    return false;
  }


  private handleIntroCommand(prompt: string): void {


    const userRequest = prompt.replace(/^\/intro\s*/i, '').trim() || 'Generate an intro message';


    this.lightMessages.push({
      text: prompt,
      from: 'user',
      theme: 'light'
    });


    this.codeGenerationIntroService.createAndDisplayIntroCardWithShimmer(
      userRequest,
      [],
      this.chatWindow
    ).subscribe({
      next: (messageId) => {
      },
      error: (error) => {
        this.toastService.error('Failed to generate intro message. Please try again.');
      }
    });
  }


  private handleWireframeGenerationCommand(prompt: string): void {


    const userRequest = prompt.replace(/^\/wireframe-generation\s*/i, '').trim() || 'Generate a wireframe';


    this.lightMessages.push({
      text: prompt,
      from: 'user',
      theme: 'light'
    });


    this.uiDesignIntroService.createAndDisplayIntroCardWithShimmer(
      userRequest,
      this.chatWindow
    ).subscribe({
      next: (messageId) => {
      },
      error: (error) => {
        this.toastService.error('Failed to generate wireframe intro message. Please try again.');
      }
    });
  }


  onUIDesignEditPrompt(prompt: string): void {
    if (!this.isUIDesignMode$.value) {
      return;
    }


    if (this.handleSpecialCommands(prompt)) {
      return;
    }

    const selectedNodes = this.uiDesignSelectionService.getSelectedNodes();
    if (selectedNodes.length === 0) {
      this.toastService.error('Please select one or more pages to edit');
      return;
    }


    this.cleanupLegacyEditMessages();


    this.createLoadingNodesForRegeneration(selectedNodes);


    this.uiDesignSelectionService.setEditingInProgress(true);
    this.isUIDesignRegenerating$.next(true);
    this.isUIDesignLoading$.next(true);


    const sessionId = this.generateRegenerationSessionId();


    const aiMessageId = `ai-regeneration-${sessionId}`;


    this.lightMessages.push({
      id: aiMessageId,
      text: '',
      from: 'ai' as const,
      theme: 'light' as const,
      showIntroMessage: true,
      showLoadingIndicator: true,
      loadingPhase: 'intro',
      mainAPIInProgress: true,
    });


    this.activeRegenerationSessions.set(sessionId, {
      aiMessageId: aiMessageId,
      timestamp: Date.now(),
      isActive: true,
      sessionId,
      prompt,
      selectedNodes: [...selectedNodes],
    });


    this.activeAIMessageIds.add(aiMessageId);
    this.currentActiveMessageId = aiMessageId;


    const editRequest = this.uiDesignSelectionService.buildEditRequest(prompt);
    if (!editRequest) {
      this.handleEditFailure('Failed to build edit request');
      return;
    }


    const mainEditAPICall = this.uiDesignEditService.editUIDesignPage(editRequest);


    this.uiDesignIntroService
      .executeParallelRegeneration(
        prompt,
        selectedNodes,
        mainEditAPICall,
        this.currentActiveMessageId || undefined
      )
      .subscribe({
        next: result => {

          if (result.mainAPISuccess) {
            this.handleEditSuccess(result.mainAPIResult, selectedNodes);


          } else {
            this.handleEditFailure('Main edit API failed');
          }
        },
        error: error => {
          this.handleEditFailure(error.message || 'Parallel regeneration API calls failed');
        },
      });

    this.cdr.markForCheck();
  }


  private createLoadingNodesForRegeneration(selectedNodes: MultiSelectedNodeData[]): void {

    const currentNodes = this.uiDesignNodes$.value;
    const loadingNodes: UIDesignNode[] = [];


    const loadingMessage = this.generateLoadingMessage(selectedNodes);

    selectedNodes.forEach((selectedNode, index) => {

      const existingNode = currentNodes.find(node => node.id === selectedNode.nodeId);
      if (!existingNode) {
        return;
      }


      const loadingNode: UIDesignNode = {
        id: `loading-${selectedNode.nodeId}-${Date.now()}-${index}`,
        type: 'ui-design',
        data: {
          title: selectedNode.fileName,
          displayTitle: selectedNode.fileName,
          htmlContent: '',
          rawContent: '',
          width: existingNode.data.width,
          height: existingNode.data.height,
          isLoading: true,
          loadingMessage: loadingMessage,
          originalNodeId: selectedNode.nodeId,
        },
        position: { ...existingNode.position },
        selected: false,
        dragging: false,
        visible: true,
      };

      loadingNodes.push(loadingNode);
    });


    this.uiDesignLoadingNodes$.next(loadingNodes);

  }


  private generateLoadingMessage(selectedNodes: MultiSelectedNodeData[]): string {
    if (selectedNodes.length === 1) {
      return `Editing ${selectedNodes[0].fileName}...`;
    } else {
      return `Editing ${selectedNodes.length} selected pages...`;
    }
  }


  private clearLoadingNodes(): void {
    this.uiDesignLoadingNodes$.next([]);
  }


  private clearAllLoadingNodes(): void {


    this.uiDesignLoadingNodes$.next([]);


    const currentNodes = this.uiDesignNodes$.value;
    const nonLoadingNodes = currentNodes.filter(
      node =>
        !node.data.isLoading &&
        !node.id.startsWith('loading-') &&
        node.id !== 'ui-design-loading-node'
    );


    if (nonLoadingNodes.length !== currentNodes.length) {
      this.uiDesignNodes$.next(nonLoadingNodes);
    }

  }


  private handleEditSuccess(response: any, selectedNodes: MultiSelectedNodeData[]): void {


    this.clearLoadingNodes();


    const validatedResponse = this.uiDesignSelectionService.validateEditResponse(response);
    if (!validatedResponse) {
      this.handleEditFailure('Invalid response format from edit API');
      return;
    }


    this.updateMultipleNodesContent(validatedResponse, selectedNodes);


    const fileNames = selectedNodes.map(node => node.fileName);
    this.updateEditChatMessages(true, fileNames);


    this.restoreOriginalChatMessageText();


    this.uiDesignSelectionService.setEditingInProgress(false);
    this.isUIDesignRegenerating$.next(false);
    this.isUIDesignLoading$.next(false);


    if (this.isRegenerationInProgress$.value) {
      this.completeDirectRegenerationResponse();
    }

  }


  private handleEditFailure(errorMessage: string): void {


    this.clearAllLoadingNodes();


    this.updateEditChatMessages(false, '', errorMessage);


    this.uiDesignSelectionService.setEditingInProgress(false);
    this.isUIDesignRegenerating$.next(false);
    this.isUIDesignLoading$.next(false);


    if (this.isRegenerationInProgress$.value) {
      this.handleRegenerationFailure();
    }


    this.toastService.error(errorMessage);

    this.cdr.markForCheck();
  }


  public identifyTrulyNewFiles(
    currentNodes: UIDesignNode[],
    responseMap: Map<string, string>,
    selectedNodes: MultiSelectedNodeData[]
  ): {
    newFileNames: string[];
    existingFileNames: string[];
    responseFileNames: string[];
    duplicatesFiltered: string[];
    analysisDetails: any;
  } {


    const responseFileNames = Array.from(responseMap.keys());


    const existingNodeData = currentNodes.map(node => ({
      id: node.id,
      title: node.data.title,
      displayTitle: node.data.displayTitle,
    }));


    const analysisResults = responseFileNames.map(fileName => {

      const isSelectedNodeFile = selectedNodes.some(
        selected =>
          selected.fileName === fileName ||
          this.filenameNormalizationService.normalizeFilename(selected.fileName).canonicalKey ===
            this.filenameNormalizationService.normalizeFilename(fileName).canonicalKey
      );

      if (isSelectedNodeFile) {
        return {
          fileName,
          isDuplicate: true,
          reason: 'File belongs to selected node - should update existing node',
          isSelectedNodeFile: true,
        };
      }


      const matchResult = this.filenameNormalizationService.findMatchingNode(
        fileName,
        existingNodeData
      );

      return {
        fileName,
        isDuplicate: matchResult.isMatch,
        reason: matchResult.reason,
        matchType: matchResult.matchType,
        confidence: matchResult.confidence,
        matchedNodeId: matchResult.matchedNodeId,
        isSelectedNodeFile: false,
      };
    });


    const newFileNames = analysisResults
      .filter(result => !result.isDuplicate)
      .map(result => result.fileName);

    const duplicatesFiltered = analysisResults
      .filter(result => result.isDuplicate)
      .map(result => result.fileName);

    const result = {
      newFileNames,
      existingFileNames: existingNodeData.map(node => node.title),
      responseFileNames,
      duplicatesFiltered,
      analysisDetails: {
        totalAnalyzed: responseFileNames.length,
        newFilesFound: newFileNames.length,
        duplicatesFiltered: duplicatesFiltered.length,
        individualAnalysis: analysisResults,
        normalizationServiceUsed: true,
      },
    };

    return result;
  }


  private updateMultipleNodesContent(
    validatedResponse: any[],
    selectedNodes: MultiSelectedNodeData[]
  ): void {
    const currentNodes = this.uiDesignNodes$.value;
    let updatedCount = 0;


    const responseMap = new Map<string, string>();
    validatedResponse.forEach(file => {
      responseMap.set(file.fileName, file.content);
    });


    const duplicateAnalysis = this.identifyTrulyNewFiles(currentNodes, responseMap, selectedNodes);
    const newFileNames = duplicateAnalysis.newFileNames;


    const updatedNodes = currentNodes.map(node => {

      const selectedNode = selectedNodes.find(selected => selected.nodeId === node.id);
      if (selectedNode && responseMap.has(selectedNode.fileName)) {
        const updatedContent = responseMap.get(selectedNode.fileName)!;


        const enhancedContent = this.enhanceHTMLWithCSS(updatedContent);
        const sanitizedContent = this.sanitizer.bypassSecurityTrustHtml(enhancedContent);

        updatedCount++;
        return {
          ...node,
          data: {
            ...node.data,
            htmlContent: sanitizedContent,
            rawContent: enhancedContent,
          },
        };
      }
      return node;
    });


    const newNodes = this.createNewNodesFromEditResponse(
      newFileNames,
      responseMap,
      updatedNodes.length
    );


    const finalNodes = [...updatedNodes, ...newNodes];

    this.uiDesignNodes$.next(finalNodes);
    this.cdr.markForCheck();


    this.synchronizeOverviewPreviewWithCanvasNodes(finalNodes, selectedNodes);
  }


  private createNewNodesFromEditResponse(
    newFileNames: string[],
    responseMap: Map<string, string>,
    existingNodeCount: number
  ): UIDesignNode[] {
    if (newFileNames.length === 0) {
      return [];
    }


    const existingNodes = this.uiDesignNodes$.value.map(node => ({
      id: node.id,
      position: node.position,
      dimensions: { width: node.data.width, height: node.data.height },
    }));


    const selectedNodes = this.uiDesignSelectionService.getSelectedNodes();
    const selectedNodeId = selectedNodes.length > 0 ? selectedNodes[0].nodeId : '';


    const positioningResult = this.uiDesignNodePositioningService.calculateRegenerationPositions(
      existingNodes,
      selectedNodeId,
      newFileNames.length
    );

    const newNodes: UIDesignNode[] = newFileNames.map((fileName, index) => {
      const content = responseMap.get(fileName) || '';


      const enhancedContent = this.enhanceHTMLWithCSS(content);
      const sanitizedContent = this.sanitizer.bypassSecurityTrustHtml(enhancedContent);


      const position =
        positioningResult.positions[index] ||
        this.calculateNewNodePosition(
          existingNodeCount + index,
          existingNodeCount + newFileNames.length
        );

      const newNode: UIDesignNode = {
        id: this.generateNodeId(),
        type: 'ui-design',
        data: {
          title: fileName,
          displayTitle: this.formatFileName(fileName),
          htmlContent: sanitizedContent,
          rawContent: enhancedContent,
          width: 420,
          height: 720,
          isLoading: false,
        },
        position: position,
        selected: false,
        dragging: false,
        visible: true,
      };

      return newNode;
    });

    return newNodes;
  }


  private calculateNewNodePosition(
    nodeIndex: number,
    totalNodes: number
  ): { x: number; y: number } {
    const columnsPerRow = 2;
    const gridSpacing = { x: 480, y: 780 };

    const row = Math.floor(nodeIndex / columnsPerRow);
    const col = nodeIndex % columnsPerRow;


    const totalCols = Math.min(totalNodes, columnsPerRow);
    const gridWidth = totalCols * gridSpacing.x;
    const startX = -gridWidth / 2 + gridSpacing.x / 2;

    const totalRows = Math.ceil(totalNodes / columnsPerRow);
    const gridHeight = totalRows * gridSpacing.y;
    const startY = -gridHeight / 2 + gridSpacing.y / 2;

    return {
      x: startX + col * gridSpacing.x,
      y: startY + row * gridSpacing.y,
    };
  }


  private generateNodeId(): string {
    return `ui-design-node-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
  }


  private formatFileName(fileName: string): string {
    if (!fileName) return '';

    try {

      const transformResult = this.uiDesignFilenameTransformerService.transformFileName(fileName);


      const displayTitle = transformResult.displayTitle || 'Untitled Page';

      return displayTitle;
    } catch (error) {


      const nameWithoutExtension = fileName.replace(/\.[^/.]+$/, '');
      return (
        nameWithoutExtension
          .replace(/[_-]/g, ' ')
          .replace(/([a-z])([A-Z])/g, '$1 $2')
          .split(' ')
          .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
          .join(' ')
          .trim() || 'Untitled Page'
      );
    }
  }


  private synchronizeOverviewPreviewWithCanvasNodes(
    updatedNodes: UIDesignNode[],
    _selectedNodes: MultiSelectedNodeData[]
  ): void {

    try {

      const currentResponseData = this.generateUIDesignService.getUIDesignResponse();

      if (!currentResponseData) {
        this.createNewUIDesignResponseFromNodes(updatedNodes);
        return;
      }


      const updatedPages: MobilePage[] = updatedNodes.map(node => ({
        fileName: node.data.displayTitle || node.data.title,
        content: node.data.rawContent,
      }));


      const updatedResponseData: UIDesignResponseData = {
        ...currentResponseData,
        pages: updatedPages,
      };


      this.generateUIDesignService.setUIDesignResponse(updatedResponseData);


      this.uiDesignPages$.next(updatedPages);
      // Sync web pages
      const webPages = this.convertMobilePagesToWebPages(updatedPages);
      this.webPages$.next(webPages);


      this.cdr.detectChanges();

    } catch (error) {
    }
  }


  private createNewUIDesignResponseFromNodes(nodes: UIDesignNode[]): void {
    const pages: MobilePage[] = nodes.map(node => ({
      fileName: node.data.displayTitle || node.data.title,
      content: node.data.rawContent,
    }));

    // Get project_id from the service if available, otherwise generate a fallback
    const projectId = this.generateUIDesignService.getProjectId() || 'ui-design-project-' + Date.now();
    const commitHash = this.generateUIDesignService.getCommitHash();

    const responseData: UIDesignResponseData = {
      pages: pages,
      jobId: 'ui-design-' + Date.now(),
      projectId: projectId,
      commitHash: commitHash || undefined
    };

    this.generateUIDesignService.setUIDesignResponse(responseData);
    this.uiDesignPages$.next(pages);
    // Sync web pages
    const webPages = this.convertMobilePagesToWebPages(pages);
    this.webPages$.next(webPages);

  }


  private cleanupLegacyEditMessages(): void {


    this.lightMessages = this.lightMessages.filter(msg => {
      const text = msg.text.toLowerCase();
      const isLegacyMessage =
        text.includes('hang tight') ||
        text.includes('working on edit') ||
        text.includes('edit is coming') ||
        text.includes('edit coming up') ||
        text.includes('we are working on') ||
        text.includes('processing your edit') ||
        text.includes('regenerating your') ||
        text.includes('updating your design') ||
        text.includes('modifying your page');

      if (isLegacyMessage) {
        return false;
      }
      return true;
    });

    this.cdr.markForCheck();
  }


  private updateEditChatMessages(
    success: boolean,
    fileNames: string | string[],
    _errorMessage?: string
  ): void {

    this.lightMessages = this.lightMessages.filter(msg => {
      const isTemporaryEditingMessage =
        msg.text.includes('editing') ||
        msg.text.includes('Please wait') ||
        msg.text.includes('Hang tight') ||
        msg.text.includes('working on edit') ||
        msg.text.includes('edit is coming') ||
        msg.text.toLowerCase().includes('processing') ||
        msg.text.toLowerCase().includes('regenerating');

      const isAIMessage =
        msg.from === 'ai' &&
        msg.id &&
        (msg.id.startsWith('ai-generation-') || msg.id.startsWith('ai-regeneration-'));


      return !isTemporaryEditingMessage || isAIMessage;
    });

    if (success) {

      let successMessage: string;
      if (Array.isArray(fileNames)) {
        if (fileNames.length === 1) {
          successMessage = `Successfully updated "${fileNames[0]}"! The changes are now visible in the canvas.`;
        } else {
          successMessage = `Successfully updated ${fileNames.length} pages! The changes are now visible in the canvas.`;
        }
      } else {
        successMessage = `Successfully updated "${fileNames}"! The changes are now visible in the canvas.`;
      }


      const messageId = `ai-edit-success-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;

      this.lightMessages.push({
        id: messageId,
        text: '',
        from: 'ai',
        theme: 'light',
      });


      this.startTypewriterEffectForMessage(successMessage, messageId);
    }

    this.cdr.markForCheck();
  }


  private enhanceHTMLWithCSS(htmlContent: string): string {
    if (!htmlContent || typeof htmlContent !== 'string') {
      return '<html><body><p>No content available</p></body></html>';
    }


    const hasDoctype = htmlContent.includes('<!DOCTYPE');
    const hasHtmlTag = htmlContent.includes('<html');
    const hasHead = htmlContent.includes('<head');

    if (hasDoctype && hasHtmlTag && hasHead) {

      return this.injectCSSFrameworksIfMissing(htmlContent);
    } else {

      return this.wrapInCompleteHTML(htmlContent);
    }
  }


  private injectCSSFrameworksIfMissing(htmlContent: string): string {
    let enhanced = htmlContent;


    if (!enhanced.includes('tailwindcss')) {
      enhanced = enhanced.replace(
        '</head>',
        '  <link href="https://cdn.tailwindcss.com/3.3.0" rel="stylesheet">\n</head>'
      );
    }

    if (!enhanced.includes('bootstrap')) {
      enhanced = enhanced.replace(
        '</head>',
        '  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">\n</head>'
      );
    }

    return enhanced;
  }

  private wrapInCompleteHTML(htmlContent: string): string {
    return `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>UI Design Preview</title>

  <!-- CSS Frameworks -->
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

  <style>
    body {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
      line-height: 1.6 !important;
      margin: 0 !important;
      padding: 20px !important;
      background: #f8fafc !important;
      min-height: 100vh;
    }
    * { box-sizing: border-box !important; }
    .container { max-width: 100% !important; margin: 0 auto !important; }
    img { max-width: 100%; height: auto; }
  </style>
</head>
<body class="iframe-content">
  ${htmlContent}
</body>
</html>`;
  }

  isNodeSelectedForEditing(nodeId: string): boolean {
    return this.uiDesignSelectionService.isNodeSelected(nodeId);
  }

  selectAllNodes(): void {
    const currentNodes = this.uiDesignNodes$.value;
    if (currentNodes.length === 0) {
      return;
    }

    const allNodesData: MultiSelectedNodeData[] = currentNodes.map(node => ({
      nodeId: node.id,
      fileName: node.data.displayTitle || node.data.title,
      htmlContent: node.data.htmlContent as string,
      rawContent: node.data.rawContent,
      selectedImages: [],
      metadata: {
        nodePosition: node.position,
        nodeDimensions: { width: node.data.width, height: node.data.height },
        selectionTimestamp: Date.now(),
      },
    }));

    this.uiDesignSelectionService.selectAllNodes(allNodesData);
  }

  clearAllSelection(): void {
    this.uiDesignSelectionService.clearSelection();
  }

  getSelectedNodesCount(): number {
    return this.uiDesignSelectionService.getSelectedNodesCount();
  }

  getPromptBarPlaceholder(): string {
    if (this.isUIDesignMode$.value) {
      return this.uiDesignSelectionService.getSelectionSummary();
    }
    return 'Ask me';
  }

  getPromptBarEnabledState(): boolean {
    if (this.isUIDesignMode$.value) {

      if (this.isUIDesignRegenerating$.value) {
        return false;
      }
      return this.uiDesignSelectionService.getIsPromptBarEnabled();
    }

    if (this.isRegenerationInProgress$.value) {
      return false;
    }

    return this.isPromptBarEnabled;
  }

  getPromptBarDisabledState(): boolean {
    if (this.isUIDesignMode$.value) {

      if (this.isUIDesignRegenerating$.value) {
        return true;
      }
      return !this.uiDesignSelectionService.getIsPromptBarEnabled();
    }

    if (this.isRegenerationInProgress$.value) {
      return true;
    }

    return !this.isPromptBarEnabled;
  }

  handleUIDesignPromptSubmission(): void {
    if (this.isUIDesignMode$.value) {

      if (this.lightPrompt && this.lightPrompt.trim()) {
        this.onUIDesignEditPrompt(this.lightPrompt.trim());
        this.lightPrompt = '';
      }
    } else {

      this.handleCodeRegenerationSubmission();
    }
  }

  handleRegenerationPayload(payload: { prompt: string; image: string[] }): void {

    this.validateAndResetRegenerationState('New regeneration request');

    if (this.isRegenerationCallInProgress) {
      return;
    }

    this.clearRegenerationTimeout();

    if (this.isUIDesignMode$.value) {

      const selectedNodes = this.uiDesignSelectionService.getSelectedNodes();
      if (selectedNodes.length === 0) {
        this.toastService.error('Please select one or more pages to edit');
        return;
      }

      this.cleanupLegacyEditMessages();

      this.createLoadingNodesForRegeneration(selectedNodes);

      this.uiDesignSelectionService.setEditingInProgress(true);
      this.isUIDesignRegenerating$.next(true);
      this.isUIDesignLoading$.next(true);

      const sessionId = this.generateRegenerationSessionId();

      const aiMessageId = `ai-regeneration-${sessionId}`;

      this.lightMessages.push({
        id: aiMessageId,
        text: '',
        from: 'ai',
        theme: 'light',
        showLoadingIndicator: true,
        loadingPhase: 'intro',
        mainAPIInProgress: true,
      });

      this.activeRegenerationSessions.set(sessionId, {
        aiMessageId: aiMessageId,
        timestamp: Date.now(),
        isActive: true,
        sessionId,
        prompt: payload.prompt,
        selectedNodes: [...selectedNodes],
      });

      this.activeAIMessageIds.add(aiMessageId);
      this.currentActiveMessageId = aiMessageId;

      const editRequest = this.uiDesignSelectionService.buildEditRequest(payload.prompt);
      if (!editRequest) {
        this.handleEditFailure('Failed to build edit request');
        return;
      }

      const mainEditAPICall = this.uiDesignEditService.editUIDesignPage(editRequest);

      this.uiDesignIntroService
        .executeParallelRegeneration(
          payload.prompt,
          selectedNodes,
          mainEditAPICall,
          this.currentActiveMessageId || undefined
        )
        .subscribe({
          next: result => {

            if (result.mainAPISuccess) {
              this.handleEditSuccess(result.mainAPIResult, selectedNodes);
            } else {
              this.handleEditFailure('Main edit API failed');
            }
          },
          error: _error => {
            this.handleEditFailure('Regeneration API calls failed');
          }
        });
    } else {

      this.handleCodeRegeneration(payload);
    }
  }

  private handleCodeRegeneration(payload: { prompt: string; image: string[] }): void {

    const currentCodeFiles = this.getCurrentCodeFiles();
    if (!currentCodeFiles || currentCodeFiles.length === 0) {
      this.handleEditError('No code files found to edit. Please ensure code generation is complete.');
      return;
    }

    this.sequentialRegenerationService.reset();

    this.regenerationIntegrationService.startRegeneration();

    this.executeSequentialCodeRegenerationWithPayload(currentCodeFiles, payload.prompt, payload.image);
  }

  handleUserMessageData(messageData: {
    prompt: string;
    images: Array<{ url: string; name: string; id: string }>;
    timestamp: string;
  }): void {

    const imageDataUri = messageData.images.length > 0 ? messageData.images[0].url : undefined;

    const userMessageId = `user-regeneration-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

    this.lightMessages.push({
      id: userMessageId,
      from: 'user',
      text: messageData.prompt,
      theme: 'light',
      imageDataUri: imageDataUri
    });

    this.cdr.detectChanges();
  }

  private executeSequentialCodeRegenerationWithPayload(
    currentCodeFiles: FileModel[],
    userRequest: string,
    images: string[]
  ): void {

    this.isRegenerationCallInProgress = true;

    this.startRegenerationTimeout();

    this.prepareForRegeneration();

    this.cleanupStatusMonitoring();

    this.lastUserRequest = userRequest;

    this.isCodeGenerationLoading$.next(true);
    this.isRegenerationInProgress$.next(true);
    this.regenerationStartTime = Date.now();

    if (!this.projectId || !this.jobId) {
      const appState = this.appStateService.getState();
      if (appState?.project?.projectId && appState?.project?.jobId) {
        this.projectId = appState.project.projectId;
        this.jobId = appState.project.jobId;
      } else {
        this.handleEditError('Cannot start regeneration: Missing project information. Please try generating code first.');
        return;
      }
    }

    const aiMessageId = `ai-regeneration-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;


    this.activeAIMessageIds.add(aiMessageId);
    this.currentActiveMessageId = aiMessageId;

    this.executeSequentialRegenerationAPIs(currentCodeFiles, userRequest, images, aiMessageId);
  }

  private executeSequentialRegenerationAPIs(
    currentCodeFiles: FileModel[],
    userRequest: string,
    images: string[],
    aiMessageId: string
  ): void {

    const mainRegenerationAPICall = this.codeGenerationService
      .editCode(currentCodeFiles, userRequest, images, this.projectId || undefined, this.jobId || undefined);

    this.sequentialRegenerationService
      .executeSequentialCodeRegeneration(
        userRequest,
        currentCodeFiles,
        mainRegenerationAPICall,
        this.projectId!,
        this.jobId!,
        aiMessageId,
        {
          enableIntroAPI: true,
          enableSSE: true,
          sseEventType: 'code-regen',
          timeoutMs: 600000,
          retryAttempts: 2
        }
      )
      .subscribe({
        next: result => {

          if (result.overallSuccess) {
            this.handleSequentialRegenerationSuccess(result, aiMessageId);
          } else {

            if (result.timeout && result.reason === 'deployment_completed') {
              this.handleSequentialRegenerationSuccess(result, aiMessageId);
            } else {
              this.handleSequentialRegenerationError(new Error('Sequential regeneration failed'), aiMessageId);
            }
          }
        },
        error: error => {

          if (error.message && error.message.includes('timeout')) {
            this.handleSequentialRegenerationError(error, aiMessageId);
          } else {
            this.handleSequentialRegenerationError(error, aiMessageId);
          }
        },
        complete: () => {

          this.resetRegenerationCallFlag('Sequential regeneration completed');
        }
      });

    this.subscribeToSequentialRegenerationProgress(aiMessageId);
  }

  private handleSequentialRegenerationSuccess(result: any, aiMessageId: string): void {

    const aiMessage = this.lightMessages.find(msg => msg.id === aiMessageId);
    if (aiMessage) {

      aiMessage.showLoadingIndicator = true;
      aiMessage.loadingPhase = 'main';
      aiMessage.mainAPIInProgress = false;
    }

    if (result.regenerationResult?.data) {

    }

    this.cdr.detectChanges();
  }

  private startRegenerationTimeout(): void {
    this.clearRegenerationTimeout();

    this.regenerationTimeoutTimer = window.setTimeout(() => {

      this.validateAndResetRegenerationState('Regeneration timeout reached');

      this.sequentialRegenerationService.reset();

      this.regenerationIntegrationService.completeRegeneration();

      this.handleEditError('Regeneration timed out. Please try again.');

    }, this.REGENERATION_TIMEOUT_MS);

  }

  private clearRegenerationTimeout(): void {
    if (this.regenerationTimeoutTimer) {
      window.clearTimeout(this.regenerationTimeoutTimer);
      this.regenerationTimeoutTimer = null;
    }
  }

  private validateAndResetRegenerationState(_reason: string): void {

    if (this.isRegenerationCallInProgress) {
      this.isRegenerationCallInProgress = false;
    }

    if (this.isCodeGenerationLoading$.value) {
      this.isCodeGenerationLoading$.next(false);
    }

    if (this.isRegenerationInProgress$.value) {
      this.isRegenerationInProgress$.next(false);
    }

    this.clearRegenerationTimeout();

    this.codeRegenerationProgressDescription = '';

    this.regenerationStartTime = 0;

  }

  private resetRegenerationCallFlag(_reason: string): void {
    this.isRegenerationCallInProgress = false;
    this.clearRegenerationTimeout();
  }

  private handleSequentialRegenerationError(_error: any, aiMessageId: string): void {

    this.performRegenerationErrorCleanup();

    const aiMessage = this.lightMessages.find(msg => msg.id === aiMessageId);
    if (aiMessage) {
      aiMessage.showLoadingIndicator = false;
      aiMessage.loadingPhase = undefined;
      aiMessage.mainAPIInProgress = false;
      aiMessage.text = 'Sorry, there was an error during regeneration. The system has been reset and you can try again.';
    }

    this.isCodeGenerationLoading$.next(false);
    this.isRegenerationInProgress$.next(false);

    this.resetRegenerationCallFlag('Sequential regeneration error');

    this.handleEditError('Regeneration failed. Please try again.');

    this.cdr.detectChanges();
  }

  private performRegenerationErrorCleanup(): void {

    this.codeRegenerationProgressDescription = '';
    this.stopRegenerationPreviewLoading();

    this.previewError$.next(false);
    this.isIframeReady$.next(false);
    this.isUrlValidated$.next(false);
    this.isUrlAvailable$.next(false);

    this.clearRegenerationTimeout();

    const sessionKey = this.currentCheckpointSession();
    if (sessionKey) {
      this.regenerationCheckpointService.completeCheckpointSession(sessionKey, 'failed');
      this.currentCheckpointSession.set(null);
    }

    this.stepperStateService.setRegenerationActive(false);

    this.regenerationIntegrationService.completeRegeneration();

    this.performSequentialRegenerationCleanup();

  }

  private performSequentialRegenerationCleanup(): void {

    try {

      this.sequentialRegenerationService.reset();
    } catch (error) {
    }
  }

  private handleCodeRegenerationSubmission(): void {
    const userRequest = this.lightPrompt.trim();
    const currentImageDataUri = this.selectedImageDataUri;

    if (!userRequest) {
      return;
    }

    this.lightPrompt = '';

    const currentCodeFiles = this.getCurrentCodeFiles();
    if (!currentCodeFiles || currentCodeFiles.length === 0) {
      this.handleEditError('No code files found to edit. Please ensure code generation is complete.');
      return;
    }

    const images: string[] = currentImageDataUri ? [currentImageDataUri] : [];

    this.executeParallelCodeRegeneration(currentCodeFiles, userRequest, images);

  }

  private executeParallelCodeRegeneration(
    _currentCodeFiles: FileModel[],
    userRequest: string,
    _images: string[],
    skipUserMessage: boolean = false
  ): void {

    if (this.isRegenerationCallInProgress && !skipUserMessage) {
      return;
    }

    if (!skipUserMessage) {
      this.clearRegenerationTimeout();
    }

    if (!skipUserMessage) {
      this.isRegenerationCallInProgress = true;

      this.startRegenerationTimeout();
    }

    this.lastUserRequest = userRequest;

    this.isCodeGenerationLoading$.next(true);
    this.isRegenerationInProgress$.next(true);
    this.regenerationStartTime = Date.now();

    this.subscribeToRegenerationProgress();

    this.startRegenerationPreviewLoading();

    if (!this.projectId || !this.jobId) {
      const appState = this.appStateService.getState();
      if (appState?.project?.projectId && appState?.project?.jobId) {
        this.projectId = appState.project.projectId;
        this.jobId = appState.project.jobId;
      } else {
        this.handleEditError('Cannot start regeneration: Missing project information. Please try generating code first.');
        return;
      }
    }

    const aiMessageId = `ai-regeneration-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

    if (!skipUserMessage) {
      this.lightMessages.push({
        text: userRequest,
        from: 'user',
        theme: 'light',
      });
    } else {
    }

    this.lightMessages.push({
      id: aiMessageId,
      text: '',
      from: 'ai',
      theme: 'light',
      showIntroMessage: true,
      showLoadingIndicator: true,
      loadingPhase: 'intro',
      mainAPIInProgress: true,
    });

    this.activeAIMessageIds.add(aiMessageId);
    this.currentActiveMessageId = aiMessageId;

    if (this.projectId && this.jobId) {
      this.startStatusMonitoring(this.projectId, this.jobId, 'code-regeneration-parallel');
    }
    this.updatePollingStatus(true);

    this.subscribeToRegenerationSSEUpdates(aiMessageId);
  }


  private handleRegenerationSSEFailure(aiMessageId: string, progressUpdate: any): void {

    this.isCodeGenerationLoading$.next(false);
    this.isRegenerationInProgress$.next(false);
    this.resetRegenerationCallFlag('Regeneration error handling');
    this.codeRegenerationProgressDescription = '';
    this.stopRegenerationPreviewLoading();

    const aiMessage = this.lightMessages.find(msg => msg.id === aiMessageId);
    if (aiMessage) {
      aiMessage.showLoadingIndicator = false;
      aiMessage.loadingPhase = 'completed';
      aiMessage.mainAPIInProgress = false;

      aiMessage.text = progressUpdate.dynamicMessage || progressUpdate.log || 'Regeneration failed. Please try again.';
    }

    const errorMessage = progressUpdate.dynamicMessage || progressUpdate.log || 'Regeneration failed. Please try again.';
    this.createRegenerationErrorResult(errorMessage);

    this.codeGenerationIntroService.completeRegenerationAfterSSE();

    this.activeAIMessageIds.delete(aiMessageId);
    this.currentActiveMessageId = null;

    this.toastService.error(errorMessage);

    this.currentProgress = 'DEPLOY';
    this.currentStatus = 'FAILED';
    this.updatePromptBarEnabledState();

    if (this.projectId && this.jobId) {
      this.startStatusMonitoring(this.projectId, this.jobId, 'post-regeneration-failure');
    }

    this.cdr.detectChanges();

  }


  private subscribeToRegenerationSSEUpdates(aiMessageId: string): void {

    if (!this.subscription) {
      this.subscription = new Subscription();
    }

    this.subscription.add(
      this.sseDataProcessor.codeFiles$.pipe(
        takeUntil(this.destroy$),
        filter(() => this.isRegenerationInProgress$.value),
        filter(() => this.isCodeGenerationComplete)
      ).subscribe(codeFiles => {

        if (codeFiles && codeFiles.length > 0) {

          const fileModels = codeFiles.map(file => ({
            name: file.path || 'Unknown file',
            type: 'file' as const,
            content: file.code || '',
            fileName: file.path || 'Unknown file'
          }));

          this.files$.next(fileModels);
          this.handleRegenerationCodeFiles(fileModels, aiMessageId);


          this.handleAutomaticCodeTabSwitch();
        }
      })
    );

  }

  private subscribeToSequentialRegenerationProgress(aiMessageId: string): void {

    if (!this.subscription) {
      this.subscription = new Subscription();
    }

    this.subscription.add(
      this.sequentialRegenerationService.progressUpdates$.pipe(
        takeUntil(this.destroy$),
        filter(() => this.isRegenerationInProgress$.value)
      ).subscribe(progressUpdate => {

        if (progressUpdate.introMessage && progressUpdate.event === 'intro-message-typewriter') {

          const aiMessage = this.lightMessages.find(msg => msg.id === aiMessageId);
          if (aiMessage) {

            aiMessage.isTyping = true;
            aiMessage.showLoadingIndicator = false;
            this.startTypewriterEffectForMessage(progressUpdate.introMessage, aiMessageId);
          } else {
          }
        }

        if (progressUpdate.event === 'code-regen' &&
            progressUpdate.progress === 'DEPLOY' &&
            progressUpdate.status === 'COMPLETED' &&
            progressUpdate.deploymentCompleted) {

          this.completeRegenerationProcess(aiMessageId);

          this.handleRegenerationUIStateUpdate({
            shouldRefresh: true,
            shouldSwitchTab: 'preview',
            phase: 'DEPLOY',
            status: 'COMPLETED'
          });

          this.codeGenerationIntroService.completeRegenerationAfterSSE();

          return;
        }

        if (progressUpdate.event === 'regeneration-failed' && progressUpdate.status === 'FAILED') {

          this.handleRegenerationSSEFailure(aiMessageId, progressUpdate);

          return;
        }

      })
    );

  }


  private cleanUrl(url: string): string {
    if (!url || typeof url !== 'string') {
      return '';
    }

    let cleanedUrl = url;

    cleanedUrl = cleanedUrl.replace(/\x1b\[[0-9;]*m/g, '');

    cleanedUrl = cleanedUrl.replace(/\[\d+m/g, '');

    cleanedUrl = cleanedUrl.replace(/[\x00-\x1F\x7F]/g, '');

    const netIndex = cleanedUrl.indexOf('.net');
    if (netIndex !== -1) {
      cleanedUrl = cleanedUrl.substring(0, netIndex + 4);
    }

    cleanedUrl = cleanedUrl.replace(/\?+$/, '');

    cleanedUrl = cleanedUrl.trim();

    if (cleanedUrl !== url) {
    }

    return cleanedUrl;
  }


  private completeRegenerationProcess(aiMessageId: string): void {

    const sessionKey = this.currentCheckpointSession();
    if (sessionKey) {
      this.regenerationCheckpointService.completeCheckpointSession(sessionKey, 'completed');
      this.currentCheckpointSession.set(null);

    }

    this.isCodeGenerationLoading$.next(false);
    this.isRegenerationInProgress$.next(false);
    this.codeRegenerationProgressDescription = '';

    this.stopRegenerationPreviewLoading();

    const targetMessage = this.lightMessages.find(msg => msg.id === aiMessageId);
    if (targetMessage) {
      targetMessage.showLoadingIndicator = false;
      targetMessage.loadingPhase = 'completed';
      targetMessage.mainAPIInProgress = false;
    }

    const currentDeployedUrl = this.deployedUrl$.value;
    if (currentDeployedUrl) {

      this.isPreviewTabEnabled = true;
      this.previewError$.next(false);

    } else {

    }

    if (this.currentView$.value !== 'preview') {
      this.currentView$.next('preview');
    }

    this.resetRegenerationCallFlag('Regeneration completion');

    if (this.projectId && this.jobId) {
      this.startStatusMonitoring(this.projectId, this.jobId, 'post-regeneration');
      this.updatePollingStatus(true);
    }

    this.cdr.detectChanges();

  }


  private handleRegenerationCodeFiles(codeFiles: any[], _aiMessageId: string): void {
    if (!codeFiles || codeFiles.length === 0) {
      return;
    }

    const currentProgress = this.newPollingResponseProcessor.getCurrentProgress();
    const currentStatus = this.newPollingResponseProcessor.getCurrentStatus();

    if (currentProgress === 'BUILD') {

      const fileModels = codeFiles.map(file => {
        const content = file.code || file.content || file.data || '';
        const fileName = file.path || file.name || file.fileName || 'unknown.txt';

        if (!content) {
        }

        return {
          name: fileName,
          fileName: fileName,
          language: this.getLanguageFromPath(fileName),
          content: content,
          path: fileName,
          type: 'file' as const,
        };
      });

      this.generatedCode = fileModels;
      this.isCodeGenerationComplete = true;

      if (!this.isCodeTabEnabled) {
        this.isCodeTabEnabled = true;
      }

      if (currentProgress && currentProgress.toString().toUpperCase() === "DEPLOY" && currentStatus === 'COMPLETED') {

        this.ngZone.run(() => {
          this.toggleCodeView();
          this.cdr.markForCheck();
        });
      }


      this.cdr.detectChanges();

    } else {
    }
  }


  private createRegenerationAccordion(accordionData: any): void {

    const projectName = this.projectName || accordionData.projectName || 'Untitled Project';

    const generationResult: GenerationResult = {
      type: 'success',
      version: accordionData.version,
      projectName: projectName,
      files: accordionData.files,
      timestamp: accordionData.timestamp,
      isLatest: true
    };

    if (this.chatWindow) {
      this.chatWindow.addGenerationResult(generationResult);
    } else {
    }

    this.cdr.detectChanges();
  }

  private handleRegenerationUIStateUpdate(stateUpdate: any): void {

    if (stateUpdate.description && stateUpdate.isProgressDescription) {
      this.codeRegenerationProgressDescription = stateUpdate.description;
    }

    this.updatePreviewTabStateForRegeneration(stateUpdate);

    if (stateUpdate.shouldSwitchTab) {
      if (stateUpdate.shouldSwitchTab === 'code') {
        this.switchToCodeTab();
      } else if (stateUpdate.shouldSwitchTab === 'preview') {
        this.switchToPreviewTab();
      }
    }

    if (stateUpdate.shouldRefresh) {
      this.refreshIframe();
    }

    if (stateUpdate.phase === 'CODE_GENERATION' && stateUpdate.status === 'IN_PROGRESS') {
      this.isCodeGenerationLoading$.next(true);
    } else if (stateUpdate.phase === 'CODE_GENERATION' && stateUpdate.status === 'COMPLETED') {
      this.isCodeGenerationLoading$.next(false);
    }

    this.cdr.detectChanges();
  }

  private switchToCodeTab(): void {
    this.currentView$.next('editor');
    this.isCodeActive$.next(true);
    this.isPreviewActive$.next(false);
    this.isArtifactsActive$.next(false);
    this.cdr.detectChanges();
  }

  private switchToPreviewTab(): void {
    this.currentView$.next('preview');
    this.isCodeActive$.next(false);
    this.isPreviewActive$.next(true);
    this.isArtifactsActive$.next(false);
    this.cdr.detectChanges();
  }

  private refreshIframe(): void {

    const currentUrl = this.deployedUrl$.value;
    if (currentUrl) {
      const separator = currentUrl.includes('?') ? '&' : '?';
      const refreshUrl = `${currentUrl}${separator}_refresh=${Date.now()}`;

      this.urlSafe = this.sanitizer.bypassSecurityTrustResourceUrl(refreshUrl);
      this.cdr.detectChanges();

    } else {
    }
  }

  onCanvasWheel(event: WheelEvent): void {

    const rect = this.uiDesignCanvas?.nativeElement.getBoundingClientRect();
    if (!rect) return;

    const isOverCanvas = event.clientX >= rect.left &&
                        event.clientX <= rect.right &&
                        event.clientY >= rect.top &&
                        event.clientY <= rect.bottom;

    if (!isOverCanvas) return;

    event.preventDefault();

    const centerPoint = {
      x: event.clientX - rect.left,
      y: event.clientY - rect.top,
    };

    this.ngZone.run(() => {
      if (event.deltaY < 0) {
        this.uiDesignCanvasService.zoomIn(centerPoint);
      } else {
        this.uiDesignCanvasService.zoomOut(centerPoint);
      }
    });
  }

  zoomInCanvas(): void {
    this.uiDesignCanvasService.zoomIn();
  }

  zoomOutCanvas(): void {
    this.uiDesignCanvasService.zoomOut();
  }

  resetCanvasView(): void {

    this.updateCanvasContainerSize();

    this.uiDesignCanvasService.resetViewport();

    setTimeout(() => {
      this.centerCanvasOnNodes();
    }, 50);

  }

  fitCanvasToView(): void {
    this.uiDesignViewportService.fitContentToView();
  }

  centerCanvasOnNodes(): void {
    this.uiDesignViewportService.centerViewOnNodes();
  }

  centerCanvasOnNodesWithViewport(viewport: { x: number; y: number; zoom: number }): void {

    this.uiDesignCanvasService.updateViewport({
      x: viewport.x,
      y: viewport.y,
      zoom: viewport.zoom,
    });

  }

  updateCanvasContainerSize(): void {
    if (this.uiDesignCanvas?.nativeElement) {
      const canvasContainer = this.uiDesignCanvas.nativeElement;
      const canvasRect = canvasContainer.getBoundingClientRect();

      const rightPanelContent = canvasContainer.closest('[awe-rightpanel-content]');
      const rightPanelRect = rightPanelContent?.getBoundingClientRect();

      const containerRect = rightPanelRect || canvasRect;


      const rightPanelPadding = 16;

      const availableWidth = containerRect.width - rightPanelPadding * 2;


      const finalWidth = Math.max(availableWidth, 300);

      const finalHeight = 500;

      this.uiDesignViewportService.updateContainerSize(finalWidth, finalHeight);

    }
  }

  private setupAutoCanvasCentering(): void {

    this.updateCanvasContainerSize();

    setTimeout(() => {
      this.centerCanvasOnNodes();
    }, 100);

    if (this.uiDesignCanvas?.nativeElement && typeof ResizeObserver !== 'undefined') {
      const resizeObserver = new ResizeObserver(entries => {
        for (const _entry of entries) {

          clearTimeout(this.resizeTimeout);
          this.resizeTimeout = setTimeout(() => {
            this.updateCanvasContainerSize();
            this.centerCanvasOnNodes();
          }, 150);
        }
      });

      resizeObserver.observe(this.uiDesignCanvas.nativeElement);

      this.canvasResizeObserver = resizeObserver;
    }

  }

  getCanvasTransformStyle(): string {
    return this.uiDesignCanvasService.getTransformStyle();
  }

  getCanvasZoomPercentage(): number {
    return this.uiDesignCanvasService.getZoomPercentage();
  }

  isCanvasAtMinZoom(): boolean {
    return this.uiDesignCanvasService.isAtMinZoom();
  }

  isCanvasAtMaxZoom(): boolean {
    return this.uiDesignCanvasService.isAtMaxZoom();
  }

  trackByUIDesignNode(_index: number, node: UIDesignNode): string {
    return node.id;
  }

  getLoadingText(title: string): string {

    if (title.includes('Editing') && title.includes('...')) {
      return title;
    }

    return 'Generating wireframe...';
  }

  private subscribeToNewPollingProcessor(): void {
    if (!this.subscription) {
      this.subscription = new Subscription();
    }

    this.subscription.add(
      this.newPollingResponseProcessor.currentProgress$.subscribe(progress => {

        if (this.shouldBlockUIDesignData('progress')) {
          return;
        }

        this.currentProgress = progress;
        this.handleNewProgressChange(progress);
        this.updatePromptBarEnabledState();

        this.updatePreviewTabStateForProgress(progress);

        this.updateCodeTabStateForProgress(progress);

      })
    );

    this.subscription.add(
      this.newPollingResponseProcessor.currentStatus$.subscribe(status => {

        if (this.shouldBlockUIDesignData('status')) {
          return;
        }

        this.currentStatus = status;
        this.handleNewStatusChange(status);
        this.updatePromptBarEnabledState();

        this.updatePreviewTabStateForProgress(this.currentProgress);

        this.updateCodeTabStateForProgress(this.currentProgress);

      })
    );

    this.subscription.add(
      this.newPollingResponseProcessor.progressDescription$.subscribe(description => {

        if (this.isUIDesignMode$.value) {
          return;
        }

        this.newProgressDescription = description;
        this.updateStepperDescription(description);
      })
    );

    this.subscription.add(
      this.newPollingResponseProcessor.logContent$.subscribe(content => {

        if (this.shouldBlockUIDesignData('logs')) {
          return;
        }

        this.newLogContent = content;
        this.updateLogWindow(content);

      })
    );

    this.subscription.add(
      this.newPollingResponseProcessor.artifactData$.subscribe(data => {

        if (this.shouldBlockUIDesignData('artifacts')) {
          return;
        }

        if (data === null) {
        }

        this.newArtifactData = data;
        this.updateArtifactsWindow(data);
      })
    );

    this.subscription.add(
      this.newPollingResponseProcessor.fileList$.subscribe(files => {

        if (this.isUIDesignMode$.value) {
          return;
        }

        this.newFileList = files;
      })
    );

    this.subscription.add(
      this.newPollingResponseProcessor.codeFiles$.subscribe(files => {

        if (this.isUIDesignMode$.value) {
          return;
        }

        this.newCodeFiles = files;
        this.updateCodeViewer(files);
      })
    );

    this.subscription.add(
      this.newPollingResponseProcessor.previewUrl$.subscribe(url => {

        if (this.isUIDesignMode$.value) {
          return;
        }

        this.updatePreviewWindow(url);

        if (url && url.trim() !== '' && url !== 'ERROR_DEPLOYMENT_FAILED') {

          this.hasNewPollingResponseUrl = true;

          this.isNewPreviewEnabled = true;

          this.newPreviewUrl = url;

          this.updatePreviewWindow(url);


          this.handleAutomaticPreviewTabSwitch();
        } else if (url === 'ERROR_DEPLOYMENT_FAILED') {
          this.showDeploymentErrorInPreview();
        } else {
        }
      })
    );

    this.subscription.add(
      this.newPollingResponseProcessor.projectInfo$.subscribe(projectInfo => {

        if (this.isUIDesignMode$.value) {
          return;
        }

        this.currentProjectInfo = projectInfo;
        this.updateProjectInfo(projectInfo);
      })
    );

    this.subscription.add(
      this.codeSharingService.generatedCode$.subscribe(generatedCode => {
        if (generatedCode && this.isCodeGenerationComplete) {
          this.handleCodeUpdate(generatedCode);
        }
      })
    );
  }

  private subscribeToSSEDataProcessor(): void {

    if (!this.sseDataProcessorSubscription) {
      this.sseDataProcessorSubscription = new Subscription();
    }

    this.sseDataProcessorSubscription.add(
      this.sseDataProcessor.codeFiles$.pipe(
        takeUntil(this.destroy$),
        filter(codeFiles => {

          const shouldProcess = codeFiles && codeFiles.length > 0 &&
                               !this.isUIDesignMode$.value &&
                               !this.isRegenerationInProgress$.value;
          if (!shouldProcess && codeFiles && codeFiles.length > 0) {
            if (this.isUIDesignMode$.value) {
            } else if (this.isRegenerationInProgress$.value) {
            }
          }
          return shouldProcess;
        })
      ).subscribe(codeFiles => {

        const fileModels = codeFiles.map(file => ({
          name: file.path,
          type: 'file' as const,
          content: file.code,
        }));


        this.isViewingSeedProjectTemplate = false;

        this.generatedCode = fileModels;
        this.isCodeGenerationComplete = true;
        this.updateCodeViewer(codeFiles);


        this.handleAutomaticCodeTabSwitch();

        this.cdr.markForCheck();
      })
    );

    this.sseDataProcessorSubscription.add(
      this.sseDataProcessor.artifactData$.pipe(
        takeUntil(this.destroy$),
        filter(artifactData => {

          const shouldProcess = !this.isUIDesignMode$.value && !this.isRegenerationInProgress$.value;
          if (!shouldProcess && artifactData) {
            if (this.isUIDesignMode$.value) {
            } else if (this.isRegenerationInProgress$.value) {
            }
          }
          return shouldProcess;
        })
      ).subscribe(artifactData => {
        if (artifactData) {
          this.newArtifactData = artifactData;
          this.updateArtifactsWindow(artifactData);
          this.cdr.markForCheck();
        }
      })
    );

    this.sseDataProcessorSubscription.add(
      this.sseDataProcessor.previewUrl$.pipe(
        takeUntil(this.destroy$),
        filter(() => !this.isUIDesignMode$.value && !this.isRegenerationInProgress$.value)
      ).subscribe(url => {
        if (url && url.trim() !== '' && url !== 'ERROR_DEPLOYMENT_FAILED') {
          this.updatePreviewWindow(url);


          this.handleAutomaticPreviewTabSwitch();

          this.cdr.markForCheck();
        }
      })
    );

  }

  private handleNewProgressChange(progress: ProgressState | null): void {
    if (!progress) return;

    this.checkStoredLayoutForDisplay(progress);

    switch (progress) {
      case 'OVERVIEW':
        this.handleProjectOverviewState();
        break;
      case 'SEED_PROJECT_INITIALIZED':
        this.handleSeedProjectInitializedState();
        break;
      case 'FILE_QUEUE':
        this.handleFileQueueState();
        break;
      case 'Design_System Analyzed':
        this.handleDesignSystemState();
        break;
      case 'COMPONENTS_CREATED':
        this.handleComponentsCreatedState();
        break;
      case 'Layout Analyzed':
      case 'LAYOUT_ANALYZED':
        this.handleLayoutAnalyzedState();
        break;
      case 'PAGES_GENERATED':
        this.handlePagesGeneratedState();
        break;
      case 'BUILD':
        this.handleBuildState();
        break;
      case 'DEPLOY':
      case 'deployed':
        this.handleDeployedState();
        break;
    }

    this.previousProgressState = this.currentProgressState;
    this.currentProgressState = progress;
    this.cdr.detectChanges();
  }

  private checkStoredLayoutForDisplay(currentProgress: ProgressState): void {

    if (
      this.detectedLayoutFromPrevMetadata &&
      this.previousProgressState === 'LAYOUT_ANALYZED' &&
      currentProgress !== 'LAYOUT_ANALYZED' &&
      currentProgress !== 'Layout Analyzed' &&
      !this.shouldShowLayoutArtifact
    ) {

      const existingLayoutArtifact = this.artifactsData.find(item => item.name === 'Layout Analyzed');
      if (existingLayoutArtifact) {
        return;
      }

      const layoutKey = this.detectedLayoutFromPrevMetadata;
      const layoutImageUrl = `assets/images/layout-${layoutKey}.png`;
      const layoutName = this.layoutMapping[layoutKey];

      const artifactItem = {
        name: 'Layout Analyzed',
        type: 'image',
        content: layoutImageUrl,
      };

      this.artifactsData.push(artifactItem);

      this.ensureLogsFileAtBottom();

      this.layoutAnalyzedData = [
        {
          key: layoutKey,
          name: layoutName,
          imageUrl: layoutImageUrl,
        },
      ];

      this.loadedArtifacts.add('Layout Analyzed');
      this.hasLayoutAnalyzed = true;
      this.shouldShowLayoutArtifact = true;

      this.enableArtifactsTabIfNeeded();

    }
  }

  private handleNewStatusChange(status: StatusType | null): void {
    if (!status) return;

    switch (status) {
      case 'IN_PROGRESS':
        this.showProgressIndicator();
        break;
      case 'COMPLETED':
        this.showCompletionState();
        break;
      case 'FAILED':
        this.showErrorState();
        break;
    }

    this.pollingStatus = status;
    this.cdr.detectChanges();
  }

  private updateStepperDescription(description: string): void {
    this.lastProgressDescription = description;
    this.cdr.detectChanges();
  }

  private updatePromptBarEnabledState(): void {
    const currentProgress = this.currentProgress;
    const currentStatus = this.currentStatus;

    const shouldEnable =
      currentProgress === 'DEPLOY' && (currentStatus === 'COMPLETED' || currentStatus === 'FAILED');

    if (this.isPromptBarEnabled !== shouldEnable) {
      this.isPromptBarEnabled = shouldEnable;

      this.cdr.detectChanges();
    }
  }

  private updateLogWindow(content: string): void {
    if (content) {

      const timestamp = new Date().toLocaleTimeString('en-US', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
      });

      const logMessage = `${timestamp} - INFO - ${content}`;
      this.logMessages = [...this.logMessages, logMessage];
      this.hasLogs = true;
      this.isLogsTabEnabled = true;

      this.autoEnableLogsTabIfNeeded();

      this.enableArtifactsTabIfNeeded();

      this.cdr.detectChanges();
    }
  }

  private autoEnableLogsTabIfNeeded(): void {

    if (!this.logsTabAutoEnabled && !this.userSelectedTab && this.hasLogs) {

      this.logsTabAutoEnabled = true;

      this.autoSwitchToLogsView();
    }
  }

  private autoSwitchToLogsView(): void {

    if (!this.hasLogs) {
      return;
    }

    this.isCodeActive$.next(false);
    this.isPreviewActive$.next(false);

    this.isArtifactsActive$.next(false);

    this.currentView$.next('logs');

    this.isLoading$.next(false);

    if (this.formattedLogMessages.length > 0) {

      setTimeout(() => {
        this.scrollLogsToBottom();
      }, 0);
    }

  }

  private updateProjectOverviewArtifact(content: string): void {

    const projectOverviewIndex = this.artifactsData.findIndex(
      item => item.name === 'Project Overview'
    );

    if (projectOverviewIndex !== -1) {

      this.artifactsData[projectOverviewIndex].content = content;
    } else {

    }

    this.isArtifactsTabEnabled = true;
    this.cdr.detectChanges();
  }

  private updateDesignSystemArtifact(artifactData: any): void {

    const designSystemIndex = this.artifactsData.findIndex(item => item.name === 'Design System');

    if (designSystemIndex !== -1) {

      this.artifactsData[designSystemIndex].content = artifactData.tokens || artifactData.content;
    } else {

      this.artifactsData.push({
        name: 'Design System',
        type: 'component',
        content: artifactData.tokens || artifactData.content,
      });

      this.ensureLogsFileAtBottom();
    }

    this.isArtifactsTabEnabled = true;
    this.cdr.detectChanges();
  }

  private updateArtifactsWindow(artifactData: any): void {
    if (!artifactData) {

      if (this.artifactsData.length === 0 && this.persistentArtifacts.size > 0) {
        this.restorePersistentArtifacts();
      }
      return;
    }

    this.processArtifactData(artifactData);

    switch (artifactData.type) {
      case 'readme':
        this.displayReadmeContent(artifactData.content);
        break;
      case 'layout':
        this.displayLayoutContent(artifactData.layoutCode);
        break;
      case 'design-tokens':
        this.displayDesignTokens(artifactData.tokens);
        break;
    }

    this.isArtifactsTabEnabled = true;
    this.cdr.detectChanges();
  }

  private updateCodeViewer(files: FileData[]): void {
    if (files && files.length > 0) {

      const fileModels: FileModel[] = files.map(file => ({
        name: file.path,
        type: 'file',
        content: file.code,
        fileName: file.path,
      }));

      this.files$.next(fileModels);
      this.isCodeTabEnabled = true;
      this.isCodeGenerationComplete = true;
      this.cdr.detectChanges();
    }
  }

  private handleCodeUpdate(generatedCode: any): void {
    try {

      if (Array.isArray(generatedCode)) {

        const fileModels: FileModel[] = generatedCode.map(file => ({
          name: file.fileName || file.name || 'unknown.txt',
          type: 'file',
          content: file.content || '',
          fileName: file.fileName || file.name || 'unknown.txt',
        }));

        this.files$.next(fileModels);
      } else if (typeof generatedCode === 'object' && generatedCode !== null) {

        const fileModels: FileModel[] = Object.entries(generatedCode).map(([path, content]) => ({
          name: path,
          type: 'file',
          content: typeof content === 'string' ? content : JSON.stringify(content, null, 2),
          fileName: path,
        }));

        this.files$.next(fileModels);
      }

      this.cdr.detectChanges();

      if (this.codeViewer) {
        setTimeout(() => {
          this.codeViewer.refreshOpenFiles();
        }, 100);
      }
    } catch (error) {
    }
  }

  private updatePreviewWindow(url: string): void {

    if (url && url.trim() !== '' && this.isNewPreviewEnabled) {

      this.hasNewPollingResponseUrl = true;

      this.processUrlForIframe(url.trim());
    } else if (url === 'ERROR_DEPLOYMENT_FAILED') {
      this.showDeploymentErrorInPreview();
    } else {

      this.isIframeReady$.next(false);
      this.isUrlValidated$.next(false);
      this.isUrlAvailable$.next(false);

      this.isPreviewTabEnabled = false;
      this.previewIcon$.next('bi-code-slash');
      this.urlSafe = undefined;
    }

    this.cdr.detectChanges();
  }

  private isValidPreviewUrl(url: string): boolean {
    try {

      if (!url || typeof url !== 'string' || url.trim() === '') {
        this.urlValidationError$.next('URL is empty or invalid');
        return false;
      }

      const urlObject = new URL(url.trim());

      const isValidProtocol = urlObject.protocol === 'http:' || urlObject.protocol === 'https:';

      if (!isValidProtocol) {
        this.urlValidationError$.next(
          `Invalid protocol: ${urlObject.protocol}. Only HTTP and HTTPS are allowed.`
        );
        return false;
      }

      if (!urlObject.hostname || urlObject.hostname.trim() === '') {
        this.urlValidationError$.next('URL hostname is missing or empty');
        return false;
      }

      this.urlValidationError$.next('');
      return true;
    } catch (error) {
      this.urlValidationError$.next(error instanceof Error ? error.message : 'Invalid URL format');
      return false;
    }
  }

  private processUrlForIframe(url: string): void {

    this.isIframeReady$.next(false);
    this.isUrlValidated$.next(false);
    this.isUrlAvailable$.next(false);

    if (!this.isValidPreviewUrl(url)) {
      return;
    }
    this.isUrlValidated$.next(true);

    this.checkUrlAvailability(url.trim())
      .then(isAvailable => {

        if (isAvailable) {
          this.isUrlAvailable$.next(true);

          this.prepareUrlForIframe(url.trim());
        } else {
          this.urlValidationError$.next('URL is not accessible');
        }
      })
      .catch(_error => {
        this.urlValidationError$.next('Error checking URL availability');
      });
  }

  private async checkUrlAvailability(url: string): Promise<boolean> {
    try {

      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000);

      try {
        await fetch(url, {
          method: 'HEAD',
          signal: controller.signal,
          mode: 'no-cors',
        });

        clearTimeout(timeoutId);

        return true;
      } catch (fetchError) {
        clearTimeout(timeoutId);

        if (fetchError instanceof Error && fetchError.name === 'TypeError') {
          return true;
        }

        throw fetchError;
      }
    } catch (error) {

      return true;
    }
  }

  private prepareUrlForIframe(url: string): void {

    try {

      const cleanedUrl = this.cleanUrl(url);

      this.urlSafe = this.sanitizer.bypassSecurityTrustResourceUrl(cleanedUrl);

      this.deployedUrl$.next(url);

      this.isIframeReady$.next(true);

      this.isPreviewTabEnabled = true;
      this.isPreviewLoading$.next(false);
      this.previewIcon$.next('bi-eye');
      this.previewError$.next(false);

      this.cdr.detectChanges();
    } catch (error) {
      this.urlValidationError$.next('Error preparing URL for iframe');
    }
  }

  private updateProjectInfo(projectInfo: ProjectInfo | null): void {
    if (!projectInfo) {
      return;
    }

    if (projectInfo.name && projectInfo.name.trim() !== '') {
      this.projectName = projectInfo.name;
      this.isProjectNameLoading = false;
    }

    const projectOverviewIndex = this.artifactsData.findIndex(
      item => item.name === 'Project Overview'
    );
    if (projectOverviewIndex !== -1) {

      const currentContent = this.artifactsData[projectOverviewIndex].content;
      const updatedContent = currentContent.replace(/# .*/, `# ${projectInfo.name}`);
      this.artifactsData[projectOverviewIndex].content = updatedContent;
    }

    this.cdr.detectChanges();
  }

  private handleProjectOverviewState(): void {

    this.isArtifactsTabEnabled = this.newPollingResponseProcessor.isArtifactsTabEnabled();
  }

  private handleSeedProjectInitializedState(): void {

  }

  private handleFileQueueState(): void {

  }

  private handleComponentsCreatedState(): void {

  }

  private handlePagesGeneratedState(): void {

  }

  private handleLayoutAnalyzedState(): void {
    this.hasLayoutAnalyzed = true;
    this.isArtifactsTabEnabled = this.newPollingResponseProcessor.isArtifactsTabEnabled();
  }

  private handleDesignSystemState(): void {
    this.hasDesignSystem = true;
    this.isArtifactsTabEnabled = this.newPollingResponseProcessor.isArtifactsTabEnabled();
  }

  private handleBuildState(): void {

    if (!this.subscription) {
      this.subscription = new Subscription();
    }

    this.subscription.add(
      this.newPollingResponseProcessor.currentStatus$.subscribe(status => {
        const currentProgress = this.newPollingResponseProcessor.getCurrentProgress();
        if (currentProgress === 'BUILD' && (status === 'IN_PROGRESS' || status === 'COMPLETED')) {

          this.isCodeTabEnabled = this.newPollingResponseProcessor.isCodeTabEnabled();
        }
      })
    );

    this.subscription.add(
      combineLatest([
        this.newPollingResponseProcessor.codeFiles$,
        this.newPollingResponseProcessor.currentGenerationType$
      ]).subscribe(([files, _generationType]) => {
        if (files && files.length > 0) {

          const fileModels = files.map(file => {

            const fullPath = file.path;

            const language = this.getLanguageFromPath(file.path);

            return {
              name: fullPath,
              language: language,
              content: file.code,
              path: fullPath,
              type: 'file' as const,
            };
          });

          this.files$.next(fileModels);

          if (fileModels.length > 0) {
            this.setCodeTabEnabled('View generated code');

            this.isViewingSeedProjectTemplate = false;
          }

          this.isCodeTabEnabled = true;


          this.handleAutomaticCodeTabSwitch();
        }
      })
    );
  }

  private handleDeployedState(): void {

    if (!this.subscription) {
      this.subscription = new Subscription();
    }

    this.subscription.add(
      this.newPollingResponseProcessor.previewUrl$.subscribe(url => {

        if (url && url.trim() !== '') {

          if (url === 'ERROR_DEPLOYMENT_FAILED') {
            this.showDeploymentErrorInPreview();

            this.setPreviewTabError('Deployment failed', 'View error details');
          } else {

            this.newPreviewUrl = '';
            this.urlSafe = undefined;

            this.newPreviewUrl = url.trim();
            this.deployedUrl$.next(url.trim());

            this.urlSafe = this.sanitizer.bypassSecurityTrustResourceUrl(url.trim());

            this.setPreviewTabEnabled('View deployed application preview');

            this.isPreviewTabEnabled = true;
            this.previewError$.next(false);
            this.isLoading$.next(false);
            this.isPreviewLoading$.next(false);

            this.togglePreviewView();

          }
        } else if (url === '') {

          this.newPreviewUrl = '';
          this.urlSafe = undefined;
          this.deployedUrl$.next('');
        }
      })
    );

    this.subscription.add(
      this.newPollingResponseProcessor.currentStatus$.subscribe(status => {
        const currentProgress = this.newPollingResponseProcessor.getCurrentProgress();
        if (currentProgress === 'DEPLOY') {
          if (status === 'COMPLETED') {

            if (this.isRegenerationInProgress$.value) {

            }

          } else if (status === 'IN_PROGRESS') {

            this.clearPreviousDeployment();
          }
        }
      })
    );
  }

  private clearPreviousDeployment(): void {
    this.newPreviewUrl = '';
    this.urlSafe = undefined;
    this.deployedUrl$.next('');
    this.previewError$.next(false);
  }

  private showDeploymentErrorInPreview(): void {
    this.previewError$.next(true);
    this.isPreviewTabEnabled = true;


    this.handleAutomaticFailedTabSwitch();
  }

  get isNewArtifactsTabEnabled(): boolean {
    return this.newPollingResponseProcessor.isArtifactsTabEnabled();
  }

  get isNewCodeTabEnabled(): boolean {

    return true;
  }

  get isNewLogsTabEnabled(): boolean {
    return true;
  }

  get isNewPreviewTabEnabled(): boolean {

    return true;
  }

  get isPreviewTabInteractive(): boolean {
    const currentState = this.previewTabState$.value;
    return currentState.isEnabled && !currentState.isLoading && !currentState.hasError;
  }

  get isPreviewContentReady(): boolean {

    const hasDeployedUrl = !!this.deployedUrl$.value || !!this.previewUrl;
    const isDeployCompleted = this.newPollingResponseProcessor.isPreviewEnabled();
    return hasDeployedUrl || isDeployCompleted || this.isFailedState;
  }

  get previewTabName(): Observable<string> {
    return this.previewTabName$.asObservable();
  }

  get previewTabState(): Observable<PreviewTabState> {
    return this.previewTabState$.asObservable();
  }

  get previewTabStatus(): Observable<PreviewTabStatus> {
    return this.previewTabStatus$.asObservable();
  }

  get currentPreviewTabState(): PreviewTabState {
    return this.previewTabState$.value;
  }

  get currentPreviewTabStatus(): PreviewTabStatus {
    return this.previewTabStatus$.value;
  }

  get codeTabState(): Observable<CodeTabState> {
    return this.codeTabState$.asObservable();
  }

  get currentCodeTabState(): CodeTabState {
    return this.codeTabState$.value;
  }

  get isInFailedState(): boolean {
    return this.isFailedState;
  }

  get regenerationInProgress$(): Observable<boolean> {
    return this.isRegenerationInProgress$.asObservable();
  }

  get codeGenerationLoading$(): Observable<boolean> {
    return this.isCodeGenerationLoading$.asObservable();
  }


  get isDownloadLoading(): Observable<boolean> {
    return this.isDownloadLoading$.asObservable();
  }


  get isCloneLoading(): Observable<boolean> {
    return this.isCloneLoading$.asObservable();
  }

  private updatePreviewTabState(updates: Partial<PreviewTabState>): void {
    const currentState = this.previewTabState$.value;
    const newState = { ...currentState, ...updates };
    this.previewTabState$.next(newState);

    if (newState.hasError) {
      this.previewTabStatus$.next(PreviewTabStatus.ERROR);
    } else if (newState.isLoading) {
      this.previewTabStatus$.next(PreviewTabStatus.LOADING);
    } else if (newState.isEnabled) {
      this.previewTabStatus$.next(PreviewTabStatus.ENABLED);
    } else {
      this.previewTabStatus$.next(PreviewTabStatus.DISABLED);
    }
  }

  private setPreviewTabDisabled(message: string = 'Preview will be available once the application is deployed'): void {
    this.updatePreviewTabState({
      isEnabled: false,
      isLoading: false,
      hasError: false,
      tooltipMessage: message
    });
  }

  private setPreviewTabLoading(message: string = 'Preparing preview...'): void {

    this.previewTabName$.next('Preview');

    const isRetryOperation = message.toLowerCase().includes('retry');

    this.updatePreviewTabState({
      isEnabled: isRetryOperation ? true : false,
      isLoading: true,
      hasError: false,
      loadingMessage: message,
      tooltipMessage: isRetryOperation ? 'Retrying - preview will update when ready' : 'Preview is being prepared'
    });

    if (isRetryOperation) {
    }
  }

  private setPreviewTabEnabled(message: string = 'View deployed application preview'): void {

    this.previewTabName$.next('Preview');

    this.updatePreviewTabState({
      isEnabled: true,
      isLoading: false,
      hasError: false,
      tooltipMessage: message
    });
  }

  private setPreviewTabError(errorMessage: string = 'An error occurred', tooltipMessage: string = 'View error details'): void {

    this.previewTabName$.next('Error');


    const detailedErrorMessage = this.extractDetailedErrorMessageForPreview(errorMessage);

    this.updatePreviewTabState({
      isEnabled: true,
      isLoading: false,
      hasError: true,
      errorMessage: detailedErrorMessage,
      tooltipMessage
    });


    this.errorDescription$.next(detailedErrorMessage);
  }

  private updatePreviewTabStateForProgress(progress: ProgressState | null): void {
    if (!progress) return;

    const progressUpper = progress.toUpperCase();
    const currentStatus = this.newPollingResponseProcessor.getCurrentStatus();

    switch (progressUpper) {
      case 'OVERVIEW':
      case 'SEED_PROJECT_INITIALIZED':
      case 'FILE_QUEUE':
      case 'DESIGN_SYSTEM_MAPPED':
      case 'COMPONENTS_CREATED':
      case 'LAYOUT_ANALYZED':
      case 'PAGES_GENERATED':
        this.setPreviewTabLoading('Generating your application...');
        break;

      case 'BUILD_STARTED':
      case 'BUILD':
        this.setPreviewTabLoading('Building your application...');
        break;

      case 'BUILD_SUCCEEDED':
      case 'FILES_GENERATED':
        this.setPreviewTabLoading('Preparing deployment...');
        break;

      case 'DEPLOY':
        if (currentStatus === 'IN_PROGRESS') {
          this.setPreviewTabLoading('Deploying your application...');
        } else if (currentStatus === 'COMPLETED') {

          this.setPreviewTabLoading('Finalizing preview...');
        } else if (currentStatus === 'FAILED') {
          this.setPreviewTabError('Deployment failed', 'View error details');
        }
        break;

      case 'DEPLOYED':
        if (currentStatus === 'COMPLETED') {

          if (this.isPreviewContentReady) {
            this.setPreviewTabEnabled('View deployed application preview');
          } else {
            this.setPreviewTabLoading('Preparing preview...');
          }
        }
        break;

      default:

        if (!this.currentPreviewTabState.isEnabled && !this.currentPreviewTabState.hasError) {
          this.setPreviewTabLoading('Processing...');
        }
        break;
    }
  }

  private updatePreviewTabStateForRegeneration(stateUpdate: any): void {
    if (!stateUpdate.phase) return;

    switch (stateUpdate.phase) {
      case 'CODE_GENERATION':
        if (stateUpdate.status === 'IN_PROGRESS') {
          this.setPreviewTabDisabled('Code generation in progress...');
        } else if (stateUpdate.status === 'COMPLETED') {
          this.setPreviewTabDisabled('Building application...');
        }
        break;

      case 'BUILD':
        if (stateUpdate.status === 'IN_PROGRESS') {
          this.setPreviewTabDisabled('Building application...');
        } else if (stateUpdate.status === 'COMPLETED') {
          this.setPreviewTabDisabled('Preparing deployment...');
        }
        break;

      case 'DEPLOY':
        if (stateUpdate.status === 'IN_PROGRESS') {
          this.setPreviewTabDisabled('Deploying application...');
        } else if (stateUpdate.status === 'COMPLETED') {

          this.setPreviewTabEnabled('View regenerated application');
        } else if (stateUpdate.status === 'FAILED') {
          this.setPreviewTabError('Regeneration failed', 'View error details');
        }
        break;

      default:

        this.setPreviewTabDisabled('Processing regeneration...');
        break;
    }

    this.updateCodeTabStateForRegeneration(stateUpdate);
  }

  private handlePreviewError(errorType: 'deployment' | 'loading' | 'network' | 'timeout', errorMessage?: string): void {
    let message = 'An error occurred';
    let tooltip = 'View error details';

    switch (errorType) {
      case 'deployment':
        message = 'Deployment failed';
        tooltip = 'Click to view deployment error details';
        break;
      case 'loading':
        message = 'Preview loading failed';
        tooltip = 'Click to retry loading preview';
        break;
      case 'network':
        message = 'Network error';
        tooltip = 'Check your connection and try again';
        break;
      case 'timeout':
        message = 'Preview timeout';
        tooltip = 'Preview took too long to load';
        break;
    }

    if (errorMessage) {
      message = errorMessage;
    }

    this.setPreviewTabError(message, tooltip);
  }


  private extractDetailedErrorMessageForPreview(errorMessage: string): string {

    if (errorMessage && errorMessage.trim() !== '' && errorMessage !== 'An error occurred') {

      const trimmedMessage = errorMessage.trim();
      if (trimmedMessage.length > 20) {
        return trimmedMessage;
      }
    }


    try {


      let currentErrorState: any = null;
      this.regenerationIntegrationService?.errorState$?.pipe(take(1)).subscribe(state => {
        currentErrorState = state;
      });

      if (currentErrorState?.hasError && currentErrorState.errorMessage) {
        return currentErrorState.errorMessage.trim();
      }
    } catch (error) {
    }


    return 'An error occurred during processing. Please try again.';
  }

  public retryPreviewLoading(): void {

    this.previewError$.next(false);
    this.isFailedState = false;

    this.setPreviewTabLoading('Retrying preview...');

    if (this.previewUrl || this.deployedUrl$.value) {
      const url = this.previewUrl || this.deployedUrl$.value;
      if (url) {
        this.validateAndPrepareUrl(url);
      } else {
        this.setPreviewTabDisabled('No preview URL available. Please regenerate your application.');
      }
    } else {

      this.setPreviewTabDisabled('No preview URL available. Please regenerate your application.');
    }
  }

  private async validateAndPrepareUrl(url: string): Promise<void> {
    try {
      this.setPreviewTabLoading('Validating preview URL...');

      if (!url || !this.isValidUrl(url)) {
        throw new Error('Invalid URL format');
      }

      const isAvailable = await this.checkUrlAvailabilityWithTimeout(url, 60000);

      if (!isAvailable) {
        throw new Error('Preview URL is not accessible');
      }

      this.urlSafe = this.sanitizer.bypassSecurityTrustResourceUrl(url);

      this.setPreviewTabEnabled('View deployed application preview');

    } catch (error) {

      if (error instanceof Error) {
        if (error.message.includes('timeout')) {
          this.handlePreviewError('timeout', 'Preview URL took too long to respond');
        } else if (error.message.includes('network') || error.message.includes('accessible')) {
          this.handlePreviewError('network', 'Preview URL is not accessible');
        } else {
          this.handlePreviewError('loading', error.message);
        }
      } else {
        this.handlePreviewError('loading', 'Unknown error occurred while preparing preview');
      }
    }
  }

  private async checkUrlAvailabilityWithTimeout(url: string, timeoutMs: number): Promise<boolean> {
    return new Promise((resolve) => {
      const timeout = setTimeout(() => {
        resolve(false);
      }, timeoutMs);

      this.checkUrlAvailability(url).then((isAvailable) => {
        clearTimeout(timeout);
        resolve(isAvailable);
      }).catch(() => {
        clearTimeout(timeout);
        resolve(false);
      });
    });
  }

  private isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  private updateCodeTabState(updates: Partial<CodeTabState>): void {
    const currentState = this.codeTabState$.value;
    const newState = { ...currentState, ...updates };
    this.codeTabState$.next(newState);

  }

  private setCodeTabDisabled(message: string = 'Code will be available once generated'): void {
    this.updateCodeTabState({
      isEnabled: false,
      isLoading: false,
      hasError: false,
      tooltipMessage: message
    });
  }

  private setCodeTabLoading(message: string = 'Generating code...'): void {
    this.updateCodeTabState({
      isEnabled: false,
      isLoading: true,
      hasError: false,
      loadingMessage: message,
      tooltipMessage: 'Code is being generated'
    });
  }

  private setCodeTabEnabled(message: string = 'View generated code'): void {
    this.updateCodeTabState({
      isEnabled: true,
      isLoading: false,
      hasError: false,
      tooltipMessage: message
    });
  }

  private setCodeTabError(errorMessage: string = 'Code generation failed', tooltipMessage: string = 'View error details'): void {
    this.updateCodeTabState({
      isEnabled: true,
      isLoading: false,
      hasError: true,
      errorMessage,
      tooltipMessage
    });
  }

  private updateCodeTabStateForRegeneration(stateUpdate: any): void {
    if (!stateUpdate.phase) return;

    const shouldEnableCodeTab = stateUpdate.phase === 'BUILD' && stateUpdate.status === 'IN_PROGRESS';

    switch (stateUpdate.phase) {
      case 'CODE_GENERATION':
        if (stateUpdate.status === 'IN_PROGRESS') {
          this.setCodeTabLoading('Generating code...');
        } else if (stateUpdate.status === 'COMPLETED') {

          this.setCodeTabDisabled('Code will be available during build phase');
        } else if (stateUpdate.status === 'FAILED') {
          this.setCodeTabError('Code generation failed', 'View error details');
        }
        break;

      case 'SEED_PROJECT_INITIALIZED':
        if (stateUpdate.status === 'IN_PROGRESS') {

          if (!this.isCodeGenerationComplete) {
            this.setCodeTabLoading('Loading seed project template...');
          }

        } else if (stateUpdate.status === 'COMPLETED') {
          this.setCodeTabEnabled('View seed project template');
        } else if (stateUpdate.status === 'FAILED') {
          this.setCodeTabError('Template loading failed', 'View error details');
        }
        break;

      case 'BUILD':
        if (stateUpdate.status === 'IN_PROGRESS') {

          this.setCodeTabEnabled('View generated code');
        } else if (stateUpdate.status === 'COMPLETED') {
          this.setCodeTabEnabled('View generated code');
        } else if (stateUpdate.status === 'FAILED') {

          this.setCodeTabDisabled('Build failed - code unavailable');
        }
        break;

      case 'DEPLOY':
        if (stateUpdate.status === 'IN_PROGRESS') {

          this.setCodeTabEnabled('View generated code');
        } else if (stateUpdate.status === 'COMPLETED') {
          this.setCodeTabEnabled('View generated code');
        } else if (stateUpdate.status === 'FAILED') {
          this.setCodeTabError('Deployment failed', 'View error details');
        }
        break;

      default:

        if (this.isCodeGenerationComplete && this.files$.value.length > 0) {

          this.setCodeTabEnabled('View seed project template');
        } else if (shouldEnableCodeTab) {
          this.setCodeTabEnabled('View generated code');
        } else if (!this.currentCodeTabState.hasError) {
          this.setCodeTabDisabled('Code will be available during build phase');
        }
        break;
    }
  }

  private updateCodeTabStateForProgress(progress: ProgressState | null): void {
    if (!progress) return;

    const progressUpper = progress.toUpperCase();
    const currentStatus = this.newPollingResponseProcessor.getCurrentStatus();

    const shouldEnableCodeTab = progressUpper === 'BUILD' && currentStatus === 'IN_PROGRESS';

    switch (progressUpper) {
      case 'CODE_GENERATION':
        if (currentStatus === 'IN_PROGRESS') {
          this.setCodeTabLoading('Generating code...');
        } else if (currentStatus === 'COMPLETED') {

          this.setCodeTabDisabled('Code will be available during build phase');
        } else if (currentStatus === 'FAILED') {
          this.setCodeTabError('Code generation failed', 'View error details');
        }
        break;

      case 'SEED_PROJECT_INITIALIZED':
        if (currentStatus === 'IN_PROGRESS') {

          if (!this.isCodeGenerationComplete) {
            this.setCodeTabLoading('Loading seed project template...');
          }

        } else if (currentStatus === 'COMPLETED') {
          this.setCodeTabEnabled('View seed project template');
        } else if (currentStatus === 'FAILED') {
          this.setCodeTabError('Template loading failed', 'View error details');
        }
        break;

      case 'FILE_QUEUE':
      case 'DESIGN_SYSTEM_MAPPED':
      case 'COMPONENTS_CREATED':
      case 'LAYOUT_ANALYZED':
      case 'PAGES_GENERATED':

        if (this.isCodeGenerationComplete && this.files$.value.length > 0) {
          this.setCodeTabEnabled('View seed project template');
        } else {
          this.setCodeTabDisabled('Code will be available during build phase');
        }
        break;

      case 'FILES_GENERATED':
        if (currentStatus === 'IN_PROGRESS') {
          this.setCodeTabLoading('Generating final files...');
        } else if (currentStatus === 'COMPLETED') {
          this.setCodeTabEnabled('View generated files');
        } else if (currentStatus === 'FAILED') {
          this.setCodeTabError('File generation failed', 'View error details');
        }
        break;

      case 'BUILD':
        if (currentStatus === 'IN_PROGRESS') {

          this.setCodeTabEnabled('View generated code');
        } else if (currentStatus === 'COMPLETED') {
          this.setCodeTabEnabled('View generated code');
        } else if (currentStatus === 'FAILED') {

          this.setCodeTabDisabled('Build failed - code unavailable');
        }
        break;

      case 'DEPLOY':
      case 'DEPLOYED':
        if (currentStatus === 'IN_PROGRESS') {

          this.setCodeTabEnabled('View generated code');
        } else if (currentStatus === 'COMPLETED') {
          this.setCodeTabEnabled('View generated code');
        } else if (currentStatus === 'FAILED') {
          this.setCodeTabError('Deployment failed', 'View error details');
        }
        break;

      default:

        if (this.isCodeGenerationComplete && this.files$.value.length > 0) {

          this.setCodeTabEnabled('View seed project template');
        } else if (shouldEnableCodeTab) {
          this.setCodeTabEnabled('View generated code');
        } else if (!this.currentCodeTabState.hasError) {
          this.setCodeTabDisabled('Code will be available during build phase');
        }
        break;
    }
  }

  private showProgressIndicator(): void {
    this.isLoading$.next(true);
    this.isPreviewLoading$.next(true);
    this.previewError$.next(false);

    this.setPreviewTabLoading('Generating your application...');
  }

  private showCompletionState(): void {
    this.isLoading$.next(false);
    this.isPreviewLoading$.next(false);
    this.previewError$.next(false);
    this.isCodeGenerationComplete = true;

    if (this.isPreviewContentReady) {
      this.setPreviewTabEnabled('View deployed application preview');
    } else {
      this.setPreviewTabDisabled('Deployment in progress...');
    }

  }

  private addGenerationAccordionToChat(): void {

    this.generationVersionCounter++;

    const projectName = this.projectName || 'Untitled Project';

    let generatedFiles: string[] = [];

    if (this.generatedCode && this.generatedCode.length > 0) {
      generatedFiles = this.generatedCode.map((file: any) => file.name || file.path || 'Unknown file');
    }

    else if (this.artifactsData && this.artifactsData.length > 0) {
      generatedFiles = this.artifactsData
        .filter(artifact => artifact.type === 'file' || artifact.type === 'code')
        .map(artifact => artifact.name);
    }

    else {
      this.files$.pipe(take(1)).subscribe(files => {
        if (files && files.length > 0) {
          generatedFiles = files.map(file => file.name || 'Unknown file');
        }
      });
    }

    if (generatedFiles.length === 0) {
      generatedFiles = ['Application files generated'];
    }

    const generationResult: GenerationResult = {
      type: 'success',
      version: this.generationVersionCounter,
      projectName: projectName,
      files: generatedFiles,
      timestamp: new Date()
    };

    if (this.chatWindow) {
      this.chatWindow.addGenerationResult(generationResult);
    } else {
    }
  }

  private addRegenerationGenerationAccordionToChat(codeFiles: FileData[]): void {

    this.generationVersionCounter++;

    const projectName = this.projectName || 'Untitled Project';

    let regeneratedFiles: string[] = [];

    if (codeFiles && codeFiles.length > 0) {
      regeneratedFiles = codeFiles.map(file => file.path || 'Unknown file');
    } else {

      if (this.generatedCode && this.generatedCode.length > 0) {
        regeneratedFiles = this.generatedCode.map((file: any) => file.name || file.path || 'Unknown file');
      } else {
        regeneratedFiles = ['Regenerated application files'];
      }
    }

    const generationResult: GenerationResult = {
      type: 'success',
      version: this.generationVersionCounter,
      projectName: projectName,
      files: regeneratedFiles,
      timestamp: new Date()
    };

    if (this.chatWindow) {
      this.chatWindow.addGenerationResult(generationResult);
    } else {
    }
  }

  private showErrorState(): void {

    this.isLoading$.next(false);
    this.isPreviewLoading$.next(false);
    this.previewError$.next(true);
    this.isFailedState = true;


    if (this.persistentArtifacts.size > 0) {
      this.restorePersistentArtifacts();
    }

    this.handleLayoutAnalysisFailed();

    this.previewTabName$.next('Error');


    const sseErrorMessage = this.extractErrorMessageFromSSELog();
    const errorMessage = sseErrorMessage || 'An error occurred during processing. Please try again.';

    this.errorDescription$.next(errorMessage);

    this.extractErrorMessageFromProgressDescription();

    this.setPreviewTabError(errorMessage, 'View error details');

    this.isPreviewTabEnabled = true;

    if (!this.userSelectedTab) {
      this.togglePreviewView();

      this.userSelectedTab = false;
    } else {

      this.currentView$.next('preview');
    }

    this.cdr.detectChanges();

  }

  private displayReadmeContent(content: string): void {
    const readmeIndex = this.artifactsData.findIndex(item => item.name === 'README');
    if (readmeIndex !== -1) {
      this.artifactsData[readmeIndex].content = content;
    } else {

    }
  }

  private displayLayoutContent(layoutCode: string): void {

    this.preventDuplicateLayoutAnalyzed();

    if (!layoutCode || !this.layoutMapping[layoutCode]) {
      return;
    }

    const imageUrl = `assets/images/layout-${layoutCode}.png`;
    const layoutName = this.layoutMapping[layoutCode];

    const layoutIndex = this.artifactsData.findIndex(item => item.name === 'Layout Analyzed');
    if (layoutIndex !== -1) {

      this.artifactsData[layoutIndex].content = imageUrl;
    } else {

    }

    this.layoutData = [layoutCode];

    this.layoutAnalyzedData = [
      {
        key: layoutCode,
        name: layoutName,
        imageUrl: imageUrl,
      },
    ];

    this.hasLayoutAnalyzed = true;

    this.stopLayoutAnalyzing();

  }

  private displayDesignTokens(tokens: DesignTokensData): void {
    const designSystemIndex = this.artifactsData.findIndex(item => item.name === 'Design System');
    if (designSystemIndex !== -1) {
      this.artifactsData[designSystemIndex].content = tokens;
    } else {
      this.artifactsData.push({
        name: 'Design System',
        type: 'component',
        content: tokens,
      });

      this.ensureLogsFileAtBottom();
    }
    this.hasDesignSystem = true;
    this.designSystemData = tokens;
  }

  ngAfterViewInit(): void {

    setTimeout(() => {

      if (this.chatWindow) {

        this.chatWindow.showImagePreview = (imageUrl: string, imageName: string = 'Image') => {

          this.showImagePreview(imageUrl, imageName);
        };
      }
    }, 0);

    this.setupPanelWidthObserver();
    this.setupResizerEventListener();
    this.setupCanvasWheelListener();
  }

  private setupCanvasWheelListener(): void {
    if (this.canvasContent?.nativeElement) {

      this.wheelEventListener = (event: WheelEvent) => {
        this.onCanvasWheel(event);
      };

      this.ngZone.runOutsideAngular(() => {
        this.canvasContent.nativeElement.addEventListener('wheel', this.wheelEventListener, {
          passive: false,
          capture: false
        });
      });
    }
  }

  private setupResizerEventListener(): void {

    setTimeout(() => {
      const resizer = document.querySelector('.resizer') as HTMLElement;
      if (resizer) {
        resizer.addEventListener('mousedown', (event: MouseEvent) => {
          this.startResize(event);
        });
      } else {

        setTimeout(() => this.setupResizerEventListener(), 100);
      }
    }, 100);
  }

  private setupPanelWidthObserver(): void {

    if (typeof ResizeObserver !== 'undefined') {
      const resizeObserver = new ResizeObserver(entries => {
        for (const entry of entries) {
          const width = entry.contentRect.width;

          this.shouldHideProjectName$.next(width < 400);
          this.cdr.detectChanges();
        }
      });

      setTimeout(() => {
        const leftPanel = document.querySelector('.awe-leftpanel') as HTMLElement;
        if (leftPanel) {
          resizeObserver.observe(leftPanel);
        }
      }, 100);
    } else {

      const checkPanelWidth = () => {
        const leftPanel = document.querySelector('.awe-leftpanel') as HTMLElement;
        if (leftPanel) {
          const width = leftPanel.offsetWidth;
          this.shouldHideProjectName$.next(width < 400);
          this.cdr.detectChanges();
        }
      };

      window.addEventListener('resize', checkPanelWidth);

      setTimeout(checkPanelWidth, 100);
    }
  }

  private initializeAppState(): void {

    this.subscription = this.appStateService.project$.subscribe(projectState => {

      if (projectState.projectId && projectState.projectId !== this.projectId) {

        if (this.projectId) {

          this.stepperStateService.triggerStepperReset();
        }

        this.projectId = projectState.projectId;

        this.hasAddedInitialGenerationAccordion = false;
        this.generationVersionCounter = 0;

        this.stepperStateService.setCurrentProjectId(this.projectId);
      }

      if (projectState.jobId && projectState.jobId !== this.jobId) {
        this.jobId = projectState.jobId;

      }

      if (projectState.prompt && this.lightMessages.length === 0 && !this.isUIDesignMode$.value) {

        this.lightMessages = [
          { text: projectState.prompt, from: 'user', theme: 'light' },
          {
            text: "I'm generating code based on your request. Please wait while I process your input...",
            from: 'ai',
            theme: 'light',
          },
        ];
      } else if (this.isUIDesignMode$.value) {
      }

      if (this.projectId && this.jobId && !this.isPolling && !this.isUIDesignMode$.value) {

        this.startLayoutAnalyzing();

        this.startStatusMonitoring(this.projectId, this.jobId, 'code generation');
        this.updatePollingStatus(true);

      } else if (this.isUIDesignMode$.value) {
      }
    });

    this.currentView$.next('preview');
    this.isLoading$.next(true);
    this.isPreviewLoading$.next(true);
    this.isPreviewActive$.next(true);
    this.isCodeActive$.next(false);

    this.isLogsTabEnabled = true;
    this.isPreviewTabEnabled = true;
    this.isCodeTabEnabled = false;

    this.subscription.add(
      this.newPollingResponseProcessor.logContent$.subscribe(logContent => {
        if (logContent && logContent.trim() !== '') {

          const logLines = logContent.split('\n').filter(line => line.trim() !== '');

          this.startLogStreaming(logLines);

          this.hasLogs = true;

          this.autoEnableLogsTabIfNeeded();

          this.enableArtifactsTabIfNeeded();

          for (const log of logLines) {
            if (typeof log === 'string' && log.includes('"message"') && log.includes('"data"')) {
              try {

                const jsonStartIndex = log.indexOf('{');
                if (jsonStartIndex !== -1) {
                  const jsonPart = log.substring(jsonStartIndex);
                  const parsedData = JSON.parse(jsonPart);

                  if (parsedData.message && parsedData.data) {

                    const layoutKeys = parsedData.data;

                    this.layoutData = [];

                    if (typeof layoutKeys === 'string') {

                      if (this.layoutMapping[layoutKeys]) {
                        this.layoutData = [layoutKeys];
                      }
                    } else if (Array.isArray(layoutKeys)) {

                      const validKeys = layoutKeys.filter(
                        key => typeof key === 'string' && this.layoutMapping[key]
                      );

                      if (validKeys.length > 0) {
                        this.layoutData = [validKeys[0]];
                      }
                    } else if (typeof layoutKeys === 'object' && layoutKeys !== null) {

                      const validKeys = Object.keys(layoutKeys).filter(
                        key => this.layoutMapping[key]
                      );

                      if (validKeys.length > 0) {
                        this.layoutData = [validKeys[0]];
                      }
                    }

                    if (this.layoutData.length === 0) {
                    } else {

                      if (log.includes('LAYOUT_ANALYZED')) {

                        this.captureLayoutAnalyzedState();
                      } else if (log.includes('DESIGN_SYSTEM_MAPPED')) {

                        this.designSystemData = parsedData.data;

                        this.captureDesignSystemMappedState();
                      }
                    }

                    this.isLayoutLoading = false;
                  }
                }
              } catch (e) {
              }
            }
          }

          this.addLogUpdateToChatWindow(logLines);
        }
      })
    );

    this.subscription.add(
      this.newPollingResponseProcessor.artifactData$.subscribe(artifactData => {
        if (artifactData) {

          if (artifactData.type === 'text') {

            this.updateProjectOverviewArtifact(artifactData.content);
          } else if (artifactData.type === 'json') {

            this.updateDesignSystemArtifact(artifactData);
          }
        }
      })
    );

    this.subscription.add(
      this.newPollingResponseProcessor.codeFiles$.subscribe(codeFiles => {
        if (codeFiles && codeFiles.length > 0) {

          const fileModels = codeFiles.map(file => ({
            name: file.path,
            type: 'file' as const,
            content: file.code,
          }));

          this.generatedCode = fileModels;
          this.isCodeGenerationComplete = true;

          if (this.isCodeGenerationLoading$.value) {
            this.isCodeGenerationLoading$.next(false);
            this.codeRegenerationProgressDescription = '';

            this.createRegenerationSuccessResult(codeFiles);

            const lastMessage = this.getLastMessage();

            if (lastMessage && lastMessage.from === 'ai') {
              const isLastMessageIntro = this.isIntroMessage(lastMessage);

              if (isLastMessageIntro) {

                lastMessage.showLoadingIndicator = false;
                lastMessage.loadingPhase = 'completed';
                lastMessage.mainAPIInProgress = false;

                this.codeGenerationIntroService.generateCompletionMessage(
                  this.lastUserRequest || 'code regeneration',
                  this.getCurrentCodeFiles(),
                  { preservedEdits: 0, updatedFiles: 0, conflicts: 0 }
                ).subscribe(completionMessage => {
                  this.addCompletionMessageWithTypewriter(completionMessage);
                });
              } else {

                this.codeGenerationIntroService.generateCompletionMessage(
                  this.lastUserRequest || 'code regeneration',
                  this.getCurrentCodeFiles(),
                  { preservedEdits: 0, updatedFiles: 0, conflicts: 0 }
                ).subscribe(completionMessage => {
                  lastMessage.text = completionMessage;
                  this.cdr.markForCheck();
                });
              }
            } else {

              this.codeGenerationIntroService.generateCompletionMessage(
                this.lastUserRequest || 'code regeneration',
                this.getCurrentCodeFiles(),
                { preservedEdits: 0, updatedFiles: 0, conflicts: 0 }
              ).subscribe(completionMessage => {
                this.addCompletionMessageWithTypewriter(completionMessage);
              });
            }

            this.cdr.detectChanges();
          }

          this.processCodeFromService(fileModels);
        }
      })
    );

    this.subscription.add(
      this.newPollingResponseProcessor.previewUrl$.subscribe(previewUrl => {
        if (previewUrl && previewUrl.trim() !== '') {
          this.previewUrl = previewUrl;
          this.isNewPreviewEnabled = true;
        }
      })
    );

    this.subscription.add(
      this.newPollingResponseProcessor.currentProgress$.subscribe(progress => {
        if (progress) {
          this.currentProgressState = progress;

        }
      })
    );

    this.subscription.add(
      this.newPollingResponseProcessor.currentStatus$.subscribe(status => {
        if (status) {

          if (status === 'COMPLETED') {

            if (this.isCodeGenerationLoading$.value) {

            }
          } else if (status === 'FAILED') {


            if (this.persistentArtifacts.size > 0) {
              this.restorePersistentArtifacts();
            }

            if (this.isCodeGenerationLoading$.value) {
              this.isCodeGenerationLoading$.next(false);
              this.isRegenerationInProgress$.next(false);
              this.resetRegenerationCallFlag('SSE FAILED status');
              this.codeRegenerationProgressDescription = '';

              this.createRegenerationErrorResult('Code regeneration failed. Please try again or check the logs for more details.');

              const lastMessage = this.getLastMessage();
              if (lastMessage && lastMessage.from === 'ai') {
                lastMessage.text = 'Code regeneration failed. Please try again or check the logs for more details.';
                lastMessage.showLoadingIndicator = false;
                lastMessage.loadingPhase = 'completed';
                lastMessage.mainAPIInProgress = false;
              }

              this.toastService.error('Code regeneration failed. Please try again.');

              this.cdr.detectChanges();
            }
          }
        }
      })
    );

    this.themeService.themeObservable
      .pipe(takeUntil(this.destroy$))
      .subscribe((theme: 'light' | 'dark') => {
        this.currentTheme$.next(theme);

      });

    this.subscription.add(
      this.newPollingResponseProcessor.artifactData$.subscribe(artifactData => {
        if (artifactData && this.isUIDesignArtifact(artifactData)) {
          this.handleUIDesignResponse(artifactData.content);
        }
      })
    );

    this.generatedCode = this.codeSharingService.getGeneratedCode();

    if (this.generatedCode) {
      this.isCodeGenerationComplete = true;
      this.isPreviewLoading$.next(false);

      this.layoutData = [];

      if (!this.userSelectedTab) {
        this.currentView$.next('preview');
      } else {
      }

      setTimeout(() => {
        const iframe = document.querySelector('.preview-frame') as HTMLIFrameElement;
        if (!iframe) {
        }
      }, 200);

      this.processCodeFromService(this.generatedCode);
    } else {

      if (this.projectId && this.jobId && !this.isPolling && !this.isUIDesignMode$.value) {

        this.startStatusMonitoring(this.projectId, this.jobId, 'code generation');
        this.updatePollingStatus(true);
      } else if (this.isPolling) {
      } else if (this.isUIDesignMode$.value) {
      }
    }

    this.subscription = this.codeSharingService.generatedCode$.subscribe(code => {
      if (code) {
        this.isCodeGenerationComplete = true;
        this.processCodeFromService(code);
      }
    });
  }

  processApiResponse(response: any): void {
    if (!response || !response.details) return;

    const { progress, progress_description, log } = response.details;

    let formattedDescription = progress_description;
    if (progress_description) {

      if (progress_description.includes('LAYOUT_ANALYZED')) {
        formattedDescription = this.formatLayoutAnalyzedDescription(progress_description);
      }

      if (formattedDescription !== progress_description) {

      } else {

      }
    }

    if (progress) {
      this.currentProgressState = progress;

      if (progress.includes('LAYOUT_ANALYZED') || progress.includes('DESIGN_SYSTEM_MAPPED')) {
        this.isLayoutLoading = true;

        if (log && typeof log === 'string') {
          try {

            if (log.includes('{') && log.includes('}')) {

              const parsedLog = JSON.parse(log);

              if (parsedLog.message && parsedLog.data) {

                const layoutKeys = parsedLog.data;

                this.layoutData = [];

                if (progress.includes('LAYOUT_ANALYZED')) {

                } else if (progress.includes('DESIGN_SYSTEM_MAPPED')) {

                  this.designSystemData = parsedLog.data;
                }

                if (typeof layoutKeys === 'string') {

                  if (this.layoutMapping[layoutKeys]) {
                    this.layoutData = [layoutKeys];
                  }
                } else if (Array.isArray(layoutKeys)) {

                  const validKeys = layoutKeys.filter(
                    key => typeof key === 'string' && this.layoutMapping[key]
                  );

                  if (validKeys.length > 0) {
                    this.layoutData = [validKeys[0]];
                  }
                } else if (typeof layoutKeys === 'object' && layoutKeys !== null) {

                  const validKeys = Object.keys(layoutKeys).filter(key => this.layoutMapping[key]);

                  if (validKeys.length > 0) {
                    this.layoutData = [validKeys[0]];
                  }
                }
              }
            }
          } catch (e) {
          }
        }

        if (
          !this.layoutData ||
          this.layoutData.length === 0 ||
          !this.layoutMapping[this.layoutData[0]]
        ) {

          this.isLayoutLoading = true;
          this.isPreviewLoading$.next(true);
          this.isLoading$.next(true);
        } else {

          this.isLayoutLoading = false;
          this.isPreviewLoading$.next(false);
          this.isLoading$.next(false);

          if (progress.includes('LAYOUT_ANALYZED')) {

            this.captureLayoutAnalyzedState();
          } else if (progress.includes('DESIGN_SYSTEM_MAPPED')) {

            this.captureDesignSystemMappedState();
          }
        }
      }
    }

    if (progress_description && (!this.layoutData || this.layoutData.length === 0)) {

      if (progress_description.includes('{') && progress_description.includes('}')) {
        try {
          const jsonStartIndex = progress_description.indexOf('{');
          const jsonEndIndex = progress_description.lastIndexOf('}') + 1;

          if (jsonStartIndex !== -1 && jsonEndIndex !== -1) {
            const jsonPart = progress_description.substring(jsonStartIndex, jsonEndIndex);
            const parsedData = JSON.parse(jsonPart);

            if (parsedData.message && parsedData.data) {

              const layoutKeys = parsedData.data;

              this.layoutData = [];

              if (typeof layoutKeys === 'string') {

                if (this.layoutMapping[layoutKeys]) {
                  this.layoutData = [layoutKeys];
                }
              } else if (Array.isArray(layoutKeys)) {

                this.layoutData = layoutKeys.filter(
                  key => typeof key === 'string' && this.layoutMapping[key]
                );

                if (this.layoutData.length > 0) {
                  this.layoutData.forEach(_key => {

                  });
                }
              } else if (typeof layoutKeys === 'object' && layoutKeys !== null) {

                this.layoutData = Object.keys(layoutKeys).filter(key => this.layoutMapping[key]);

                if (this.layoutData.length > 0) {
                  this.layoutData.forEach(_key => {

                  });

                  if (progress_description.includes('LAYOUT_ANALYZED')) {

                    this.captureLayoutAnalyzedState();
                  } else if (progress_description.includes('DESIGN_SYSTEM_MAPPED')) {

                    this.designSystemData = parsedData.data;

                    this.captureDesignSystemMappedState();
                  }
                }
              }
            }
          }
        } catch (e) {
        }
      }

      if (!this.layoutData || this.layoutData.length === 0) {

      }
    }
  }

  getPageTitle(index: number, layoutKey: string): string {

    const defaultTitles = [
      'Home Page',
      'About Us Page',
      'Products Page',
      'Services Page',
      'Blog Page',
      'Contact Page',
      'Gallery Page',
      'FAQ Page',
    ];

    if (layoutKey && this.layoutMapping[layoutKey]) {

      return defaultTitles[index % defaultTitles.length] + ` (${layoutKey})`;
    }

    return defaultTitles[index % defaultTitles.length] || `Page ${index + 1}`;
  }

  private processCodeFromService(code: any): void {
    this.generatedCode = code;

    const fileModels: FileModel[] = [];

    if (Array.isArray(this.generatedCode)) {

      if (
        this.generatedCode.length > 0 &&
        'fileName' in this.generatedCode[0] &&
        'content' in this.generatedCode[0]
      ) {

        for (const file of this.generatedCode) {
          fileModels.push({
            name: file.fileName,
            type: 'file',
            content:
              typeof file.content === 'string'
                ? file.content
                : JSON.stringify(file.content, null, 2),
          });
        }
      } else {

        for (const item of this.generatedCode) {
          if (typeof item === 'object' && item !== null) {

            const name = item.name || item.fileName || item.path || 'unknown.txt';
            const content = item.content || '';
            fileModels.push({
              name,
              type: 'file',
              content: typeof content === 'string' ? content : JSON.stringify(content, null, 2),
            });
          }
        }
      }
    } else if (typeof this.generatedCode === 'string') {

      try {
        const parsedCode = JSON.parse(this.generatedCode);

        if (Array.isArray(parsedCode)) {

          for (const item of parsedCode) {
            if (typeof item === 'object' && item !== null) {
              const name = item.fileName || item.name || item.path || 'unknown.txt';
              const content = item.content || '';
              fileModels.push({
                name,
                type: 'file',
                content: typeof content === 'string' ? content : JSON.stringify(content, null, 2),
              });
            }
          }
        } else if (typeof parsedCode === 'object') {

          for (const [filePath, content] of Object.entries(parsedCode)) {
            fileModels.push({
              name: filePath,
              type: 'file',
              content: typeof content === 'string' ? content : JSON.stringify(content, null, 2),
            });
          }
        }
      } catch (e) {

        fileModels.push({
          name: 'output.txt',
          type: 'file',
          content: this.generatedCode,
        });
      }
    } else if (typeof this.generatedCode === 'object' && this.generatedCode !== null) {

      for (const [filePath, content] of Object.entries(this.generatedCode)) {
        fileModels.push({
          name: filePath,
          type: 'file',
          content: typeof content === 'string' ? content : JSON.stringify(content, null, 2),
        });
      }
    } else {
    }

    const projectState = this.appStateService.getProjectState();
    const technology = projectState.technology || 'angular';

    const sortedFileModels = this.sortFilesByTechnology(fileModels, technology);

    if (sortedFileModels.length > 0) {
      this.files$.next(sortedFileModels);

      this.fileTreePersistenceService.initializeBaseline(sortedFileModels);

      this.setCodeTabEnabled('View generated code');

    } else {
    }

    this.isLoading$.next(false);
    this.isPreviewLoading$.next(false);
    this.isCodeGenerationComplete = true;

    this.layoutData = [];

    if (!this.userSelectedTab) {
      this.togglePreviewView();
    } else {

      this.isCodeActive$.next(false);
      this.isPreviewActive$.next(true);

    }

    setTimeout(() => {
      const iframe = document.querySelector('.preview-frame') as HTMLIFrameElement;
      if (!iframe) {

        if (this.currentView$.value === 'preview') {
          this.currentView$.next('editor');
          setTimeout(() => {
            this.currentView$.next('preview');
          }, 50);
        } else {
        }
      }
    }, 200);

    if (this.codeSharingService.getDeployedUrl()) {
      this.deployedUrl$.next(this.codeSharingService.getDeployedUrl() ?? '');
    }

     this.isPreviewTabEnabled = !!this.deployedUrl$.value;

    if (this.deployedUrl$.value) {
      this.urlSafe = this.sanitizer.bypassSecurityTrustResourceUrl(this.deployedUrl$.value);
    } else {
      this.urlSafe = undefined;
    }

    this.subscription?.add(
      this.codeSharingService.deployedUrl$.subscribe(url => {
        this.deployedUrl$.next(url ?? '');
        if (url) {
          this.isPreviewLoading$.next(false);
          this.previewIcon$.next('bi-eye');
          this.isPreviewTabEnabled = true;
        }
      })
    );
  }

  toggleView() {
    if (!this.deployedUrl$.value && this.currentView$.value === 'editor') {

      this.isPreviewLoading$.next(true);
      this.previewIcon$.next('bi-arrow-clockwise');
      this.previewError$.next(false);

      const generatedCode = this.codeSharingService.getGeneratedCode();
      if (generatedCode) {
        this.codeGenerationService.getPreviewDeployment(generatedCode).subscribe({
          next: response => {
            if (response.status === 'success' && response.deployedUrl) {
              this.codeSharingService.setDeployedUrl(response.deployedUrl);
              this.currentView$.next('preview');
              this.previewError$.next(false);
            } else {
              this.previewError$.next(true);
              this.currentView$.next('preview');
            }
          },
          error: _error => {
            this.isPreviewLoading$.next(false);
            this.previewIcon$.next('bi-code-slash');
            this.previewError$.next(true);
            this.currentView$.next('preview');
          },
          complete: () => {
            this.isPreviewLoading$.next(false);
          },
        });
      }
    } else {

      const newView = this.currentView$.value === 'editor' ? 'preview' : 'editor';
      this.currentView$.next(newView);
      this.previewIcon$.next(newView === 'editor' ? 'bi-code-slash' : 'bi-eye');
    }
  }

  @HostListener('window:resize')
  onWindowResize() {

    if (!this.isResizing$.value) {

      const leftPanel = document.querySelector('.awe-leftpanel') as HTMLElement;
      const rightPanel = document.querySelector('.awe-rightpanel') as HTMLElement;
      const container = document.querySelector('.awe-splitscreen') as HTMLElement;

      if (leftPanel && rightPanel && container && !this.isLeftPanelCollapsed$.value) {

        const containerWidth = container.offsetWidth;

        const leftWidthStr = this.defaultLeftPanelWidth;
        const rightWidthStr = this.defaultRightPanelWidth;

        const leftWidthPercent = parseFloat(leftWidthStr);
        const rightWidthPercent = parseFloat(rightWidthStr);

        if (!isNaN(leftWidthPercent) && !isNaN(rightWidthPercent)) {

          const minWidthPx = parseInt(this.minWidth$.value || '300', 10);

          if ((containerWidth * leftWidthPercent) / 100 < minWidthPx) {

            const newLeftPercentage = ((minWidthPx / containerWidth) * 100).toFixed(2) + '%';
            const newRightPercentage =
              (((containerWidth - minWidthPx) / containerWidth) * 100).toFixed(2) + '%';

            leftPanel.style.width = newLeftPercentage;
            rightPanel.style.width = newRightPercentage;

            this.defaultLeftPanelWidth = newLeftPercentage;
            this.defaultRightPanelWidth = newRightPercentage;
          }

          else if ((containerWidth * rightWidthPercent) / 100 < minWidthPx) {

            const newRightPercentage = ((minWidthPx / containerWidth) * 100).toFixed(2) + '%';
            const newLeftPercentage =
              (((containerWidth - minWidthPx) / containerWidth) * 100).toFixed(2) + '%';

            leftPanel.style.width = newLeftPercentage;
            rightPanel.style.width = newRightPercentage;

            this.defaultLeftPanelWidth = newLeftPercentage;
            this.defaultRightPanelWidth = newRightPercentage;
          }
        }
      }
    }
  }

  ngOnDestroy() {

    this.clearRegenerationTimeout();

    if (this.subscription) {
      this.subscription.unsubscribe();
      this.subscription = null;
    }

    if (this.sseDataProcessorSubscription) {
      this.sseDataProcessorSubscription.unsubscribe();
      this.sseDataProcessorSubscription = null;
    }

    this.destroy$.next();
    this.destroy$.complete();

    if (this.checkSessionStorageInterval) {
      clearInterval(this.checkSessionStorageInterval);
      this.checkSessionStorageInterval = null;
    }

    if (this.logStreamTimer) {
      clearInterval(this.logStreamTimer);
      this.logStreamTimer = null;
    }

    if (this.autoSwitchToLogsTimer) {
      clearTimeout(this.autoSwitchToLogsTimer);
      this.autoSwitchToLogsTimer = null;
    }

    if (this.resizeTimeout) {
      clearTimeout(this.resizeTimeout);
      this.resizeTimeout = null;
    }

    if (this.canvasResizeObserver) {
      this.canvasResizeObserver.disconnect();
      this.canvasResizeObserver = undefined;
    }

    if (this.wheelEventListener && this.canvasContent?.nativeElement) {
      this.canvasContent.nativeElement.removeEventListener('wheel', this.wheelEventListener);
      this.wheelEventListener = undefined;
    }

    this.cleanupStatusMonitoring();

    this.pollingService.resetLogs();
    this.currentProgressState = '';
    this.lastProgressDescription = '';
    this.pollingStatus = 'PENDING';
    this.hasLogs = false;
    this.logMessages = [];
    this.formattedLogMessages = [];
    this.processedLogHashes.clear();
    this.expandedCodeLogs.clear();
    this.promptService.setImage(null);
    this.promptService.setPrompt('');
    this.promptSubmissionService.resetSubmissionState();

    this.codeSharingService.resetState();

    this.appStateService.updateProjectState({
      codeGenerated: false,
      generatedCode: null,
    });

    this.resetComponentState();


    try {

      const iframe = document.querySelector('.preview-frame') as HTMLIFrameElement;
      if (iframe && iframe.contentWindow) {

        iframe.src = 'about:blank';
      }
    } catch (error) {
    }

    this.urlSafe = undefined;

    this.userSelectedTab = false;

    this.stepperStateService.clearCurrentProjectId();

    document.body.classList.remove('user-select-none');

    if (this.isResizing$.value) {
      const leftPanel = document.querySelector('.awe-leftpanel') as HTMLElement;
      const rightPanel = document.querySelector('.awe-rightpanel') as HTMLElement;
      const resizer = document.querySelector('.resizer') as HTMLElement;
      const overlay = document.getElementById('resize-overlay');

      if (leftPanel) leftPanel.classList.remove('dragging');
      if (rightPanel) rightPanel.classList.remove('dragging');
      if (resizer) resizer.classList.remove('active');

      if (overlay) {
        document.body.removeChild(overlay);
      }

      document.body.classList.remove('user-select-none');
      document.documentElement.classList.remove('resizing-active');

      this.isResizing$.next(false);
    }

    this.clearAllTypewriterTimeouts();

    this.clearAllLoadingNodes();

    this.destroy$.next();
    this.destroy$.complete();
  }

  private cleanupStatusMonitoring(): void {
    if (this.useSSE && this.enhancedSSEService.isMonitoring()) {
      this.enhancedSSEService.stopMonitoring();
    }

    if (this.sseSubscription) {
      this.sseSubscription.unsubscribe();
      this.sseSubscription = null;
    }

    if (this.sseDataProcessorSubscription) {
      this.sseDataProcessorSubscription.unsubscribe();
      this.sseDataProcessorSubscription = null;
    }

    if (this.isPolling) {
      this.pollingService.stopPolling();
      this.isPolling = false;
    }
  }

  private startStatusMonitoring(projectId: string, jobId: string, taskType: string): void {
    // Skip SSE monitoring in project loading mode - all data comes from single API response
    if (this.isProjectLoadingMode$.value) {
      console.log('🚫 Skipping SSE monitoring in project loading mode for task:', taskType);
      return;
    }

    console.log('🔄 Starting SSE monitoring for task:', taskType);
    if (this.useSSE) {
      this.startSSEMonitoring(projectId, jobId, taskType);
    }
  }

  private startSSEMonitoring(projectId: string, jobId: string, taskType: string): void {

    const sseOptions = {
      reconnectInterval: 5000,
      maxReconnectAttempts: 10,
      enableExponentialBackoff: true,
      backoffFactor: 1.5,
      maxBackoffInterval: 60000,
      enableHeartbeat: true,
      heartbeatInterval: 60000
    };

    this.sseSubscription = this.enhancedSSEService.startMonitoring(
      projectId,
      jobId,
      sseOptions,
      () => this.fallbackToPolling(projectId, jobId, taskType)
    ).pipe(
      takeUntil(this.destroy$)
    ).subscribe({
      next: (_data) => {

      },
      error: (_error) => {
        this.fallbackToPolling(projectId, jobId, taskType);
      }
    });

    this.updatePollingStatus(true);
  }

  private fallbackToPolling(_projectId: string, _jobId: string, _taskType: string): void {
    this.useSSE = false;

  }

  private clearAllTypewriterTimeouts(): void {
    Object.values(this.typewriterTimeouts).forEach(timeout => clearTimeout(timeout));
    this.typewriterTimeouts = {};
  }

  private startArtifactTypewriter(artifactName: string, content: string): void {

    if (this.typewriterTimeouts[artifactName]) {
      clearTimeout(this.typewriterTimeouts[artifactName]);
    }

    this.artifactTypewriterStates[artifactName] = {
      visibleContent: '',
      isTyping: true,
      fullContent: content,
    };

    this.typeArtifactCharacter(artifactName, 0);
  }

  private typeArtifactCharacter(artifactName: string, charIndex: number): void {
    const state = this.artifactTypewriterStates[artifactName];
    if (!state || !state.isTyping) {
      return;
    }

    const fullContent = state.fullContent;
    if (charIndex < fullContent.length) {
      const nextChar = fullContent.charAt(charIndex);
      state.visibleContent = fullContent.substring(0, charIndex + 1);

      let delay = this.artifactTypingSpeed;
      if (['.', '!', '?'].includes(nextChar)) {
        delay = this.artifactTypingSpeed * 1.3;
      } else if ([',', ';', ':'].includes(nextChar)) {
        delay = this.artifactTypingSpeed * 1.1;
      } else if (nextChar === ' ') {
        delay = this.artifactTypingSpeed * 0.7;
      } else if (nextChar === '\n') {
        delay = this.artifactTypingSpeed * 1.5;
      }

      this.cdr.detectChanges();

      this.typewriterTimeouts[artifactName] = setTimeout(() => {
        this.typeArtifactCharacter(artifactName, charIndex + 1);
      }, delay);
    } else {

      state.isTyping = false;
      this.cdr.detectChanges();
    }
  }

  getArtifactVisibleContent(artifactName: string): string {
    const state = this.artifactTypewriterStates[artifactName];
    if (state && state.isTyping) {
      return state.visibleContent;
    }

    return this.selectedArtifactFile?.content || '';
  }


  private updatePollingStatus(isPolling: boolean): void {
    // Skip polling status updates in project loading mode - no polling needed
    if (this.isProjectLoadingMode$.value) {
      return;
    }

    this.isPolling = isPolling;
    if (isPolling) {

      if (this.currentView$.value === 'loading') {
        this.isLoading$.next(true);
      }

      if (this.isCodeGenerationLoading$.value) {

      }

      if (this.selectedImageDataUri) {
        this.selectedImageDataUri = '';
      }
    } else {

      if (this.isCodeGenerationLoading$.value) {
        this.codeRegenerationProgressDescription = '';
      }
    }
  }

  startResize(event: MouseEvent) {

    event.preventDefault();

    this.isResizing$.next(true);

    const leftPanel = document.querySelector('.awe-leftpanel') as HTMLElement;
    const rightPanel = document.querySelector('.awe-rightpanel') as HTMLElement;
    const resizer = document.querySelector('.resizer') as HTMLElement;
    const container = document.querySelector('.awe-splitscreen') as HTMLElement;
    const splitScreen = document.querySelector('.smooth-split-screen') as HTMLElement;

    if (!leftPanel || !rightPanel || !container || !resizer) {
      return;
    }

    const overlay = document.createElement('div');
    overlay.id = 'resize-overlay';
    overlay.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      z-index: 9999;
      cursor: col-resize;
      background: transparent;
      pointer-events: auto;
    `;
    document.body.appendChild(overlay);

    resizer.classList.add('active');

    leftPanel.classList.add('dragging');
    rightPanel.classList.add('dragging');

    if (splitScreen) {
      splitScreen.classList.add('resizing');
    }

    document.body.classList.add('user-select-none');

    document.documentElement.classList.add('resizing-active');

    const initialX = event.clientX;
    const containerWidth = container.offsetWidth;
    const initialLeftWidth = leftPanel.offsetWidth;

    const minWidth = parseInt(this.minWidth$.value || '300', 10);
    const maxLeftWidth = containerWidth - minWidth;
    const minLeftWidth = minWidth;

    let lastX = initialX;
    let velocity = 0;
    let lastUpdateTime = Date.now();

    let animationFrameId: number | null = null;

    const handleMouseMove = (e: MouseEvent) => {

      e.preventDefault();
      e.stopPropagation();

      if (animationFrameId !== null) {
        cancelAnimationFrame(animationFrameId);
      }

      animationFrameId = requestAnimationFrame(() => {

        const dx = e.clientX - initialX;

        let newLeftWidth = initialLeftWidth + dx;

        newLeftWidth = Math.max(minLeftWidth, Math.min(maxLeftWidth, newLeftWidth));

        const leftPercentage = ((newLeftWidth / containerWidth) * 100).toFixed(2) + '%';
        const rightPercentage =
          (((containerWidth - newLeftWidth) / containerWidth) * 100).toFixed(2) + '%';

        leftPanel.style.width = leftPercentage;
        rightPanel.style.width = rightPercentage;

        this.defaultLeftPanelWidth = leftPercentage;
        this.defaultRightPanelWidth = rightPercentage;

        const now = Date.now();
        const elapsed = now - lastUpdateTime;
        if (elapsed > 0) {
          velocity = (e.clientX - lastX) / elapsed;
          lastX = e.clientX;
          lastUpdateTime = now;
        }

        this.ngZone.run(() => {
          this.cdr.detectChanges();
        });

        animationFrameId = null;
      });
    };

    const handleMouseUp = () => {

      if (animationFrameId !== null) {
        cancelAnimationFrame(animationFrameId);
        animationFrameId = null;
      }

      const overlay = document.getElementById('resize-overlay');
      if (overlay) {
        overlay.removeEventListener('mousemove', handleMouseMove);
        overlay.removeEventListener('mouseup', handleMouseUp);
        overlay.removeEventListener('touchmove', handleTouchMove);
        overlay.removeEventListener('touchend', handleTouchEnd);
        document.body.removeChild(overlay);
      }

      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      document.removeEventListener('touchmove', handleTouchMove, {
        passive: false,
      } as EventListenerOptions);
      document.removeEventListener('touchend', handleTouchEnd);

      if (Math.abs(velocity) > 0.5) {
        const momentum = velocity * 10;
        let newLeftWidth = leftPanel.offsetWidth + momentum;

        newLeftWidth = Math.max(minLeftWidth, Math.min(maxLeftWidth, newLeftWidth));

        const leftPercentage = ((newLeftWidth / containerWidth) * 100).toFixed(2) + '%';
        const rightPercentage =
          (((containerWidth - newLeftWidth) / containerWidth) * 100).toFixed(2) + '%';

        leftPanel.classList.remove('dragging');
        rightPanel.classList.remove('dragging');

        leftPanel.style.width = leftPercentage;
        rightPanel.style.width = rightPercentage;

        this.defaultLeftPanelWidth = leftPercentage;
        this.defaultRightPanelWidth = rightPercentage;
      } else {

        leftPanel.classList.remove('dragging');
        rightPanel.classList.remove('dragging');
      }

      this.isResizing$.next(false);

      resizer.classList.remove('active');

      if (splitScreen) {
        splitScreen.classList.remove('resizing');
      }

      document.body.classList.remove('user-select-none');

      document.documentElement.classList.remove('resizing-active');

      this.ngZone.run(() => {
        this.cdr.detectChanges();
      });
    };

    const handleTouchMove = (e: TouchEvent) => {

      e.preventDefault();

      if (e.touches.length > 0) {
        const touchEvent = e.touches[0];

        const mouseEvent = {
          clientX: touchEvent.clientX,
          preventDefault: () => e.preventDefault(),
        } as MouseEvent;

        handleMouseMove(mouseEvent);
      }
    };

    const handleTouchEnd = () => {
      handleMouseUp();
    };

    overlay.addEventListener('mousemove', handleMouseMove, { passive: false });
    overlay.addEventListener('mouseup', handleMouseUp, { passive: true });
    overlay.addEventListener('touchmove', handleTouchMove, {
      passive: false,
    } as EventListenerOptions);
    overlay.addEventListener('touchend', handleTouchEnd, { passive: true });

    document.addEventListener('mousemove', handleMouseMove, { passive: false });
    document.addEventListener('mouseup', handleMouseUp, { passive: true });
    document.addEventListener('touchmove', handleTouchMove, {
      passive: false,
    } as EventListenerOptions);
    document.addEventListener('touchend', handleTouchEnd, { passive: true });
  }
  onPanelToggled(isCollapsed: boolean) {
    this.isPanelCollapsed$.next(isCollapsed);
  }


  toggleHistoryView(): void {
    this.isHistoryActive$.next(!this.isHistoryActive$.value);
  }

  toggleCodeView(): void {

    if (!this.isCodeGenerationComplete) {
      return;
    }

    if (this.previewError$.value) {
      return;
    }

    if (this.codeViewer && this.codeViewer.saveCurrentContent) {
      this.codeViewer.saveCurrentContent();
    }

    this.userSelectedTab = true;

    this.ngZone.run(() => {

      this.isCodeActive$.next(true);
      this.isPreviewActive$.next(false);

      this.isArtifactsActive$.next(false);

      this.currentView$.next('editor');

      this.isLoading$.next(false);
      this.previewIcon$.next('bi-code-slash');

      this.cdr.markForCheck();
    });
  }
  togglePreviewView(): void {

    if (this.codeViewer && this.codeViewer.saveCurrentContent) {
      this.codeViewer.saveCurrentContent();
    }

    this.userSelectedTab = true;

    this.ngZone.run(() => {

      this.isCodeActive$.next(false);
      this.isPreviewActive$.next(true);

      this.isArtifactsActive$.next(false);

      this.currentView$.next('preview');

      if (!this.hasNewPollingResponseUrl && this.deployedUrl$.value) {
        this.urlSafe = this.sanitizer.bypassSecurityTrustResourceUrl(this.deployedUrl$.value);
      }

      if (this.isCodeGenerationComplete && !this.previewError$.value) {
        this.isPreviewLoading$.next(false);
      }

      this.cdr.markForCheck();
    });

    if (this.currentProgressState &&
        (this.currentProgressState.includes('LAYOUT_ANALYZED') ||
         this.currentProgressState.includes('PAGES_GENERATED')) &&
        this.layoutData && this.layoutData.length > 0) {
      this.isPreviewLoading$.next(false);
      this.isLayoutLoading = false;
    } else if (!this.isCodeGenerationComplete) {
      this.isPreviewLoading$.next(true);
    }

    this.previewIcon$.next('bi-eye');
    this.isPreviewTabEnabled = true;
  }

  toggleLogsView(): void {

    this.toggleArtifactsView();

  }

  toggleArtifactsView(): void {

    if (!this.isArtifactsTabEnabledWithLogs) {
      this.toastService.info('Artefacts and logs will be available when generated during the workflow');
      return;
    }

    if (this.codeViewer && this.codeViewer.saveCurrentContent) {
      this.codeViewer.saveCurrentContent();
    }

    this.userSelectedTab = true;

    this.isCodeActive$.next(false);
    this.isPreviewActive$.next(false);

    this.isArtifactsActive$.next(true);

    this.currentView$.next('artifacts');

    this.ensureArtifactPersistence();

    this.ensureLogsFileAtBottom();

    if (!this.selectedArtifactFile && this.artifactsData.length > 0) {
      this.selectArtifactFile(this.artifactsData[0]);
    }

    this.cdr.detectChanges();
  }

  toggleOverviewView(): void {

    if (this.codeViewer && this.codeViewer.saveCurrentContent) {
      this.codeViewer.saveCurrentContent();
    }

    this.userSelectedTab = true;

    this.isCodeActive$.next(false);
    this.isPreviewActive$.next(false);

    this.isArtifactsActive$.next(false);

    this.currentView$.next('overview');

    this.cdr.detectChanges();
  }

  toggleCodeViewEnhanced(): void {

    if (!this.isCodeGenerationComplete || this.previewError$.value) {
      this.completeTabTransition();
      return;
    }

    if (this.codeViewer && this.codeViewer.saveCurrentContent) {
      this.codeViewer.saveCurrentContent();
    }

    this.ngZone.run(() => {

      this.isCodeActive$.next(true);
      this.isPreviewActive$.next(false);
      this.isArtifactsActive$.next(false);

      this.currentView$.next('editor');

      this.isLoading$.next(false);
      this.previewIcon$.next('bi-code-slash');

      this.completeTabTransition();
    });
  }

  togglePreviewViewEnhanced(): void {

    if (this.codeViewer && this.codeViewer.saveCurrentContent) {
      this.codeViewer.saveCurrentContent();
    }

    this.ngZone.run(() => {

      this.isCodeActive$.next(false);
      this.isPreviewActive$.next(true);
      this.isArtifactsActive$.next(false);

      this.currentView$.next('preview');

      if (!this.hasNewPollingResponseUrl && this.deployedUrl$.value) {
        this.urlSafe = this.sanitizer.bypassSecurityTrustResourceUrl(this.deployedUrl$.value);
      }

      if (this.isCodeGenerationComplete && !this.previewError$.value) {
        this.isPreviewLoading$.next(false);
      }

      this.completeTabTransition();
    });
  }

  toggleArtifactsViewEnhanced(): void {

    if (this.codeViewer && this.codeViewer.saveCurrentContent) {
      this.codeViewer.saveCurrentContent();
    }

    this.isCodeActive$.next(false);
    this.isPreviewActive$.next(false);
    this.isArtifactsActive$.next(true);

    this.currentView$.next('artifacts');

    this.ensureArtifactPersistence();

    if (!this.selectedArtifactFile && this.artifactsData.length > 0) {
      this.selectArtifactFile(this.artifactsData[0]);
    }

    this.completeTabTransition();
  }

  toggleOverviewViewEnhanced(): void {

    if (this.codeViewer && this.codeViewer.saveCurrentContent) {
      this.codeViewer.saveCurrentContent();
    }

    this.isCodeActive$.next(false);
    this.isPreviewActive$.next(false);
    this.isArtifactsActive$.next(false);

    this.currentView$.next('overview');

    this.completeTabTransition();
  }

  toggleLogsViewEnhanced(): void {

    this.toggleArtifactsViewEnhanced();

  }

  onUIDesignPageChange(pageIndex: number): void {
    this.currentUIDesignPageIndex$.next(pageIndex);
    // Sync web page index
    this.currentWebPageIndex$.next(pageIndex);
  }

  onUIDesignFullscreenRequest(page: MobilePage): void {
    this.createMobileFullscreenOverlay(page);
  }

  private createMobileFullscreenOverlay(page: MobilePage): void {

    const originalBodyOverflow = document.body.style.overflow;
    const originalBodyPosition = document.body.style.position;
    const originalBodyTop = document.body.style.top;
    const originalBodyWidth = document.body.style.width;
    const scrollY = window.scrollY;

    // Get current theme
    const currentTheme = this.themeService.getCurrentTheme();
    const isDarkTheme = currentTheme === 'dark';

    const overlay = document.createElement('div');
    overlay.className = 'mobile-fullscreen-overlay';
    overlay.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: ${isDarkTheme ? 'rgba(0, 0, 0, 0.7)' : 'rgba(0, 0, 0, 0.5)'};
      backdrop-filter: blur(8px);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 9999;
      animation: fadeIn 0.3s ease-out;
    `;

    const content = document.createElement('div');
    content.className = 'overlay-content';

    const isMobile = window.innerWidth <= 768;
    const contentWidth = isMobile ? '95vw' : '90vw';
    const contentHeight = isMobile ? '95vh' : '90vh';
    const maxWidth = isMobile ? 'none' : '420px';
    const maxHeight = isMobile ? 'none' : '720px';
    const borderRadius = isMobile ? '8px' : '12px';

    content.style.cssText = `
      position: relative;
      width: ${contentWidth};
      height: ${contentHeight};
      max-width: ${maxWidth};
      max-height: ${maxHeight};
      background: ${isDarkTheme ? '#1e1e1e' : 'white'};
      border-radius: ${borderRadius};
      overflow: hidden;
      animation: scaleIn 0.3s ease-out;
      box-shadow:
        0 0 0 1px ${isDarkTheme ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'},
        0 4px 8px rgba(0, 0, 0, 0.1),
        0 8px 16px rgba(0, 0, 0, 0.15),
        0 16px 32px rgba(0, 0, 0, 0.2);
    `;

    const closeButton = document.createElement('button');
    closeButton.innerHTML = `
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
        <path d="M18 6L6 18M6 6l12 12" stroke="${isDarkTheme ? 'white' : 'currentColor'}" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    `;
    closeButton.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      width: 48px;
      height: 48px;
      background: ${isDarkTheme ? 'rgba(30, 30, 30, 0.8)' : 'rgba(0, 0, 0, 0.8)'};
      border: 2px solid ${isDarkTheme ? 'rgba(255, 255, 255, 0.2)' : 'rgba(255, 255, 255, 0.2)'};
      border-radius: 50%;
      color: white;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 10001;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      backdrop-filter: blur(12px);
      box-shadow:
        0 4px 12px rgba(0, 0, 0, 0.3),
        0 2px 6px rgba(0, 0, 0, 0.2);
      font-size: 0;
    `;

    closeButton.addEventListener('mouseenter', () => {
      closeButton.style.background = 'rgba(220, 38, 38, 0.9)';
      closeButton.style.borderColor = 'rgba(255, 255, 255, 0.4)';
      closeButton.style.transform = 'scale(1.1)';
      closeButton.style.boxShadow = `
        0 6px 20px rgba(220, 38, 38, 0.4),
        0 4px 12px rgba(0, 0, 0, 0.3)
      `;
    });

    closeButton.addEventListener('mouseleave', () => {
      closeButton.style.background = isDarkTheme ? 'rgba(30, 30, 30, 0.8)' : 'rgba(0, 0, 0, 0.8)';
      closeButton.style.borderColor = 'rgba(255, 255, 255, 0.2)';
      closeButton.style.transform = 'scale(1)';
      closeButton.style.boxShadow = `
        0 4px 12px rgba(0, 0, 0, 0.3),
        0 2px 6px rgba(0, 0, 0, 0.2)
      `;
    });

    const iframe = document.createElement('iframe');
    // Process content using the same logic as SafeSrcdocDirective for consistency
    const processedContent = this.processIframeContent(page.content);
    iframe.srcdoc = processedContent;
    iframe.style.cssText = `
      width: 100%;
      height: 100%;
      border: none;
      background: ${isDarkTheme ? '#1e1e1e' : 'white'};
      border-radius: 12px;
    `;
    iframe.setAttribute(
      'sandbox',
      'allow-same-origin allow-scripts allow-top-navigation-by-user-activation'
    );

    const style = document.createElement('style');
    style.textContent = `
      @keyframes fadeIn {
        from { opacity: 0; visibility: hidden; }
        to { opacity: 1; visibility: visible; }
      }
      @keyframes scaleIn {
        from { transform: scale(0.9); opacity: 0; }
        to { transform: scale(1); opacity: 1; }
      }


      @media (max-width: 768px) {
        .mobile-overlay-close-btn {
          top: 15px !important;
          right: 15px !important;
          width: 44px !important;
          height: 44px !important;
        }
      }

      @media (max-width: 480px) {
        .mobile-overlay-close-btn {
          top: 10px !important;
          right: 10px !important;
          width: 40px !important;
          height: 40px !important;
        }
      }
    `;
    document.head.appendChild(style);

    closeButton.classList.add('mobile-overlay-close-btn');

    const closeOverlay = () => {
      document.body.style.position = originalBodyPosition;
      document.body.style.top = originalBodyTop;
      document.body.style.width = originalBodyWidth;
      document.body.style.overflow = originalBodyOverflow;

      window.scrollTo(0, scrollY);

      if (document.body.contains(overlay)) {
        document.body.removeChild(overlay);
      }
      if (document.body.contains(closeButton)) {
        document.body.removeChild(closeButton);
      }
      if (document.head.contains(style)) {
        document.head.removeChild(style);
      }
    };

    closeButton.addEventListener('click', closeOverlay);
    overlay.addEventListener('click', e => {
      if (e.target === overlay) {
        closeOverlay();
      }
    });

    const escapeListener = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        closeOverlay();
        document.removeEventListener('keydown', escapeListener);
      }
    };
    document.addEventListener('keydown', escapeListener);

    content.addEventListener('click', e => {
      e.stopPropagation();
    });

    content.appendChild(iframe);
    overlay.appendChild(content);

    document.body.appendChild(closeButton);

    document.body.style.position = 'fixed';
    document.body.style.top = `-${scrollY}px`;
    document.body.style.width = '100%';
    document.body.style.overflow = 'hidden';

    document.body.appendChild(overlay);
  }

  // Helper methods for viewport switching and page conversion
  onWebPageChange(pageIndex: number): void {
    this.currentWebPageIndex$.next(pageIndex);
    // Sync mobile page index
    this.currentUIDesignPageIndex$.next(pageIndex);
  }

  onWebFullscreenRequest(page: WebPage): void {
    this.createWebFullscreenOverlay(page);
  }

  private convertMobilePagesToWebPages(mobilePages: MobilePage[]): WebPage[] {
    return mobilePages.map(mobilePage => ({
      fileName: mobilePage.fileName,
      content: mobilePage.content
    }));
  }

  private convertWebPagesToMobilePages(webPages: WebPage[]): MobilePage[] {
    return webPages.map(webPage => ({
      fileName: webPage.fileName,
      content: webPage.content
    }));
  }

  /**
   * Process iframe content using the same logic as SafeSrcdocDirective
   * This ensures consistent rendering between overview fullscreen and double-click preview
   */
  private processIframeContent(content: string): string {
    if (!content) return '';

    // Decode HTML entities to prevent double encoding
    const decodedContent = this.decodeHtmlEntities(content);

    // Ensure proper HTML styling
    const styledContent = this.ensureProperHtmlStyling(decodedContent);

    // Fix link targets to prevent iframe navigation
    const finalContent = this.fixLinkTargets(styledContent);

    return finalContent;
  }

  /**
   * Decode HTML entities in content
   */
  private decodeHtmlEntities(content: string): string {
    if (!content) return '';

    // Create a temporary DOM element to decode HTML entities
    const textarea = document.createElement('textarea');
    textarea.innerHTML = content;
    let decodedContent = textarea.value;

    // Additional manual decoding for common entities that might not be handled
    decodedContent = decodedContent
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .replace(/&#39;/g, "'")
      .replace(/&#x27;/g, "'")
      .replace(/&#x2F;/g, '/')
      .replace(/&#10;/g, '\n')
      .replace(/&nbsp;/g, ' ');

    return decodedContent;
  }

  /**
   * Ensure proper HTML styling for iframe content
   */
  private ensureProperHtmlStyling(content: string): string {
    if (!content) return '';

    // Check if content already has a complete HTML structure
    if (content.includes('<!DOCTYPE html>') || content.includes('<html>')) {
      return content;
    }

    // Check if content has basic CSS reset
    const hasBasicStyling = content.includes('margin:') || content.includes('padding:') || content.includes('<style>');

    if (hasBasicStyling) {
      return content;
    }

    // Inject basic CSS reset and styling for proper rendering
    const basicCSS = `
      <style>
        * {
          box-sizing: border-box;
        }
        body {
          margin: 0;
          padding: 16px;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
          line-height: 1.5;
          color: #333;
          background: white;
        }
        h1, h2, h3, h4, h5, h6 {
          margin-top: 0;
          margin-bottom: 0.5em;
        }
        p {
          margin-top: 0;
          margin-bottom: 1em;
        }
        img {
          max-width: 100%;
          height: auto;
        }
        button {
          padding: 8px 16px;
          border: 1px solid #ddd;
          border-radius: 4px;
          background: white;
          cursor: pointer;
        }
        button:hover {
          background: #f5f5f5;
        }
      </style>
    `;

    // If content is just a fragment, wrap it in a basic HTML structure
    if (!content.includes('<body>')) {
      return `<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  ${basicCSS}
</head>
<body>
  ${content}
</body>
</html>`;
    }

    // If content has body but no head styling, inject CSS into head or body
    if (content.includes('<head>')) {
      return content.replace('<head>', `<head>${basicCSS}`);
    } else if (content.includes('<body>')) {
      return content.replace('<body>', `<body>${basicCSS}`);
    }

    return content;
  }

  /**
   * Fix link targets to prevent iframe navigation
   */
  private fixLinkTargets(content: string): string {
    if (!content) return '';

    try {
      // Parse the HTML content
      const parser = new DOMParser();
      const doc = parser.parseFromString(content, 'text/html');

      // Find all links and set them to open in new tab
      const links = doc.querySelectorAll('a[href]');
      links.forEach(link => {
        link.setAttribute('target', '_blank');
        link.setAttribute('rel', 'noopener noreferrer');
      });

      // Return the modified HTML
      return doc.documentElement.outerHTML;
    } catch (error) {
      // If parsing fails, return the original content
      return content;
    }
  }

  private createWebFullscreenOverlay(page: WebPage): void {
    const originalBodyOverflow = document.body.style.overflow;
    const originalBodyPosition = document.body.style.position;
    const originalBodyTop = document.body.style.top;
    const originalBodyWidth = document.body.style.width;
    const scrollY = window.scrollY;

    // Get current theme
    const currentTheme = this.themeService.getCurrentTheme();
    const isDarkTheme = currentTheme === 'dark';

    // Get all pages and current page index
    const allPages = this.webPages$.value;
    const currentPageIndex = allPages.findIndex(p => p.fileName === page.fileName);

    const overlay = document.createElement('div');
    overlay.className = 'web-fullscreen-overlay';
    overlay.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: ${isDarkTheme ? 'rgba(0, 0, 0, 0.7)' : 'rgba(0, 0, 0, 0.5)'};
      backdrop-filter: blur(8px);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 10000;
      animation: fadeIn 0.3s ease-out;
    `;

    const content = document.createElement('div');
    content.style.cssText = `
      width: 90%;
      height: 90%;
      max-width: 1400px;
      max-height: 900px;
      background: ${isDarkTheme ? '#1e1e1e' : 'white'};
      border-radius: 12px;
      box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
      position: relative;
      overflow: hidden;
      animation: slideIn 0.3s ease-out;
    `;



    // Create navigation arrows
    const leftArrow = document.createElement('button');
    leftArrow.innerHTML = `
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
        <path d="M15 18l-6-6 6-6" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    `;
    leftArrow.style.cssText = `
      position: fixed;
      left: 20px;
      top: 50%;
      transform: translateY(-50%);
      width: 50px;
      height: 50px;
      background: ${isDarkTheme ? 'rgba(30, 30, 30, 0.8)' : 'rgba(0, 0, 0, 0.8)'};
      border: 2px solid rgba(255, 255, 255, 0.2);
      border-radius: 50%;
      color: white;
      cursor: pointer;
      display: ${currentPageIndex > 0 ? 'flex' : 'none'};
      align-items: center;
      justify-content: center;
      z-index: 10001;
      transition: all 0.3s ease;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
      font-size: 0;
    `;

    const rightArrow = document.createElement('button');
    rightArrow.innerHTML = `
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
        <path d="M9 18l6-6-6-6" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    `;
    rightArrow.style.cssText = `
      position: fixed;
      right: 80px;
      top: 50%;
      transform: translateY(-50%);
      width: 50px;
      height: 50px;
      background: ${isDarkTheme ? 'rgba(30, 30, 30, 0.8)' : 'rgba(0, 0, 0, 0.8)'};
      border: 2px solid rgba(255, 255, 255, 0.2);
      border-radius: 50%;
      color: white;
      cursor: pointer;
      display: ${currentPageIndex < allPages.length - 1 ? 'flex' : 'none'};
      align-items: center;
      justify-content: center;
      z-index: 10001;
      transition: all 0.3s ease;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
      font-size: 0;
    `;

    const closeButton = document.createElement('button');
    closeButton.innerHTML = `
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
        <path d="M18 6L6 18M6 6l12 12" stroke="${isDarkTheme ? 'white' : 'currentColor'}" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    `;
    closeButton.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      width: 50px;
      height: 50px;
      border: 2px solid ${isDarkTheme ? 'rgba(255, 255, 255, 0.2)' : 'rgba(255, 255, 255, 0.2)'};
      border-radius: 50%;
      background: ${isDarkTheme ? 'rgba(30, 30, 30, 0.8)' : 'rgba(0, 0, 0, 0.8)'};
      color: white;
      font-size: 0;
      cursor: pointer;
      z-index: 10001;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    `;

    const cleanup = () => {
      document.body.style.overflow = originalBodyOverflow;
      document.body.style.position = originalBodyPosition;
      document.body.style.top = originalBodyTop;
      document.body.style.width = originalBodyWidth;
      window.scrollTo(0, scrollY);
      overlay.remove();
      closeButton.remove();
      leftArrow.remove();
      rightArrow.remove();
      document.removeEventListener('keydown', escapeListener);
    };

    // Navigation handlers
    const navigateToPrevious = () => {
      if (currentPageIndex > 0) {
        cleanup();
        const prevPage = allPages[currentPageIndex - 1];
        this.createWebFullscreenOverlay(prevPage);
        this.currentWebPageIndex$.next(currentPageIndex - 1);
        this.currentUIDesignPageIndex$.next(currentPageIndex - 1);
      }
    };

    const navigateToNext = () => {
      if (currentPageIndex < allPages.length - 1) {
        cleanup();
        const nextPage = allPages[currentPageIndex + 1];
        this.createWebFullscreenOverlay(nextPage);
        this.currentWebPageIndex$.next(currentPageIndex + 1);
        this.currentUIDesignPageIndex$.next(currentPageIndex + 1);
      }
    };

    leftArrow.addEventListener('click', navigateToPrevious);
    rightArrow.addEventListener('click', navigateToNext);

    closeButton.addEventListener('click', cleanup);
    overlay.addEventListener('click', cleanup);

    const escapeListener = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        cleanup();
        document.removeEventListener('keydown', escapeListener);
      } else if (e.key === 'ArrowLeft') {
        navigateToPrevious();
      } else if (e.key === 'ArrowRight') {
        navigateToNext();
      }
    };
    document.addEventListener('keydown', escapeListener);

    content.addEventListener('click', e => {
      e.stopPropagation();
    });

    const iframe = document.createElement('iframe');
    // Process content using the same logic as SafeSrcdocDirective for consistency
    const processedContent = this.processIframeContent(page.content);
    iframe.srcdoc = processedContent;
    iframe.style.cssText = `
      width: 100%;
      height: 100%;
      border: none;
      background: ${isDarkTheme ? '#1e1e1e' : 'white'};
      border-radius: 12px;
    `;
    iframe.setAttribute(
      'sandbox',
      'allow-same-origin allow-scripts allow-top-navigation-by-user-activation'
    );

    const style = document.createElement('style');
    style.textContent = `
      @keyframes fadeIn {
        from { opacity: 0; visibility: hidden; }
        to { opacity: 1; visibility: visible; }
      }
      @keyframes slideIn {
        from { transform: scale(0.9) translateY(20px); opacity: 0; }
        to { transform: scale(1) translateY(0); opacity: 1; }
      }
    `;
    document.head.appendChild(style);

    closeButton.addEventListener('mouseenter', () => {
      closeButton.style.background = 'rgba(220, 38, 38, 0.9)';
      closeButton.style.borderColor = 'rgba(255, 255, 255, 0.4)';
      closeButton.style.transform = 'scale(1.1)';
    });

    closeButton.addEventListener('mouseleave', () => {
      closeButton.style.background = isDarkTheme ? 'rgba(30, 30, 30, 0.8)' : 'rgba(0, 0, 0, 0.8)';
      closeButton.style.borderColor = 'rgba(255, 255, 255, 0.2)';
      closeButton.style.transform = 'scale(1)';
    });

    content.appendChild(iframe);
    overlay.appendChild(content);
    document.body.appendChild(closeButton);
    document.body.appendChild(leftArrow);
    document.body.appendChild(rightArrow);

    document.body.style.position = 'fixed';
    document.body.style.top = `-${scrollY}px`;
    document.body.style.width = '100%';
    document.body.style.overflow = 'hidden';

    document.body.appendChild(overlay);
  }

  private ensureArtifactPersistence(): void {

    this.loadedArtifacts.forEach(artifactName => {
      const exists = this.artifactsData.some(item => item.name === artifactName);
      if (!exists) {
        this.restoreArtifact(artifactName);
      }
    });

    if (
      this.hasLayoutAnalyzed &&
      !this.artifactsData.some(item => item.name === 'Layout Analyzed')
    ) {
      this.ensureLayoutAnalyzedFileExists();
    }

    if (this.hasDesignSystem && !this.artifactsData.some(item => item.name === 'Design System')) {
      this.captureDesignSystemMappedState();
    }
  }

  private restoreArtifact(artifactName: string): void {
    switch (artifactName) {
      case 'Project Overview':
        this.artifactsData.unshift({
          name: 'Project Overview',
          type: 'markdown',
          content: '# Project Overview\n\nProject overview content has been loaded.',
        });

        this.ensureLogsFileAtBottom();
        break;
      case 'Layout Analyzed':
        this.ensureLayoutAnalyzedFileExists();
        break;
      case 'Design System':
        break;
      default:
    }
  }

  selectArtifactFile(file: any): void {
    this.selectedArtifactFile = file;

    if (file && file.name === 'Design System' && file.type === 'component') {
      this.initializeDesignTokens();
    }

    if (file && file.name === 'Layout Analyzed' && file.type === 'image') {
    }

    if (file && file.name === 'Project Overview' && file.type === 'markdown' && file.content) {
      const existingState = this.artifactTypewriterStates[file.name];

      if (
        !existingState ||
        (!existingState.isTyping && existingState.fullContent !== file.content)
      ) {
        this.startArtifactTypewriter(file.name, file.content);
      }
    }

    this.cdr.detectChanges();
  }

  private initializeLayoutAnalyzedData(): void {

    this.layoutAnalyzedData = [];

    if (this.layoutData && this.layoutData.length > 0) {

      const validLayoutKeys = this.layoutData.filter(key => this.layoutMapping[key]);

      if (validLayoutKeys.length > 0) {

        this.layoutAnalyzedData = validLayoutKeys.map(key => ({
          key,
          name: this.layoutMapping[key] || 'Identified Layout',
          imageUrl: `assets/images/layout-${key}.png`,
        }));

        this.stopLayoutAnalyzing();

      } else {

        this.startLayoutAnalyzing();
      }
    } else {

      this.startLayoutAnalyzing();
    }

    if (this.hasLayoutAnalyzed) {

      this.ensureLayoutAnalyzedFileExists();
    }

    this.cdr.detectChanges();
  }


  private ensureLayoutAnalyzedFileExists(): void {

    let layoutImageUrl = 'assets/images/layout-HB.png';
    if (this.layoutData && this.layoutData.length > 0) {
      const layoutKey = this.layoutData[0];
      layoutImageUrl = `assets/images/layout-${layoutKey}.png`;
    } else if (this.layoutAnalyzedData && this.layoutAnalyzedData.length > 0) {
      layoutImageUrl = this.layoutAnalyzedData[0].imageUrl;
    }

    const layoutAnalyzedIndex = this.artifactsData.findIndex(
      file => file.name === 'Layout Analyzed'
    );

    if (layoutAnalyzedIndex === -1) {

      this.artifactsData.push({
        name: 'Layout Analyzed',
        type: 'image',
        content: layoutImageUrl,
      });

      this.ensureLogsFileAtBottom();

    } else {

      this.artifactsData[layoutAnalyzedIndex].content = layoutImageUrl;
    }
  }

  private captureLayoutAnalyzedState(): void {

    const existingLayoutArtifact = this.artifactsData.find(item => item.name === 'Layout Analyzed');
    if (existingLayoutArtifact) {
      return;
    }

    this.hasLayoutAnalyzed = true;

    this.initializeLayoutAnalyzedData();

    let layoutImageUrl = 'assets/images/layout-HB.png';
    if (this.layoutData && this.layoutData.length > 0) {
      const layoutKey = this.layoutData[0];
      layoutImageUrl = `assets/images/layout-${layoutKey}.png`;
    } else if (this.layoutAnalyzedData && this.layoutAnalyzedData.length > 0) {
      layoutImageUrl = this.layoutAnalyzedData[0].imageUrl;
    }

    const layoutAnalyzedIndex = this.artifactsData.findIndex(
      file => file.name === 'Layout Analyzed'
    );

    if (layoutAnalyzedIndex === -1) {

      this.artifactsData.push({
        name: 'Layout Analyzed',
        type: 'image',
        content: layoutImageUrl,
      });

      this.ensureLogsFileAtBottom();

    } else {

      this.artifactsData[layoutAnalyzedIndex].content = layoutImageUrl;
    }

    this.cdr.detectChanges();
  }

  private captureDesignSystemMappedState(): void {

    this.hasDesignSystem = true;

    const hasDesignSystemFile = this.artifactsData.some(file => file.name === 'Design System');
    if (!hasDesignSystemFile) {

      this.artifactsData.push({
        name: 'Design System',
        type: 'component',
        content: 'Design system information will be displayed here.',
      });

      this.ensureLogsFileAtBottom();

      this.initializeDesignTokens();

      if (this.artifactsData.length === 1) {
        this.selectArtifactFile(this.artifactsData[0]);
      }
    }

  }


  private initializeDesignTokens(): void {

    if (this.designSystemData && this.hasNewDesignTokenStructure(this.designSystemData)) {
      this.initializeFromNewStructure(this.designSystemData);
      return;
    }

    if (!this.designSystemData || this.isUsingDefaultTokens()) {
      this.startDesignTokenLoading();

      this.designTokens = [];
      return;
    }

    this.designTokens = [...ALL_DESIGN_TOKENS];

  }

  private hasNewDesignTokenStructure(data: any): boolean {
    try {
      return data?.colors && Array.isArray(data.colors) &&
             data.colors.length > 0 &&
             data.colors[0]?.id && data.colors[0]?.name && data.colors[0]?.value;
    } catch (error) {
      return false;
    }
  }

  private initializeFromNewStructure(data: any): void {
    try {
      const newTokens: DesignToken[] = [];

      if (data.colors && Array.isArray(data.colors)) {
        data.colors.forEach((colorToken: any) => {
          if (this.isValidArtifactDesignToken(colorToken)) {
            const mappedToken: DesignToken = {
              id: colorToken.id,
              name: colorToken.name,
              value: colorToken.value,
              type: 'color',
              category: colorToken.category || 'Colors',
              editable: colorToken.editable !== false
            };
            newTokens.push(mappedToken);
          }
        });
      }

      this.designTokens = newTokens;

      this.stopDesignTokenLoading();

    } catch (error) {

      this.startDesignTokenLoading();
      this.designTokens = [];
    }
  }

  private isValidArtifactDesignToken(token: any): boolean {
    return token &&
           typeof token.id === 'string' && token.id.trim() !== '' &&
           typeof token.name === 'string' && token.name.trim() !== '' &&
           typeof token.value === 'string' && token.value.trim() !== '' &&
           (typeof token.category === 'string' || token.category === undefined) &&
           (typeof token.editable === 'boolean' || token.editable === undefined);
  }

  generateColorName(hexColor: string): string {

    const hex = hexColor.startsWith('#') ? hexColor.substring(1) : hexColor;

    const colorMap: { [key: string]: string } = {
      '000000': 'Black',
      FFFFFF: 'White',
      FF0000: 'Red',
      '00FF00': 'Green',
      '0000FF': 'Blue',
      FFFF00: 'Yellow',
      '00FFFF': 'Cyan',
      FF00FF: 'Magenta',
      C0C0C0: 'Silver',
      '808080': 'Gray',
      '800000': 'Maroon',
      '808000': 'Olive',
      '008000': 'Dark Green',
      '800080': 'Purple',
      '008080': 'Teal',
      '000080': 'Navy',
      FFA500: 'Orange',
      A52A2A: 'Brown',
      FFC0CB: 'Pink',
      E48900: 'D_Yellow',
      FFA826: 'Lemon',
      '212121': 'Black',
      '4D4D4D': 'D_Grey',
      F5F7FA: 'Silver',
    };

    if (colorMap[hex.toUpperCase()]) {
      return colorMap[hex.toUpperCase()];
    }

    const r = parseInt(hex.substring(0, 2), 16);
    const g = parseInt(hex.substring(2, 4), 16);
    const b = parseInt(hex.substring(4, 6), 16);

    let hue = '';
    const max = Math.max(r, g, b);
    const min = Math.min(r, g, b);

    if (max === min) {

      const brightness = Math.round((r + g + b) / 3);
      if (brightness < 64) return 'Dark Gray';
      if (brightness < 128) return 'Gray';
      if (brightness < 192) return 'Light Gray';
      return 'Off White';
    }

    if (r === max) {
      if (g > b) hue = 'Orange';
      else hue = 'Red';
    } else if (g === max) {
      if (r > b) hue = 'Yellow Green';
      else hue = 'Green';
    } else {
      if (r > g) hue = 'Purple';
      else hue = 'Blue';
    }

    const brightness = (r + g + b) / 3;
    let prefix = '';

    if (brightness < 85) prefix = 'Dark ';
    else if (brightness > 170) prefix = 'Light ';

    return prefix + hue;
  }

  updateDesignToken(tokenId: string, newValue: string): void {

    if (!this.designTokenEditState$.value.isEditMode) {
      return;
    }

    const tokenIndex = this.designTokens.findIndex(token => token.id === tokenId);

    if (tokenIndex !== -1) {
      const token = this.designTokens[tokenIndex];
      const originalValue = this.designTokenEditState$.value.originalValues.get(tokenId);

      token.value = newValue;

      if (token.type === 'color') {
        token.name = this.generateColorName(newValue);
      }

      const hasChanged = originalValue !== newValue;
      const currentState = this.designTokenEditState$.value;
      this.designTokenEditState$.next({
        ...currentState,
        hasUnsavedChanges: hasChanged || currentState.hasUnsavedChanges
      });

    }
  }

  getDesignTokensForBackend(): any {
    const tokensByCategory = {
      colors: this.designTokens
        .filter(token => token.type === 'color')
        .map(token => ({
          id: token.id,
          name: token.name,
          value: token.value,
          hexCode: token.value,
          category: token.category,
          editable: token.editable,
        })),
      typography: this.designTokens
        .filter(token => token.type === 'typography')
        .map(token => ({
          id: token.id,
          name: token.name,
          value: token.value,
          category: token.category,
          editable: token.editable,
        })),
      buttons: this.designTokens
        .filter(token => token.category === 'Buttons')
        .map(token => ({
          id: token.id,
          name: token.name,
          variant: token.value,
          category: token.category,
          editable: token.editable,
        })),
    };

    return {
      designSystem: {
        tokens: tokensByCategory,
        timestamp: new Date().toISOString(),
        projectId: this.projectId,
        jobId: this.jobId,
      },
    };
  }

  private designTokensUpdateTimer: any;


  private sendTokensUpdateRequest(tokensPayload: any): void {

    const updateRequest = {
      project_id: this.projectId,
      job_id: this.jobId,
      action: 'update_design_tokens',
      design_tokens: tokensPayload.designSystem.tokens,
      timestamp: tokensPayload.designSystem.timestamp,
    };

    this.codeGenerationService.updateDesignTokens(updateRequest).subscribe({
      next: _response => {
        this.toastService.success('Design tokens updated');
      },
      error: _error => {
        this.toastService.error('Failed to update design tokens');
      },
    });
  }

  triggerDesignTokensUpdate(): void {

    if (this.designTokensUpdateTimer) {
      clearTimeout(this.designTokensUpdateTimer);
      this.designTokensUpdateTimer = null;
    }

    if (this.projectId && this.jobId) {
      const tokensPayload = this.getDesignTokensForBackend();
      this.sendTokensUpdateRequest(tokensPayload);
    } else {
    }
  }

  getCurrentDesignTokensState(): any {
    return {
      tokens: this.designTokens,
      hasDesignSystem: this.hasDesignSystem,
      projectId: this.projectId,
      jobId: this.jobId,
      backendPayload: this.getDesignTokensForBackend(),
    };
  }

  isValidHexColor(color: string): boolean {

    return validateHexColor(color);
  }

  onTokenValueChange(event: Event, tokenId: string): void {
    const inputElement = event.target as HTMLInputElement;
    if (inputElement && inputElement.value) {
      const value = inputElement.value;

      const token = this.designTokens.find(t => t.id === tokenId);
      if (token && token.type === 'color') {

        const isValid = this.isValidHexColor(value);

        if (isValid) {
          inputElement.classList.remove('invalid');

          const formattedValue = value.startsWith('#') ? value : `#${value}`;
          this.updateDesignToken(tokenId, formattedValue);
        } else {
          inputElement.classList.add('invalid');

          return;
        }
      } else {

        this.updateDesignToken(tokenId, value);
      }
    }
  }

  getTokensByCategory(category: string): DesignToken[] {

    return this.designTokens.filter(token => token.category === category);
  }

  toggleDesignTokenEditMode(): void {
    const currentState = this.designTokenEditState$.value;

    if (currentState.isEditMode) {

      this.saveDesignTokenChanges();
    } else {

      this.enterDesignTokenEditMode();
    }
  }

  private enterDesignTokenEditMode(): void {
    const originalValues = new Map<string, string>();

    this.designTokens.forEach(token => {
      if (token.category === 'Colors') {
        originalValues.set(token.id, token.value);
      }
    });

    this.designTokenEditState$.next({
      isEditMode: true,
      editButtonText: 'Save',
      hasUnsavedChanges: false,
      originalValues
    });

  }

  private saveDesignTokenChanges(): void {
    const currentState = this.designTokenEditState$.value;

    if (currentState.hasUnsavedChanges) {
    }

    this.designTokenEditState$.next({
      isEditMode: false,
      editButtonText: 'Edit',
      hasUnsavedChanges: false,
      originalValues: new Map()
    });

  }

  areDesignTokenInputsDisabled(): boolean {
    return !this.designTokenEditState$.value.isEditMode;
  }

  getDesignTokenEditButtonText(): string {
    return this.designTokenEditState$.value.editButtonText;
  }

  startDesignTokenLoading(): void {
    this.designTokenLoadingState$.next({
      isLoading: true,
      showAnalyzingAnimation: true,
      hasReceivedTokens: false,
      loadingMessage: 'Analyzing Design Tokens...'
    });

  }

  stopDesignTokenLoading(): void {
    this.designTokenLoadingState$.next({
      isLoading: false,
      showAnalyzingAnimation: false,
      hasReceivedTokens: true,
      loadingMessage: ''
    });

  }

  shouldShowDesignTokenLoadingAnimation(): boolean {
    const loadingState = this.designTokenLoadingState$.value;
    return loadingState.isLoading && loadingState.showAnalyzingAnimation && !loadingState.hasReceivedTokens;
  }

  hasActualDesignTokensFromPolling(): boolean {
    const loadingState = this.designTokenLoadingState$.value;

    return loadingState.hasReceivedTokens &&
           this.designTokens.length > 0 &&
           !this.isUsingDefaultTokens();
  }

  private isUsingDefaultTokens(): boolean {

    if (this.designTokens.length === 0) return true;

    const colorTokens = this.designTokens.filter(token => token.category === 'Colors');
    const defaultColorNames = ['D_Yellow', 'Lemon', 'Black', 'D_Grey', 'Silver'];

    return colorTokens.length === 5 &&
           colorTokens.every(token => defaultColorNames.includes(token.name));
  }

  getFileIconClass(fileType: string): string {
    switch (fileType) {
      case 'markdown':
        return 'file-icon-md';
      case 'image':
        return 'file-icon-img';
      case 'svg':
        return 'file-icon-svg';
      case 'text':
        return 'file-icon-txt';
      case 'component':
        return 'file-icon-component';
      case 'logs':
        return 'file-icon-logs';
      default:
        return 'file-icon-default';
    }
  }

  lastDisplayedLogIndex = 0;

  getLogClass(log: string | any): string {

    if (typeof log === 'object' && log !== null && log.type) {
      switch (log.type) {
        case 'error':
          return 'log-error';
        case 'warning':
          return 'log-warning';
        case 'debug':
          return 'log-debug';
        case 'code':
          return 'log-code';
        case 'progress-description':
          return 'log-info progress-description';
        case 'status-change':
          return 'log-info status-change';
        default:
          return 'log-info';
      }
    }

    if (typeof log === 'string') {
      if (log.includes('ERROR')) {
        return 'log-error';
      } else if (log.includes('WARN')) {
        return 'log-warning';
      } else if (log.includes('DEBUG')) {
        return 'log-debug';
      } else if (log.includes('Progress Description')) {

        return 'log-info progress-description';
      } else if (log.includes('Status changed to:')) {

        return 'log-info status-change';
      } else {
        return 'log-info';
      }
    }

    return 'log-info';
  }

  @Input() defaultLeftPanelWidth: string = '50%';
  @Input() defaultRightPanelWidth: string = '50%';

  toggleLeftPanel(): void {
    const leftPanel = document.querySelector('.awe-leftpanel') as HTMLElement;
    const rightPanel = document.querySelector('.awe-rightpanel') as HTMLElement;
    const splitScreen = document.querySelector('.awe-splitscreen') as HTMLElement;

    if (leftPanel && rightPanel && splitScreen) {
      if (this.isLeftPanelCollapsed$.value) {

        leftPanel.style.width = this.defaultLeftPanelWidth;
        rightPanel.style.width = this.defaultRightPanelWidth;
        this.isLeftPanelCollapsed$.next(false);

        splitScreen.classList.remove('left-panel-collapsed');
      } else {

        leftPanel.style.width = '0%';
        rightPanel.style.width = '100%';
        this.isLeftPanelCollapsed$.next(true);

        splitScreen.classList.add('left-panel-collapsed');
      }
    }
  }

  private baseRightIcons: { name: string; status: IconStatus }[] = [
    { name: 'awe_enhance', status: 'active' },
    { name: 'awe_enhanced_send', status: 'active' },
  ];

  private fileAttachIcon: { name: string; status: IconStatus } = {
    name: 'awe_enhanced_alternate',
    status: 'default',
  };
  get rightIcons(): { name: string; status: IconStatus }[] {
    const isUIDesignMode = this.isUIDesignMode$.value;
    if (isUIDesignMode) {
      return [...this.baseRightIcons];
    } else {
      return [this.fileAttachIcon, ...this.baseRightIcons];
    }
  }

  handleIconClick(event: { name: string; side: string; index: number; theme: string }): void {
    const normalizedIconName = event.name.toLowerCase();
    switch (normalizedIconName) {
      case 'awe_enhanced_alternate':
        this.handleEnhancedAlternate();
        break;
      case 'awe_enhance':
        this.handleEnhanceText();
        break;
      case 'awe_enhanced_send':
        if (event.theme === 'dark') {
          this.handleEnhancedSendDark();
        } else if (event.theme === 'light') {
          this.handleEnhancedSendLight();
        }
        break;
    }
  }

  handleEnhancedSendDark(): void {
    if (!this.darkPrompt.trim()) return;

    this.darkMessages.push({ text: this.darkPrompt, from: 'user', theme: 'dark' });

    setTimeout(() => {
      this.darkMessages.push({
        text: 'This is an AI-generated reply to your message (dark theme).',
        from: 'ai',
        theme: 'dark',
      });
    }, 100);

    this.darkPrompt = '';
  }

  isAiResponding: boolean = false;

  handleEnhancedSendLight(): void {
    if (!this.lightPrompt.trim()) return;
    if (this.isAiResponding) return;


    if (this.isCodeGenerationComplete) {
      this.handleCodeEditRequest();
      return;
    }

    const currentImageDataUri = this.selectedImageDataUri;

    this.isAiResponding = true;
    this.lightMessages.push({
      text: this.lightPrompt,
      from: 'user',
      theme: 'light',
      imageDataUri: currentImageDataUri || undefined,
    });

    if (currentImageDataUri) {
      this.selectedImageDataUri = '';
    }

    this.lightPrompt = '';

    setTimeout(() => {
      this.lightMessages.push({
        text: 'Analyzing your request...',
        from: 'ai',
        theme: 'light',
      });
    }, 500);

    this.appStateService.project$
      .subscribe(projectState => {

        let userSelectionsSummary = '';

        if (projectState.imageUrl) {
          userSelectionsSummary += '- **Image**: You provided an image for reference\n';
        }

        if (projectState.technology) {
          userSelectionsSummary += `- **Technology**: ${projectState.technology.charAt(0).toUpperCase() + projectState.technology.slice(1)}\n`;
        }

        if (projectState.designLibrary) {
          userSelectionsSummary += `- **Design Library**: ${projectState.designLibrary.charAt(0).toUpperCase() + projectState.designLibrary.slice(1)}\n`;
        }

        if (projectState.application) {
          userSelectionsSummary += `- **Application Type**: ${projectState.application.charAt(0).toUpperCase() + projectState.application.slice(1)}\n`;
        }

        if (projectState.type) {
          userSelectionsSummary += `- **Generation Type**: ${projectState.type}\n`;
        }

        setTimeout(() => {

          const lastMessage = this.lightMessages[this.lightMessages.length - 1];
          if (lastMessage && lastMessage.from === 'ai') {

          }
          this.isAiResponding = false;
        }, 2000);
      })
      .unsubscribe();
  }

  private handleCodeEditRequest(): void {
    const userRequest = this.lightPrompt.trim();
    const currentImageDataUri = this.selectedImageDataUri;

    this.lastUserRequest = userRequest;

    this.lightMessages.push({
      text: userRequest,
      from: 'user',
      theme: 'light',
      imageDataUri: currentImageDataUri || undefined,
    });

    if (currentImageDataUri) {
      this.selectedImageDataUri = '';
    }
    this.lightPrompt = '';

    this.isAiResponding = true;

    this.isCodeGenerationLoading$.next(true);

    this.subscribeToRegenerationProgress();

    this.startRegenerationPreviewLoading();

    const messageId = `ai-regeneration-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    this.lightMessages.push({
      id: messageId,
      text: '',
      from: 'ai',
      theme: 'light',
    });

    this.activeAIMessageIds.add(messageId);
    this.currentActiveMessageId = messageId;

    const currentCodeFiles = this.getCurrentCodeFiles();

    if (!currentCodeFiles || currentCodeFiles.length === 0) {
      this.handleEditError(
        'No code files found to edit. Please ensure code generation is complete.'
      );

      this.stopRegenerationPreviewLoading();
      return;
    }

    const images: string[] = currentImageDataUri ? [currentImageDataUri] : [];

    this.executeSequentialCodeRegenerationWithPayload(currentCodeFiles,userRequest,images);

  }

  private startRegenerationPreviewLoading(): void {

    this.isRegenerationInProgress$.next(true);
    this.regenerationStartTime = Date.now();

    this.urlSafe = undefined;

    this.isPreviewLoading$.next(true);
    this.previewIcon$.next('bi-arrow-clockwise');
    this.previewError$.next(false);

    this.isUrlValidated$.next(false);
    this.isUrlAvailable$.next(false);
    this.isIframeReady$.next(false);

    this.urlValidationError$.next('');

    this.isPreviewTabEnabled = false;

    this.setPreviewTabDisabled('Regeneration in progress...');

    this.setCodeTabDisabled('Code regeneration in progress...');

    if (this.currentView$.value !== 'editor') {
      this.currentView$.next('editor');
      this.isCodeActive$.next(true);
      this.isPreviewActive$.next(false);
      this.isArtifactsActive$.next(false);
    }

    this.cdr.detectChanges();

  }

  private stopRegenerationPreviewLoading(): void {

    this.isPreviewLoading$.next(false);
    this.previewIcon$.next('bi-eye');

    if (this.isRegenerationInProgress$.value) {
      this.isRegenerationInProgress$.next(false);
      this.regenerationStartTime = 0;
    }

    this.resetRegenerationCallFlag('Preview loading deactivation');

  }


  private handleRegenerationFailure(): void {

    const sessionKey = this.currentCheckpointSession();
    if (sessionKey) {
      this.regenerationCheckpointService.completeCheckpointSession(sessionKey, 'failed');
      this.currentCheckpointSession.set(null);

    }

    this.isRegenerationInProgress$.next(false);
    this.regenerationStartTime = 0;

    this.stopRegenerationPreviewLoading();

    const currentUrl = this.deployedUrl$.value;
    if (currentUrl && currentUrl !== 'ERROR_DEPLOYMENT_FAILED') {

      this.isPreviewTabEnabled = true;
      this.previewError$.next(false);

      this.ensureIframeLoadedAfterRegeneration();
    } else {

      this.showDeploymentErrorInPreview();
    }

    this.resetRegenerationCallFlag('Regeneration failure');

  }

  private completeDirectRegenerationResponse(): void {

    if (this.regenerationStartTime > 0) {

    }

    this.isRegenerationInProgress$.next(false);
    this.regenerationStartTime = 0;

    this.isCodeGenerationLoading$.next(false);
    this.codeRegenerationProgressDescription = '';

    this.stopRegenerationPreviewLoading();

    this.isPreviewTabEnabled = true;
    this.previewError$.next(false);

    let currentUrl = this.deployedUrl$.value;

    if (!currentUrl || currentUrl === 'ERROR_DEPLOYMENT_FAILED') {

      const codeServiceUrl = this.codeSharingService.getDeployedUrl();
      if (codeServiceUrl) {
        currentUrl = codeServiceUrl;
        this.deployedUrl$.next(currentUrl);
      }

      else if (this.newPreviewUrl) {
        currentUrl = this.newPreviewUrl;
        this.deployedUrl$.next(currentUrl);
      }
    }

    if (currentUrl && currentUrl !== 'ERROR_DEPLOYMENT_FAILED') {

      this.ensureIframeLoadedAfterRegeneration();
    } else {

      this.restoreIframeAfterRegenerationWithoutUrl();
    }

    this.cdr.detectChanges();

    this.resetRegenerationCallFlag('Direct response completion');

  }

  private ensureIframeLoadedAfterRegeneration(): void {
    const currentUrl = this.deployedUrl$.value;

    if (!currentUrl || currentUrl === 'ERROR_DEPLOYMENT_FAILED') {
      return;
    }

    this.urlValidationError$.next('');

    this.urlSafe = undefined;
    this.cdr.detectChanges();

    setTimeout(() => {

      if (this.isValidPreviewUrl(currentUrl)) {

        const refreshUrl = this.addCacheBustingToUrl(currentUrl);
        this.urlSafe = this.sanitizer.bypassSecurityTrustResourceUrl(refreshUrl);

        this.isUrlValidated$.next(true);
        this.isUrlAvailable$.next(true);
        this.isIframeReady$.next(true);

        this.isPreviewTabEnabled = true;
        this.isPreviewLoading$.next(false);
        this.previewIcon$.next('bi-eye');
        this.previewError$.next(false);

        if (this.currentView$.value !== 'preview') {
          this.currentView$.next('preview');
        }

        this.cdr.detectChanges();
      } else {
        this.showDeploymentErrorInPreview();
      }
    }, 100);
  }

  private restoreIframeAfterRegenerationWithoutUrl(): void {

    this.isPreviewLoading$.next(false);
    this.previewIcon$.next('bi-eye');
    this.previewError$.next(false);

    this.isUrlValidated$.next(true);
    this.isUrlAvailable$.next(true);
    this.isIframeReady$.next(true);

    this.urlValidationError$.next('');

    this.isPreviewTabEnabled = true;

    if (this.currentView$.value !== 'preview') {
      this.currentView$.next('preview');
    }

    if (!this.urlSafe) {
      this.urlSafe = this.sanitizer.bypassSecurityTrustResourceUrl('about:blank');
    }

    this.cdr.detectChanges();
  }

  private addCacheBustingToUrl(url: string): string {
    try {
      const urlObj = new URL(url);

      urlObj.searchParams.set('_refresh', Date.now().toString());
      const refreshUrl = urlObj.toString();
      return refreshUrl;
    } catch (error) {
      return url;
    }
  }

  private restoreIframeAfterRegenerationError(): void {

    this.isPreviewLoading$.next(false);
    this.previewIcon$.next('bi-eye');
    this.previewError$.next(false);

    const currentUrl = this.deployedUrl$.value || this.codeSharingService.getDeployedUrl() || this.newPreviewUrl;

    if (currentUrl && currentUrl !== 'ERROR_DEPLOYMENT_FAILED') {

      this.urlSafe = this.sanitizer.bypassSecurityTrustResourceUrl(currentUrl);

      this.isUrlValidated$.next(true);
      this.isUrlAvailable$.next(true);
      this.isIframeReady$.next(true);

    } else {

      this.urlSafe = this.sanitizer.bypassSecurityTrustResourceUrl('about:blank');
      this.isUrlValidated$.next(true);
      this.isUrlAvailable$.next(true);
      this.isIframeReady$.next(true);

    }

    this.urlValidationError$.next('');

    this.isPreviewTabEnabled = true;

    if (this.currentView$.value !== 'preview') {
      this.currentView$.next('preview');
    }

    this.cdr.detectChanges();
  }


  private getCurrentCodeFiles(): FileModel[] {
    try {

      let currentFiles: FileModel[] = [];

      const fileTreeFiles = this.fileTreePersistenceService.getCurrentFiles();
      if (fileTreeFiles && fileTreeFiles.length > 0) {
        currentFiles = fileTreeFiles;
      }

      else if (this.codeViewer && this.codeViewer.getAllCurrentFiles) {
        const codeViewerFiles = this.codeViewer.getAllCurrentFiles();
        if (codeViewerFiles && codeViewerFiles.length > 0) {
          currentFiles = codeViewerFiles;
        }
      }

      else if (this.codeViewer && this.codeViewer.files && this.codeViewer.files.length > 0) {
        currentFiles = this.codeViewer.files;
      }

      else if (this.files$.value && this.files$.value.length > 0) {
        currentFiles = this.files$.value;
      }

      else {
        const generatedCode = this.codeSharingService.getGeneratedCode();
        if (generatedCode) {
          currentFiles = this.convertGeneratedCodeToFileModels(generatedCode);
        } else {
          return [];
        }
      }

      const flattenedFiles = this.flattenFileTree(currentFiles);


      if (flattenedFiles.length === 0) {


        if (this.codeViewer) {
        } else {
        }

        const generatedCode = this.codeSharingService.getGeneratedCode();
        if (generatedCode) {
          if (Array.isArray(generatedCode)) {
          } else if (typeof generatedCode === 'object') {
          }
        }
      }

      return flattenedFiles;
    } catch (error) {
      return [];
    }
  }

  private flattenFileTree(files: FileModel[]): FileModel[] {
    const flatFiles: FileModel[] = [];

    const processFile = (file: FileModel, parentPath: string = '') => {
      if (file.type === 'file') {

        const fullPath = file.fileName || file.name || '';
        const finalPath =
          parentPath && !fullPath.startsWith(parentPath) ? `${parentPath}/${file.name}` : fullPath;

        flatFiles.push({
          ...file,
          name: finalPath,
          fileName: finalPath,
        });
      } else if (file.type === 'folder' && file.children) {

        const folderPath = parentPath ? `${parentPath}/${file.name}` : file.name;
        file.children.forEach(child => processFile(child, folderPath));
      }
    };

    files.forEach(file => processFile(file));
    return flatFiles;
  }

  private convertGeneratedCodeToFileModels(generatedCode: any): FileModel[] {
    const fileModels: FileModel[] = [];

    try {
      if (typeof generatedCode === 'string') {

        try {
          const parsedCode = JSON.parse(generatedCode);
          return this.convertGeneratedCodeToFileModels(parsedCode);
        } catch {

          fileModels.push({
            name: 'index.html',
            type: 'file',
            content: generatedCode,
            fileName: 'index.html',
          });
        }
      } else if (Array.isArray(generatedCode)) {

        for (const item of generatedCode) {
          if (typeof item === 'object' && item !== null) {
            const fileName = item.fileName || item.name || item.path || 'unknown.txt';
            const content = item.content || '';
            fileModels.push({
              name: fileName,
              type: 'file',
              content: typeof content === 'string' ? content : JSON.stringify(content, null, 2),
              fileName: fileName,
            });
          }
        }
      } else if (typeof generatedCode === 'object' && generatedCode !== null) {

        for (const [filePath, content] of Object.entries(generatedCode)) {
          fileModels.push({
            name: filePath,
            type: 'file',
            content: typeof content === 'string' ? content : JSON.stringify(content, null, 2),
            fileName: filePath,
          });
        }
      }
    } catch (error) {
    }

    return fileModels;
  }


  private handleEditError(errorMessage: string): void {

    if (this.isRegenerationInProgress$.value) {
      this.isRegenerationInProgress$.next(false);
      this.regenerationStartTime = 0;
      this.stopRegenerationPreviewLoading();

      this.restoreIframeAfterRegenerationError();
    }

    this.resetRegenerationCallFlag('Edit error handling');

    const lastMessage = this.lightMessages[this.lightMessages.length - 1];
    if (lastMessage && lastMessage.from === 'ai') {
      lastMessage.text = errorMessage;
    } else {

      this.lightMessages.push({
        text: errorMessage,
        from: 'ai',
        theme: 'light',
      });
    }

    this.isAiResponding = false;

    this.isCodeGenerationLoading$.next(false);
    this.codeRegenerationProgressDescription = '';

    this.cdr.detectChanges();

  }

  handleEnhancedAlternate(): void {
    const fileInput = document.createElement('input');
    fileInput.type = 'file';
    fileInput.style.display = 'none';

    fileInput.addEventListener('change', (event: Event) => {
      const input = event.target as HTMLInputElement;
      if (input.files?.length) {
        alert('File uploaded: ' + input.files[0].name);
      }
    });

    document.body.appendChild(fileInput);
    fileInput.click();
    document.body.removeChild(fileInput);
  }

  handleEnhanceText(): void {
    if (!this.lightPrompt.trim()) {
      return;
    }

    this.rightIcons[1].status = 'disable';


    this.lightMessages.push({
      text: 'Enhancing your prompt to provide more detailed instructions...',
      from: 'ai',
      theme: 'light',
    });

    this.promptService.enhancePrompt(this.lightPrompt, 'Generate Application').subscribe({
      next: (response: any) => {
        if (response && response.code && response.status_code === 200) {

          this.lightPrompt = response.code;

          this.lightMessages.push({
            text:
              "I've enhanced your prompt to provide more detailed instructions. Here's the enhanced version:\n\n" +
              response.code,
            from: 'ai',
            theme: 'light',
          });

        } else {

          this.lightMessages.push({
            text: "I couldn't enhance your prompt. Please try again with more details.",
            from: 'ai',
            theme: 'light',
          });
        }
      },
      error: (_error: any) => {

        this.lightMessages.push({
          text: 'I encountered an error while enhancing your prompt. Please try again.',
          from: 'ai',
          theme: 'light',
        });
      },
      complete: () => {
        this.rightIcons[1].status = 'active';
      },
    });
  }


  toggleExportModal(): void {

    this.isExperienceStudioModalOpen$.next(!this.isExperienceStudioModalOpen$.value);

    if (this.isExperienceStudioModalOpen$.value) {
    } else {
    }

  }

  copyToClipboard(inputElement: HTMLInputElement): void {

    try {
      inputElement.select();
      document.execCommand('copy');
      inputElement.setSelectionRange(0, 0);

      this.toastService.success('Link copied to clipboard');
    } catch (error) {
      this.toastService.error('Failed to copy link to clipboard');
    }
  }

  exportToVSCode(): void {

    this.isExperienceStudioModalOpen$.next(false);

    this.toastService.info('Preparing VSCode export...');

    try {

      const generatedCode = this.getCodeFromMultipleSources();

      if (!generatedCode) {
        this.toastService.error(
          'No code available to export. Please ensure code generation is complete.'
        );
        return;
      }

      const appName = this.generateAppNameForDownload();

      const exportFiles = this.prepareFilesForVSCodeExport(generatedCode);

      this.vscodeExportService
        .exportToVSCode({
          projectName: appName,
          files: exportFiles,
          openInVSCode: true,
          downloadFallback: true,
        })
        .subscribe({
          next: result => {
            this.handleVSCodeExportResult(result, appName);
          },
          error: _error => {
            this.toastService.error(
              'Failed to export to VSCode. Please try downloading the project instead.'
            );
          },
        });
    } catch (error) {
      this.toastService.error('Failed to prepare VSCode export. Please try again.');
    }
  }

  private prepareFilesForVSCodeExport(generatedCode: any): any[] {

    const exportFiles: any[] = [];

    try {
      if (typeof generatedCode === 'string') {

        try {
          const parsedCode = JSON.parse(generatedCode);
          this.processCodeForVSCodeExport(parsedCode, exportFiles);
        } catch {

          exportFiles.push({
            name: 'index.html',
            content: generatedCode,
            path: 'index.html',
          });
        }
      } else if (Array.isArray(generatedCode)) {

        for (const item of generatedCode) {
          if (typeof item === 'object' && item !== null) {
            const fileName = item.fileName || item.name || item.path || 'unknown.txt';
            const content = item.content || '';
            exportFiles.push({
              name: fileName,
              content: typeof content === 'string' ? content : JSON.stringify(content, null, 2),
              path: fileName,
            });
          }
        }
      } else if (typeof generatedCode === 'object' && generatedCode !== null) {

        this.processCodeForVSCodeExport(generatedCode, exportFiles);
      }

      return exportFiles;
    } catch (error) {
      return [];
    }
  }

  private processCodeForVSCodeExport(codeObject: any, exportFiles: any[]): void {
    for (const [filePath, content] of Object.entries(codeObject)) {
      exportFiles.push({
        name: filePath,
        content: typeof content === 'string' ? content : JSON.stringify(content, null, 2),
        path: filePath,
      });
    }
  }

  private handleVSCodeExportResult(result: VSCodeExportResult, appName: string): void {

    switch (result.method) {
      case 'vscode-protocol':
        if (result.success) {
          this.toastService.success('🎉 VSCode opened with your project!');
          this.toastService.info(
            'Your project files have been created and VSCode should be opening now.'
          );
        } else {
          this.toastService.warning('VSCode protocol attempted but may not have worked.');
          this.toastService.info("If VSCode didn't open, please check your VSCode installation.");
        }
        break;

      case 'download-fallback':
        const downloadFileName = result.downloadFileName || `${appName.toLowerCase()}.zip`;
        this.toastService.success(`📦 Project downloaded as "${downloadFileName}"`);
        this.toastService.info(
          `💡 Extract ${downloadFileName} → Open VSCode → File → Open Folder → Select extracted folder`
        );
        this.toastService.info(
          '🔧 For best experience: Open the .code-workspace file included in the download'
        );
        break;

      case 'manual-instructions':
        this.toastService.warning('⚠️ Please copy the files manually to your VSCode project');
        break;

      default:
        this.toastService.error(
          '❌ Unknown export method. Please try downloading the project instead.'
        );
        break;
    }

    if (result.success && result.method === 'vscode-protocol') {
      setTimeout(() => {
        this.toastService.info(
          '💡 Tip: Look for the new project folder and open the .code-workspace file in VSCode for the best experience!'
        );
      }, 3000);
    }

  }


  exportToAzure(): void {

    this.isExperienceStudioModalOpen$.next(false);

    this.toastService.info('Preparing Azure export...');

    setTimeout(() => {
      this.toastService.success('Project exported to Azure');
    }, 1500);
  }


  cloneInVSCode(): void {

    if (this.isCloneLoading$.value) {
      return;
    }

    this.isExperienceStudioModalOpen$.next(false);
    this.isCloneLoading$.next(true);

    try {

      const repositoryMetadata = this.generationStateService.getCurrentRepositoryMetadata();

      if (repositoryMetadata && repositoryMetadata.cloneUrl) {
        this.performVSCodeClone(repositoryMetadata.cloneUrl);
      } else {

        const fallbackUrl = this.generateRepositoryUrl();

        if (fallbackUrl) {
          this.performVSCodeClone(fallbackUrl);
        } else {
          this.toastService.error('Unable to generate repository URL. Please ensure project is properly configured.');
          return;
        }
      }

    } catch (error) {
      this.toastService.error('Failed to initiate VS Code clone. Please try copying the repository URL manually.');
    } finally {
      this.isCloneLoading$.next(false);
    }
  }


  private performVSCodeClone(repositoryUrl: string): void {
    const vscodeCloneUrl = `vscode://vscode.git/clone?url=${encodeURIComponent(repositoryUrl)}`;

    this.toastService.info('Opening VS Code to clone repository...');


    const vscodeWindow = window.open(vscodeCloneUrl, '_blank');

    if (vscodeWindow) {
      this.toastService.success('VS Code should open with clone dialog!');
      setTimeout(() => {
        this.toastService.info('💡 If VS Code didn\'t open, ensure it\'s installed and try copying the repository URL manually.');
      }, 3000);
    } else {

      this.copyRepositoryUrlToClipboard(repositoryUrl);
    }
  }


  private generateRepositoryUrl(): string {
    try {

      const organizationName = 'ascendion';
      const projectName = 'elderwand';


      let repositoryName = 'generated-project';

      if (this.projectId) {
        repositoryName = `project-${this.projectId}`;
      } else if (this.projectName) {
        repositoryName = this.projectName.toLowerCase().replace(/[^a-z0-9]/g, '-');
      } else {
        const appName = this.generateAppNameForDownload();
        repositoryName = appName.toLowerCase().replace(/[^a-z0-9]/g, '-');
      }


      const repositoryUrl = `https://dev.azure.com/${organizationName}/${projectName}/_git/${repositoryName}`;

      return repositoryUrl;
    } catch (error) {
      return '';
    }
  }


  private copyRepositoryUrlToClipboard(repositoryUrl: string): void {
    try {
      navigator.clipboard.writeText(repositoryUrl).then(() => {
        this.toastService.success('Repository URL copied to clipboard!');
        this.toastService.info('💡 Open VS Code → View → Command Palette → "Git: Clone" → Paste URL');
      }).catch(() => {
        this.showRepositoryUrlFallback(repositoryUrl);
      });
    } catch (error) {
      this.showRepositoryUrlFallback(repositoryUrl);
    }
  }


  private showRepositoryUrlFallback(repositoryUrl: string): void {
    this.toastService.warning('Please copy this repository URL manually:');
    setTimeout(() => {
      alert(`Repository URL: ${repositoryUrl}\n\nTo clone in VS Code:\n1. Open VS Code\n2. View → Command Palette\n3. Type "Git: Clone"\n4. Paste the URL above`);
    }, 500);
  }


  private async downloadProjectFromServer(): Promise<boolean> {
    if (!this.projectId) {
      this.toastService.error('Project ID not available for download');
      return false;
    }

    const userSignature = this.userSignatureService.getUserSignatureSync();
    const params = new HttpParams()
      .set('user_signature', userSignature);

    try {
      this.toastService.info('Downloading from server...');

      const response = await firstValueFrom(
        this.http.get(`${environment.apiUrl}/download/project/${this.projectId}`, {
          params,
          responseType: 'blob',
          observe: 'response'
        }).pipe(
          timeout(60000),
          takeUntilDestroyed(this.destroyRef)
        )
      );

      if (response && response.body) {

        const contentDisposition = response.headers.get('Content-Disposition');
        let filename = `project_${this.projectId}.zip`;

        if (contentDisposition) {
          const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
          if (filenameMatch && filenameMatch[1]) {
            filename = filenameMatch[1].replace(/['"]/g, '');
          }
        }


        const url = URL.createObjectURL(response.body);
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        link.style.display = 'none';

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);

        this.toastService.success(`Project downloaded successfully as ${filename}`);
        return true;
      }

      return false;
    } catch (error) {

      if (error instanceof HttpErrorResponse) {
        if (error.status >= 400 && error.status < 500) {
          this.toastService.warning('Server download unavailable, using local generation...');
        } else {
          this.toastService.warning('Server temporarily unavailable, using local generation...');
        }
      } else {
        this.toastService.warning('Download timeout, using local generation...');
      }

      return false;
    }
  }

  async downloadProject(): Promise<void> {

    if (this.isDownloadLoading$.value) {
      return;
    }

    this.isExperienceStudioModalOpen$.next(false);
    this.isDownloadLoading$.next(true);

    try {

      const serverDownloadSuccess = await this.downloadProjectFromServer();

      if (serverDownloadSuccess) {
        return;
      }


      this.toastService.info('Preparing download...');

      let generatedCode = this.getCodeFromMultipleSources();

      if (!generatedCode) {
        this.toastService.error(
          'No code available to download. Please ensure code generation is complete.'
        );
        return;
      }

      const appName = this.generateAppNameForDownload();

      const zip = new JSZip();

      const projectFolder = zip.folder(appName);

      if (!projectFolder) {
        throw new Error('Failed to create project folder in zip');
      }

      this.toastService.info('Processing files...');
      await this.addFilesToZip(projectFolder, generatedCode);

      this.addProjectMetadata(projectFolder, appName);

      this.toastService.info('Creating zip file...');
      const zipBlob = await zip.generateAsync({
        type: 'blob',
        compression: 'DEFLATE',
        compressionOptions: {
          level: 6,
        },
      });

      this.triggerDownload(zipBlob, appName);

      this.toastService.success(`Project "${appName}" downloaded successfully`);

    } catch (error) {
      this.toastService.error('Error creating project download. Please try again.');
    } finally {

      this.isDownloadLoading$.next(false);
    }
  }

  private getCodeFromMultipleSources(): any {

    let generatedCode = this.codeSharingService.getGeneratedCode();
    if (generatedCode) {
      return generatedCode;
    }

    let currentFiles: any = null;
    this.files.subscribe(files => (currentFiles = files)).unsubscribe();
    if (currentFiles && currentFiles.length > 0) {
      return currentFiles;
    }

    if (this.artifactsData && this.artifactsData.length > 0) {
      const codeArtifacts = this.artifactsData.filter(
        artifact => artifact.type === 'file' || artifact.type === 'code'
      );
      if (codeArtifacts.length > 0) {
        return codeArtifacts;
      }
    }

    return null;
  }

  private triggerDownload(zipBlob: Blob, appName: string): void {
    try {

      const url = URL.createObjectURL(zipBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${appName}.zip`;
      link.style.display = 'none';

      document.body.appendChild(link);
      link.click();

      document.body.removeChild(link);
      URL.revokeObjectURL(url);

    } catch (error) {
      throw new Error('Failed to trigger download');
    }
  }

  private generateAppNameForDownload(): string {

    if (this.appName) {
      return this.appName;
    }

    if (this.projectName) {
      return this.projectName.toLowerCase().replace(/[^a-z0-9]/g, '-');
    }

    const promptData = this.promptService.getCurrentPromptData();
    if (promptData?.selectedCardTitle) {
      const baseName = promptData.selectedCardTitle.toLowerCase().replace(/[^a-z0-9]/g, '-');
      return `${baseName}-app`;
    }

    const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
    return `generated-app-${timestamp}`;
  }

  private async addFilesToZip(projectFolder: JSZip, generatedCode: any): Promise<void> {
    if (typeof generatedCode === 'string') {

      try {
        const parsedCode = JSON.parse(generatedCode);
        await this.processCodeObject(projectFolder, parsedCode);
      } catch {

        projectFolder.file('index.html', generatedCode);
      }
    } else if (Array.isArray(generatedCode)) {

      for (const item of generatedCode) {
        if (typeof item === 'object' && item !== null) {
          const fileName = item.fileName || item.name || item.path || 'unknown.txt';
          const content = item.content || '';
          this.addFileToZipWithPath(projectFolder, fileName, content);
        }
      }
    } else if (typeof generatedCode === 'object' && generatedCode !== null) {

      await this.processCodeObject(projectFolder, generatedCode);
    }
  }

  private async processCodeObject(projectFolder: JSZip, codeObject: any): Promise<void> {
    for (const [filePath, content] of Object.entries(codeObject)) {
      const fileContent = typeof content === 'string' ? content : JSON.stringify(content, null, 2);
      this.addFileToZipWithPath(projectFolder, filePath, fileContent);
    }
  }

  private addFileToZipWithPath(projectFolder: JSZip, filePath: string, content: string): void {

    const cleanPath = filePath.replace(/^\/+/, '');

    const pathParts = cleanPath.split('/');
    const fileName = pathParts.pop() || 'unknown.txt';

    let currentFolder = projectFolder;
    for (const folderName of pathParts) {
      if (folderName.trim()) {
        const existingFolder = currentFolder.folder(folderName);
        currentFolder = existingFolder || currentFolder.folder(folderName)!;
      }
    }

    currentFolder.file(fileName, content);
  }

  private addProjectMetadata(projectFolder: JSZip, appName: string): void {

    const readmeContent = this.generateReadmeContent(appName);
    projectFolder.file('README.md', readmeContent);

  }


  private generateReadmeContent(appName: string): string {
    const currentDate = new Date().toLocaleDateString();
    return `# ${appName}

Generated on: ${currentDate}

## Description
This project was generated using the Experience Studio platform.

## Getting Started

### Prerequisites
- Node.js (version 14 or higher)
- npm or yarn

### Installation
\`\`\`bash
npm install
\`\`\`

### Running the Application
\`\`\`bash
npm start
\`\`\`

### Building for Production
\`\`\`bash
npm run build
\`\`\`

## Project Structure
- \`src/\` - Source code files
- \`public/\` - Static assets

## Support
For support and questions, please refer to the Experience Studio documentation.
`;
  }

  generateUnitTests(): void {

    this.isExperienceStudioModalOpen$.next(false);

    this.toastService.info('Generating unit tests...');

    setTimeout(() => {
      this.toastService.success('Unit tests generated successfully');
    }, 2000);
  }

  public handlePreviewTabClick(): void {

    const currentDeployedUrl = this.deployedUrl$.value;

    if (currentDeployedUrl || this.isInFailedState) {
      this.onTabClick('preview');
    } else {

    }
  }

  public handleEnhancedPreviewTabClick(): void {
    const currentState = this.currentPreviewTabState;

    if (currentState.isEnabled || currentState.hasError) {
      this.onTabClick('preview');
    } else if (currentState.isLoading) {

    } else {

    }
  }

  public handleEnhancedCodeTabClick(): void {
    const currentState = this.currentCodeTabState;

    if (currentState.isEnabled || currentState.hasError) {
      this.onTabClick('code');
    }
  }

  generateAPI(): void {

    this.isExperienceStudioModalOpen$.next(false);

    this.toastService.info('Generating API...');

    setTimeout(() => {
      this.toastService.success('API generated successfully');
    }, 2000);
  }

  onTabClick(tab: string): void {

    if (this.shouldPreventTabSwitching(tab)) {
      return;
    }

    if (this.tabTransitionInProgress()) {
      return;
    }

    this.userSelectedTab = true;

    this.startTabTransition(tab);

    switch (tab) {
      case 'overview':
        this.toggleOverviewViewEnhanced();
        break;
      case 'preview':
        this.togglePreviewViewEnhanced();
        break;
      case 'code':

        if ((this.isCodeGenerationComplete && !this.previewError$.value) || this.currentCodeTabState.isEnabled) {
          this.toggleCodeViewEnhanced();
        } else {
          this.completeTabTransition();
        }
        break;
      case 'logs':

        if (this.isArtifactsTabEnabledWithLogs) {
          this.toggleLogsViewEnhanced();
        } else {
          this.completeTabTransition();
        }
        break;
      case 'artifacts':

        if (this.isArtifactsTabEnabledWithLogs) {
          this.toggleArtifactsViewEnhanced();
        } else {
          this.completeTabTransition();
        }
        break;
      default:
        this.completeTabTransition();
    }
  }

  @HostListener('window:message', ['$event'])
  onMessage(event: MessageEvent) {
    if (event.data && event.data.type === 'elementSelected') {
      this.selectedElement = event.data.element;
      this.showEditorIcon = true;
    }
  }


  @HostListener('window:popstate', ['$event'])
  onPopState(event: PopStateEvent): void {
    this.handleBrowserNavigation();
  }


  @HostListener('window:beforeunload', ['$event'])
  onBeforeUnload(event: BeforeUnloadEvent): void {
        this.handlePageBeforeUnload();

  }


  @HostListener('window:pagehide', ['$event'])
  onPageHide(event: PageTransitionEvent): void {
    this.handlePageHide();
  }


  @HostListener('document:visibilitychange', ['$event'])
  onVisibilityChange(): void {
    if (document.hidden) {
            this.handlePageVisibilityChange();
    }
  }

  onIframeLoad(_event: Event): void {
    this.isPreviewLoading$.next(false);

    if (this.isRegenerationInProgress$.value) {
      this.isRegenerationInProgress$.next(false);
      this.regenerationStartTime = 0;
    }

    // Only add automatic accordion if NOT in project loading mode
    // In project loading mode, accordion is added manually after stepper completes
    if (this.isCodeGenerationComplete && !this.hasAddedInitialGenerationAccordion && !this.isProjectLoadingMode) {
      this.addGenerationAccordionToChat();
      this.hasAddedInitialGenerationAccordion = true;
    }


  }

  onIframeError(_event: any): void {
    this.previewError$.next(true);
    this.errorDescription$.next(
      'Failed to load preview. The deployed application may not be accessible.'
    );
  }


  onEditButtonClick(): void {

  }


  onRetryClick() {

    this.previewError$.next(false);

    this.previewTabName$.next('Preview');
    this.setPreviewTabLoading('Retrying code generation...');

    this.isCodeGenerationComplete = false;

    this.isPromptBarEnabled = false;

    this.isCodeActive$.next(false);
    this.isPreviewActive$.next(true);

    this.toastService.info('Retrying code generation...');

    if (this.projectId && this.jobId && !this.isUIDesignMode$.value) {


      this.pollingService.resetLogsButKeepStep();

      this.enableSSEEventFilteringForRetry();

      this.synchronizeStepperWithSSEStream();

    } else if (this.isUIDesignMode$.value) {
    }

    if (this.projectId && this.jobId) {
      this.updatePollingStatus(true);

      this.isLoading$.next(true);
      this.isPreviewLoading$.next(true);

      this.togglePreviewView();
    } else {

      this.navigateToHome();
    }

  }

  private enableSSEEventFilteringForRetry(): void {

    if (!this.projectId || !this.jobId) {
      return;
    }

    const retryTimestamp = Date.now();

    this.enhancedSSEService.enableRetryEventFiltering(retryTimestamp);

  }

  private synchronizeStepperWithSSEStream(): void {

    if (!this.projectId || !this.jobId) {
      return;
    }

    this.stepperStateService.resynchronizeWithSSEStream(this.projectId, this.jobId);

  }

  private sortFilesByTechnology(files: FileModel[], technology: string): FileModel[] {
    if (!files || files.length === 0) {
      return files;
    }

    const filePriority: { [key: string]: { [key: string]: number } } = {
      react: {

        'src/components/': 100,
        'components/': 100,
        'src/pages/': 99,
        'pages/': 99,
        'src/services/': 98,
        'services/': 98,
        'src/utils/': 97,
        'utils/': 97,
        'src/hooks/': 96,
        'hooks/': 96,
        'src/context/': 95,
        'context/': 95,
        'src/assets/': 94,
        'assets/': 94,
        'src/styles/': 93,
        'styles/': 93,
        'src/App.js': 92,
        'src/App.jsx': 92,
        'src/App.tsx': 92,
        'App.js': 92,
        'App.jsx': 92,
        'App.tsx': 92,
        'src/App.css': 91,
        'App.css': 91,
        'src/index.js': 90,
        'src/index.jsx': 90,
        'src/index.tsx': 90,
        'index.js': 90,
        'index.jsx': 90,
        'index.tsx': 90,
        'public/index.html': 89,
        'index.html': 89,
        'public/': 88,
        '.js': 80,
        '.jsx': 80,
        '.tsx': 80,
        '.css': 79,
        '.scss': 79,
        '.html': 78,

        'tsconfig.json': 20,
        'staticwebapp.config.json': 19,
        'package-lock.json': 18,
        'eslint.config.js': 17,
        'azure-pipelines.yml': 16,
        'angular.json': 15,
        '.npmrc': 14,
        '.hintrc': 13,
        '.editorconfig': 12,
        'projects/playground/': 11,
        'projects/play-comp-library/': 10,
        'projects/': 9,
        'Pipeline/play-library.yml': 8,
        'Pipeline/': 7,
        'node_modules/': 6,
        'dist/': 5,
        '.vscode/': 4,
        '.angular/': 3,
        '.gitignore': 2,
        'README.md': 1,
        'package.json': 0,
      },
      angular: {

        'src/app/components/': 100,
        'components/': 100,
        'src/app/pages/': 99,
        'pages/': 99,
        'src/app/services/': 98,
        'services/': 98,
        'src/app/models/': 97,
        'models/': 97,
        'src/app/directives/': 96,
        'directives/': 96,
        'src/app/pipes/': 95,
        'pipes/': 95,
        'src/app/guards/': 94,
        'guards/': 94,
        'src/app/interceptors/': 93,
        'interceptors/': 93,
        'src/assets/': 92,
        'assets/': 92,
        'src/environments/': 91,
        'environments/': 91,
        'src/app/app.component.ts': 90,
        'app.component.ts': 90,
        'src/app/app.component.html': 89,
        'app.component.html': 89,
        'src/app/app.component.scss': 88,
        'src/app/app.component.css': 88,
        'app.component.scss': 88,
        'app.component.css': 88,
        'src/app/app-routing.module.ts': 87,
        'app-routing.module.ts': 87,
        'src/app/app.module.ts': 86,
        'app.module.ts': 86,
        'src/main.ts': 85,
        'main.ts': 85,
        'src/index.html': 84,
        'index.html': 84,
        '.ts': 80,
        '.html': 79,
        '.scss': 78,
        '.css': 78,

        'tsconfig.json': 20,
        'staticwebapp.config.json': 19,
        'package-lock.json': 18,
        'eslint.config.js': 17,
        'azure-pipelines.yml': 16,
        'angular.json': 15,
        '.npmrc': 14,
        '.hintrc': 13,
        '.editorconfig': 12,
        'projects/playground/': 11,
        'projects/play-comp-library/': 10,
        'projects/': 9,
        'Pipeline/play-library.yml': 8,
        'Pipeline/': 7,
        'node_modules/': 6,
        'dist/': 5,
        '.vscode/': 4,
        '.angular/': 3,
        '.gitignore': 2,
        'README.md': 1,
        'package.json': 0,
      },
      vue: {

        'src/components/': 100,
        'components/': 100,
        'src/views/': 99,
        'views/': 99,
        'src/pages/': 99,
        'pages/': 99,
        'src/services/': 98,
        'services/': 98,
        'src/utils/': 97,
        'utils/': 97,
        'src/assets/': 96,
        'assets/': 96,
        'src/styles/': 95,
        'styles/': 95,
        'src/store/index.js': 94,
        'src/store/index.ts': 94,
        'store/index.js': 94,
        'store/index.ts': 94,
        'src/router/index.js': 93,
        'src/router/index.ts': 93,
        'router/index.js': 93,
        'router/index.ts': 93,
        'src/App.vue': 92,
        'App.vue': 92,
        'src/main.js': 91,
        'src/main.ts': 91,
        'main.js': 91,
        'main.ts': 91,
        'public/index.html': 90,
        'index.html': 90,
        'public/': 89,
        '.vue': 80,
        '.js': 79,
        '.ts': 79,
        '.scss': 78,
        '.css': 78,
        '.html': 77,

        'tsconfig.json': 20,
        'staticwebapp.config.json': 19,
        'package-lock.json': 18,
        'eslint.config.js': 17,
        'azure-pipelines.yml': 16,
        'angular.json': 15,
        '.npmrc': 14,
        '.hintrc': 13,
        '.editorconfig': 12,
        'projects/playground/': 11,
        'projects/play-comp-library/': 10,
        'projects/': 9,
        'Pipeline/play-library.yml': 8,
        'Pipeline/': 7,
        'node_modules/': 6,
        'dist/': 5,
        '.vscode/': 4,
        '.angular/': 3,
        '.gitignore': 2,
        'README.md': 1,
        'package.json': 0,
      },
    };

    const normalizedTech = technology.toLowerCase();
    const techKey = normalizedTech.includes('react')
      ? 'react'
      : normalizedTech.includes('angular')
        ? 'angular'
        : normalizedTech.includes('vue')
          ? 'vue'
          : 'angular';

    const priorityMap = filePriority[techKey] || filePriority['angular'];

    const sortedFiles = [...files].sort((a, b) => {
      const aName = a.name.toLowerCase();
      const bName = b.name.toLowerCase();

      let aPriority = 1000;
      let aNestedLevel = aName.split('/').length - 1;

      for (const [pattern, priority] of Object.entries(priorityMap)) {
        if (pattern.endsWith('/')) {

          if (aName.includes(pattern)) {
            aPriority = priority;
            break;
          }
        } else if (aName === pattern.toLowerCase()) {

          aPriority = priority;
          break;
        } else if (aName.endsWith(pattern.toLowerCase())) {

          aPriority = priority;
          break;
        }
      }

      let bPriority = 1000;
      let bNestedLevel = bName.split('/').length - 1;

      for (const [pattern, priority] of Object.entries(priorityMap)) {
        if (pattern.endsWith('/')) {

          if (bName.includes(pattern)) {
            bPriority = priority;
            break;
          }
        } else if (bName === pattern.toLowerCase()) {

          bPriority = priority;
          break;
        } else if (bName.endsWith(pattern.toLowerCase())) {

          bPriority = priority;
          break;
        }
      }

      if (aPriority !== bPriority) {
        return bPriority - aPriority;
      }

      if (aNestedLevel !== bNestedLevel) {
        return bNestedLevel - aNestedLevel;
      }

      return aName.localeCompare(bName);
    });

    const nestingStats: Record<string, number> = {};
    sortedFiles.forEach(file => {
      const level = file.name.split('/').length - 1;
      const levelKey = level.toString();
      nestingStats[levelKey] = (nestingStats[levelKey] || 0) + 1;
    });

    return sortedFiles;
  }

  private initializeDefaultPrompt(): void {
    // Skip default prompt initialization in project loading mode
    // Project loading mode handles its own chat initialization
    if (this.isProjectLoadingMode$.value) {
      console.log('🚫 Skipping default prompt initialization - project loading mode active');
      return;
    }

    if (this.lightMessages.length === 0) {

      this.appStateService.project$
        .subscribe(projectState => {
          if (projectState.prompt) {

            let userSelectionsSummary = '';

            if (projectState.imageUrl) {
              userSelectionsSummary += '- **Image**:  You provided an image for reference\n';
            }

            if (projectState.technology) {
              userSelectionsSummary += `- **Technology**: ${projectState.technology.charAt(0).toUpperCase() + projectState.technology.slice(1)}\n`;
            }

            if (projectState.designLibrary) {
              userSelectionsSummary += `- **Design Library**: ${projectState.designLibrary.charAt(0).toUpperCase() + projectState.designLibrary.slice(1)}\n`;
            }

            if (projectState.application) {
              userSelectionsSummary += `- **Application Type**: ${projectState.application.charAt(0).toUpperCase() + projectState.application.slice(1)}\n`;
            }

            if (projectState.type) {
              userSelectionsSummary += `- **Generation Type**: ${projectState.type}\n`;
            }

            this.lightMessages = [
              {
                text: projectState.prompt,
                from: 'user',
                theme: 'light',
                imageDataUri: projectState.imageDataUri || undefined,
              },

            ];

            if (projectState.imageDataUri) {
              this.selectedImageDataUri = projectState.imageDataUri;
            }
          } else {

            this.lightMessages = [
              {
                text: "Welcome to the code generation experience. I'll help you create code based on your requirements.",
                from: 'ai',
                theme: 'light',
              },
            ];
          }
        })
        .unsubscribe();
    }
  }

  private startLogStreaming(allLogs: string[]): void {

    if (this.logStreamTimer) {
      clearInterval(this.logStreamTimer);
    }

    if (!allLogs || allLogs.length === 0) {
      this.isStreamingLogs = false;
      return;
    }

    const wasFirstTime = !this.hasLogs;
    if (!this.hasLogs) {
      this.hasLogs = true;
    }

    if (wasFirstTime) {
      this.autoEnableLogsTabIfNeeded();

      this.enableArtifactsTabIfNeeded();
    }

    const newLogs = this.filterNewLogs(allLogs);

    if (newLogs.length === 0) {
      this.isStreamingLogs = false;
      return;
    }

    const processedLogs = this.processLogs(newLogs);

    if (processedLogs.length > 0) {

      this.isStreamingLogs = false;
      this.isTypingLog = false;

      processedLogs.forEach(log => {
        log.visibleContent = log.content || '';
      });

      this.formattedLogMessages = [...this.formattedLogMessages, ...processedLogs];

      this.logMessages = [...this.logMessages, ...newLogs];

      this.enableArtifactsTabIfNeeded();

      setTimeout(() => {
        this.scrollLogsToBottom();
      }, 0);
    }
  }

  private filterNewLogs(allLogs: string[]): string[] {

    const existingRawLogs = new Set(this.formattedLogMessages.map(log => log.rawLog || ''));

    return allLogs.filter(log => {

      if (!log || log.trim() === '') {
        return false;
      }

      if (existingRawLogs.has(log)) {
        return false;
      }

      const logHash = this.createLogHash(log);

      if (this.processedLogHashes.has(logHash)) {
        return false;
      }

      this.processedLogHashes.add(logHash);
      return true;
    });
  }

  private createLogHash(log: string): string {

    const timestamp = this.extractTimestamp(log);
    const content = this.extractLogContent(log);
    const length = log.length;

    return `${timestamp}-${content.substring(0, 50)}-${length}`;
  }

  private processLogs(logs: string[]): any[] {
    const processedLogs: any[] = [];

    const processedJsonLogs = new Set<string>();

    logs.forEach(log => {

      if (!log || log.trim() === '') {
        return;
      }

      const logContent = this.extractLogContent(log);

      if (log.includes('Generated code:')) {
        try {

          const jsonStartIndex = log.indexOf('Generated code:') + 'Generated code:'.length;
          const jsonPart = log.substring(jsonStartIndex).trim();

          if (jsonPart) {
            const codeData = JSON.parse(jsonPart);

            processedJsonLogs.add(log);

            if (typeof codeData === 'object' && codeData !== null) {
              if (Array.isArray(codeData)) {

                codeData.forEach(item => {
                  if (item && item.fileName && item.content) {
                    const codeId = `code-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
                    processedLogs.push({
                      id: codeId,
                      type: 'code',
                      timestamp: this.extractTimestamp(log),
                      path: item.fileName,
                      content: item.content,
                      contentLines: item.content.split('\n').length,
                      contentSize: item.content.length,
                      rawLog: log,
                    });

                    this.expandedCodeLogs.add(codeId);
                  }
                });
                return;
              } else {

                Object.entries(codeData).forEach(([path, content]) => {
                  if (path && content) {
                    const formattedContent =
                      typeof content === 'string' ? content : JSON.stringify(content, null, 2);

                    const codeId = `code-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
                    processedLogs.push({
                      id: codeId,
                      type: 'code',
                      timestamp: this.extractTimestamp(log),
                      path: path,
                      content: formattedContent,
                      contentLines: formattedContent.split('\n').length,
                      contentSize: formattedContent.length,
                      rawLog: log,
                    });

                    this.expandedCodeLogs.add(codeId);
                  }
                });
                return;
              }
            }
          }
        } catch (e) {
        }
      }

      if (log.includes('{') && log.includes('}')) {
        try {

          const jsonStartIndex = log.indexOf('{');
          const jsonEndIndex = log.lastIndexOf('}') + 1;

          if (jsonStartIndex !== -1 && jsonEndIndex > jsonStartIndex) {
            const jsonPart = log.substring(jsonStartIndex, jsonEndIndex);

            const codeData = JSON.parse(jsonPart);

            processedJsonLogs.add(log);

            if (
              codeData &&
              typeof codeData === 'object' &&
              codeData.filesToGenerate &&
              Array.isArray(codeData.filesToGenerate)
            ) {

              const formattedFiles = codeData.filesToGenerate.map((file: string) => {

                let icon = '📄';
                if (file.endsWith('.ts') || file.endsWith('.tsx')) icon = '📘';
                else if (file.endsWith('.js') || file.endsWith('.jsx')) icon = '📙';
                else if (file.endsWith('.css')) icon = '🎨';
                else if (file.endsWith('.html')) icon = '🌐';
                else if (file.endsWith('.json')) icon = '📋';

                return `- ${icon} ${file}`;
              });

              const formattedContent = `# Files to Generate\n\n${formattedFiles.join('\n')}`;

              if (!formattedContent || formattedContent.trim() === '') return;

              const codeId = `code-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
              processedLogs.push({
                id: codeId,
                type: 'code',
                timestamp: this.extractTimestamp(log),
                path: 'Project Structure',
                content: formattedContent,
                contentLines: formattedContent.split('\n').length,
                contentSize: formattedContent.length,
                rawLog: log,
              });

              this.expandedCodeLogs.add(codeId);

              processedLogs.push({
                id: `log-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
                type: 'info',
                timestamp: this.extractTimestamp(log),
                content: `Preparing to generate ${codeData.filesToGenerate.length} files`,
                rawLog: log,
              });

              return;
            }

            if (codeData && typeof codeData === 'object' && codeData.message && codeData.data) {

              if (typeof codeData.data === 'string') {

                let dataString = codeData.data;
                let dataJson = null;

                try {
                  dataJson = JSON.parse(dataString);
                } catch (directParseError) {

                  if (dataString.includes('\\\"') || dataString.includes('\\\\')) {
                    try {

                      dataString = dataString.replace(/\\"/g, '"').replace(/\\\\/g, '\\');
                      dataJson = JSON.parse(dataString);
                    } catch (unescapeError) {}
                  }
                }


                if (dataJson) {

                  if (typeof dataJson === 'object' && !Array.isArray(dataJson)) {


                    Object.entries(dataJson).forEach(([path, content]) => {
                      if (path && content) {
                        const formattedContent =
                          typeof content === 'string' ? content : JSON.stringify(content, null, 2);


                        if (!formattedContent || formattedContent.trim() === '') return;

                        const codeId = `code-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
                        processedLogs.push({
                          id: codeId,
                          type: 'code',
                          timestamp: this.extractTimestamp(log),
                          path: path,
                          content: formattedContent,
                          contentLines: formattedContent.split('\n').length,
                          contentSize: formattedContent.length,
                          rawLog: log,
                        });


                        this.expandedCodeLogs.add(codeId);
                      }
                    });
                  } else if (Array.isArray(dataJson)) {
                    dataJson.forEach(item => {
                      if (item && typeof item === 'object') {
                        const path = item.fileName || item.path || item.name || 'unknown.file';
                        const content = item.content || '';

                        if (path && content) {
                          const formattedContent =
                            typeof content === 'string'
                              ? content
                              : JSON.stringify(content, null, 2);


                          if (!formattedContent || formattedContent.trim() === '') return;

                          const codeId = `code-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
                          processedLogs.push({
                            id: codeId,
                            type: 'code',
                            timestamp: this.extractTimestamp(log),
                            path: path,
                            content: formattedContent,
                            contentLines: formattedContent.split('\n').length,
                            contentSize: formattedContent.length,
                            rawLog: log,
                          });


                          this.expandedCodeLogs.add(codeId);
                        }
                      }
                    });
                  }


                  processedLogs.push({
                    id: `log-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
                    type: 'info',
                    timestamp: this.extractTimestamp(log),
                    content: codeData.message,
                    rawLog: log,
                  });

                  return;
                }
              }
            }


            if (typeof codeData === 'object' && codeData !== null) {

              if (!Array.isArray(codeData)) {

                Object.entries(codeData).forEach(([path, content]) => {

                  if (!content || (typeof content === 'string' && content.trim() === '')) {
                    return;
                  }


                  if (!content) return;


                  const formattedContent =
                    typeof content === 'string' ? content : JSON.stringify(content, null, 2);


                  if (!formattedContent || formattedContent.trim() === '') return;


                  const contentLines = formattedContent.split('\n').length;
                  const contentSize = formattedContent.length;


                  const codeId = `code-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
                  processedLogs.push({
                    id: codeId,
                    type: 'code',
                    timestamp: this.extractTimestamp(log),
                    path: path || 'unknown.file',
                    content: formattedContent,
                    contentLines: contentLines,
                    contentSize: contentSize,
                    rawLog: log,
                  });


                  this.expandedCodeLogs.add(codeId);
                });
                return;
              }

              else if (Array.isArray(codeData) && codeData.length > 0) {
                codeData.forEach(item => {

                  if (
                    !item.content ||
                    (typeof item.content === 'string' && item.content.trim() === '')
                  ) {
                    return;
                  }


                  const formattedContent =
                    typeof item.content === 'string'
                      ? item.content
                      : JSON.stringify(item.content, null, 2);


                  if (!formattedContent || formattedContent.trim() === '') return;


                  const codeId = `code-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
                  processedLogs.push({
                    id: codeId,
                    type: 'code',
                    timestamp: this.extractTimestamp(log),
                    path: item.fileName || item.path || 'unknown.file',
                    content: formattedContent,
                    contentLines: formattedContent.split('\n').length,
                    contentSize: formattedContent.length,
                    rawLog: log,
                  });


                  this.expandedCodeLogs.add(codeId);
                });
                return;
              }

              else if (
                typeof codeData === 'object' &&
                'fileName' in codeData &&
                'content' in codeData
              ) {

                if (
                  !codeData.content ||
                  (typeof codeData.content === 'string' && codeData.content.trim() === '')
                ) {
                  return;
                }


                const formattedContent =
                  typeof codeData.content === 'string'
                    ? codeData.content
                    : JSON.stringify(codeData.content, null, 2);


                if (!formattedContent || formattedContent.trim() === '') return;


                const contentLines = formattedContent.split('\n').length;
                const contentSize = formattedContent.length;


                const codeId = `code-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
                processedLogs.push({
                  id: codeId,
                  type: 'code',
                  timestamp: this.extractTimestamp(log),
                  path: codeData.fileName || 'unknown.file',
                  content: formattedContent,
                  contentLines: contentLines,
                  contentSize: contentSize,
                  rawLog: log,
                });


                this.expandedCodeLogs.add(codeId);
                return;
              }
            }
          }
        } catch (e) {
        }
      }


      if (log.includes('ERROR')) {

        if (!logContent || logContent.trim() === '') {
          return;
        }

        processedLogs.push({
          id: `log-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
          type: 'error',
          timestamp: this.extractTimestamp(log),
          content: logContent,
          rawLog: log,
        });
        return;
      }


      if (log.includes('WARN')) {

        if (!logContent || logContent.trim() === '') {
          return;
        }

        processedLogs.push({
          id: `log-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
          type: 'warning',
          timestamp: this.extractTimestamp(log),
          content: logContent,
          rawLog: log,
        });
        return;
      }


      if (log.includes('Progress Description')) {

        if (!logContent || logContent.trim() === '') {
          return;
        }

        const progressDesc = logContent.replace('Progress Description Updated: ', '').trim();

        if (progressDesc !== this.lastProgressState) {
          this.lastProgressState = progressDesc;

          processedLogs.push({
            id: `log-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
            type: 'progress-description',
            timestamp: this.extractTimestamp(log),
            content: logContent,
            rawLog: log,
          });
        }
        return;
      }

      if (log.includes('Status changed to:')) {

        if (!logContent || logContent.trim() === '') {
          return;
        }

        processedLogs.push({
          id: `log-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
          type: 'status-change',
          timestamp: this.extractTimestamp(log),
          content: logContent,
          rawLog: log,
        });
        return;
      }

      if (log.includes('DEBUG')) {

        if (!logContent || logContent.trim() === '') {
          return;
        }

        if (logContent.includes('Raw response:')) {
          return;
        }

        processedLogs.push({
          id: `log-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
          type: 'debug',
          timestamp: this.extractTimestamp(log),
          content: logContent,
          rawLog: log,
        });
        return;
      }

      if (!logContent || logContent.trim() === '' || processedJsonLogs.has(log)) {
        return;
      }

      if ((log.includes('{') && log.includes('}')) || (log.includes('[') && log.includes(']'))) {

        const jsonStartIndex = log.indexOf('{');
        const jsonEndIndex = log.lastIndexOf('}') + 1;

        if (jsonStartIndex !== -1 && jsonEndIndex > jsonStartIndex) {
          try {

            const jsonPart = log.substring(jsonStartIndex, jsonEndIndex);
            JSON.parse(jsonPart);

            if (
              jsonPart.includes('"data"') ||
              jsonPart.includes('"filesToGenerate"') ||
              jsonPart.includes('"fileName"') ||
              jsonPart.includes('"content"')
            ) {
              return;
            }
          } catch (e) {

          }
        }
      }

      processedLogs.push({
        id: `log-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
        type: 'info',
        timestamp: this.extractTimestamp(log),
        content: logContent,
        rawLog: log,
      });
    });


    return processedLogs;
  }

  private extractTimestamp(log: string): string {

    const parts = log.split(' - ');
    if (parts.length >= 1) {
      const timestamp = parts[0];

      if (timestamp.match(/^\d{2}:\d{2}:\d{2}/)) {
        const now = new Date();
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const day = String(now.getDate()).padStart(2, '0');
        return `${month}/${day} ${timestamp}`;
      }
    }

    const now = new Date();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const time = now.toLocaleTimeString('en-US', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
    return `${month}/${day} ${time}`;
  }

  private extractLogContent(log: string): string {

    const parts = log.split(' - ');
    if (parts.length >= 3) {
      return parts.slice(2).join(' - ');
    }
    return log;
  }

  private getLanguageFromPath(path: string): string {
    const extension = path.split('.').pop()?.toLowerCase();

    switch (extension) {
      case 'ts':
        return 'typescript';
      case 'js':
        return 'javascript';
      case 'html':
        return 'html';
      case 'css':
        return 'css';
      case 'scss':
        return 'scss';
      case 'json':
        return 'json';
      case 'md':
        return 'markdown';
      case 'py':
        return 'python';
      case 'java':
        return 'java';
      case 'xml':
        return 'xml';
      case 'yaml':
      case 'yml':
        return 'yaml';
      default:
        return 'plaintext';
    }
  }


  private scrollLogsToBottom(): void {
    setTimeout(() => {
      const logsContainer = document.querySelector('.logs-content');
      if (logsContainer) {
        logsContainer.scrollTop = logsContainer.scrollHeight;
      }
    }, 0);
  }


  private addLogUpdateToChatWindow(_logs: string[]): void {

    this.isLogsTabEnabled = true;


    this.enableArtifactsTabIfNeeded();


    if (this.hasLogs && !this.userSelectedTab && this.isArtifactsTabEnabledWithLogs) {
      setTimeout(() => {

        this.toggleArtifactsView();

        this.userSelectedTab = false;
      }, 500);
    }

  }

  private formatLayoutAnalyzedDescription(description: string): string {
    if (!description.includes('LAYOUT_ANALYZED')) {
      return description;
    }

    const layoutMatch = description.match(/LAYOUT_ANALYZED\s+(.*?)(?:\n|$)/);

    if (layoutMatch && layoutMatch[1]) {
      const fileList = layoutMatch[1].trim();

      let files: string[] = [];

      if (fileList.includes(',')) {
        files = fileList.split(',').map(file => file.trim());
      }

      else if (fileList.includes(' ')) {
        files = fileList.split(/\s+/).filter(file => file.trim() !== '');
      }

      else {
        files = [fileList];
      }

      files = files.filter(file => file.trim() !== '');

      const formattedFileList = files
        .map(file => {

          let icon = '📄';
          if (file.endsWith('.ts') || file.endsWith('.tsx')) icon = '📘';
          else if (file.endsWith('.js') || file.endsWith('.jsx')) icon = '📙';
          else if (file.endsWith('.css')) icon = '🎨';
          else if (file.endsWith('.html')) icon = '🌐';
          else if (file.endsWith('.json')) icon = '📋';

          return `- ${icon} ${file}`;
        })
        .join('\n');

      return description.replace(
        /LAYOUT_ANALYZED\s+(.*?)(?:\n|$)/,
        `### Project Structure Analysis\n\nThe following files will be generated:\n\n${formattedFileList}\n\n`
      );
    }

    return description;
  }

  formatCodeForDisplay(code: string): string {
    if (!code) return '';

    return code
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/ /g, '&nbsp;')
      .replace(/\n/g, '<br>');
  }

  toggleCodeExpansion(log: any): void {

    if (!log.id) {
      log.id = `code-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
    }

    const isCurrentlyTyping =
      this.isTypingLog &&
      this.currentLogIndex < this.formattedLogMessages.length &&
      this.formattedLogMessages[this.currentLogIndex].id === log.id;

    if (isCurrentlyTyping && this.expandedCodeLogs.has(log.id)) {
      return;
    }

    const hasFinishedTyping =
      log.visibleContent && log.content && log.visibleContent.length === log.content.length;

    if (this.expandedCodeLogs.has(log.id)) {

      if (hasFinishedTyping || !isCurrentlyTyping) {
        this.expandedCodeLogs.delete(log.id);
      } else {
      }
    } else {

      this.expandedCodeLogs.add(log.id);
    }
  }

  isCodeExpanded(id: string): boolean {

    return id ? this.expandedCodeLogs.has(id) : true;
  }

  getLayoutForPageIndex(index: number): string {
    if (!this.layoutData || this.layoutData.length === 0) {
      return '';
    }

    if (this.layoutData.length === 1) {
      return this.layoutData[0];
    }

    return this.layoutData[index % this.layoutData.length];
  }

  extractErrorMessageFromProgressDescription(): void {

    const statusResponse = this.pollingService.getLastStatusResponse();

    if (
      statusResponse &&
      statusResponse.details &&
      statusResponse.details.progress_description &&
      statusResponse.details.progress_description.trim() !== ''
    ) {

      const extractedErrorMessage = this.extractErrorMessageFromSSEData(statusResponse.details);
      const errorMessage = extractedErrorMessage || statusResponse.details.progress_description;

      this.errorDescription$.next(errorMessage);
      this.errorTerminalOutput$.next(this.formatErrorOutput(statusResponse.details));
      return;
    }

    const progressDescription = this.pollingService.getLastProgressDescription();
    if (progressDescription && progressDescription.trim() !== '') {

      try {

        if (progressDescription.includes('{') && progressDescription.includes('}')) {
          const jsonStartIndex = progressDescription.indexOf('{');
          const jsonEndIndex = progressDescription.lastIndexOf('}') + 1;
          const jsonPart = progressDescription.substring(jsonStartIndex, jsonEndIndex);
          const parsedData = JSON.parse(jsonPart);


          const extractedErrorMessage = this.extractErrorMessageFromSSEData(parsedData);
          if (extractedErrorMessage) {
            this.errorDescription$.next(extractedErrorMessage);
            this.errorTerminalOutput$.next(this.formatErrorOutput(parsedData));
            return;
          }

          if (parsedData.message) {
            this.errorDescription$.next(parsedData.message);
          } else if (parsedData.progress_description) {
            this.errorDescription$.next(parsedData.progress_description);
          } else if (parsedData.details && parsedData.details.message) {
            this.errorDescription$.next(parsedData.details.message);
          } else if (parsedData.details && parsedData.details.progress_description) {
            this.errorDescription$.next(parsedData.details.progress_description);
          } else if (parsedData.error) {
            this.errorDescription$.next(
              typeof parsedData.error === 'string'
                ? parsedData.error
                : JSON.stringify(parsedData.error)
            );
          }

          this.errorTerminalOutput$.next(this.formatErrorOutput(parsedData));
        } else {

          this.errorDescription$.next(progressDescription);
          this.errorTerminalOutput$.next(progressDescription);
        }
      } catch (e) {
        this.errorDescription$.next(progressDescription);
        this.errorTerminalOutput$.next(progressDescription);
      }
    }
  }

  formatErrorOutput(errorData: any): string {
    try {

      const formattedError: any = {
        error_type: errorData.status || 'ERROR',
        timestamp: new Date().toISOString(),
        details: {},
      };

      if (errorData.message) {
        formattedError.message = errorData.message;
      } else if (errorData.progress_description) {
        formattedError.message = errorData.progress_description;
      }

      if (errorData.details) {
        formattedError.details = errorData.details;
      }

      if (errorData.log) {
        try {

          if (
            typeof errorData.log === 'string' &&
            (errorData.log.includes('{') || errorData.log.includes('['))
          ) {
            formattedError.log = JSON.parse(errorData.log);
          } else {
            formattedError.log = errorData.log;
          }
        } catch (e) {
          formattedError.log = errorData.log;
        }
      }

      if (errorData.stack) {
        formattedError.stack_trace = errorData.stack;
      }

      return JSON.stringify(formattedError, null, 2);
    } catch (e) {

      return JSON.stringify(errorData, null, 2);
    }
  }

  trackByLogIndex(index: number, item: any): string {
    return item.id || index.toString();
  }

  trackByLayoutId(index: number, layout: any): string {
    return layout || index.toString();
  }

  shouldShowAnalyzingLayout(): boolean {

    if (this.layoutAnalyzedData && this.layoutAnalyzedData.length > 0 && this.hasLayoutBeenDetected) {
      return false;
    }

    return this.isAnalyzingLayout || !this.hasLayoutBeenDetected ||
           !this.layoutAnalyzedData || this.layoutAnalyzedData.length === 0;
  }

  getDefaultLayoutKeyForAnalyzing(): string {

    if (this.detectedLayoutFromPrevMetadata) {
      return this.detectedLayoutFromPrevMetadata;
    }

    if (this.layoutData && this.layoutData.length > 0) {
      return this.layoutData[0];
    }

    if (this.layoutAnalyzedData && this.layoutAnalyzedData.length > 0) {
      return this.layoutAnalyzedData[0].key;
    }

    return 'HB';
  }

  private startLayoutAnalyzing(): void {
    this.isAnalyzingLayout = true;
    this.hasLayoutBeenDetected = false;
  }

  private stopLayoutAnalyzing(): void {
    this.isAnalyzingLayout = false;
    this.hasLayoutBeenDetected = true;
  }

  private handleLayoutAnalysisFailed(): void {
    this.isAnalyzingLayout = false;
    this.hasLayoutBeenDetected = false;
  }


  trackByPageIndex(index: number): number {
    return index;
  }

  trackByArtifactId(index: number, artifact: any): string {
    return artifact.id || artifact.name || index.toString();
  }

  shouldShowStepper(): boolean {
    // Show stepper in project loading mode when we have metadata
    if (this.isProjectLoadingMode$.value) {
      const parsedData = this.projectLoadingData$.value;
      return parsedData?.metadata ? true : false;
    }

    if (this.isPolling) {
      return true;
    }

    const isRegenerationActive = this.isRegenerationInProgress$.value;
    const hasStepperMessage = this.lightMessages.some(msg => msg.hasSteps);

    if (isRegenerationActive && hasStepperMessage) {
      return true;
    }

    if (this.isCodeGenerationComplete && hasStepperMessage) {
      return true;
    }

    return false;
  }


  private handleBrowserNavigation(): void {
    
    this.navigationCleanupService.triggerManualCleanup({
      clearSessionStorage: true,
      clearLocalStorage: false,
      clearArtifacts: true,
      clearLogs: true,
      clearPreviewData: true,
      clearUIDesignData: true
    });
    this.navigateToHome();
  }
  private handlePageBeforeUnload(): void {
    
    this.navigationCleanupService.triggerManualCleanup({
      clearSessionStorage: true,
      clearLocalStorage: false,
      clearArtifacts: true,
      clearLogs: true,
      clearPreviewData: true,
      clearUIDesignData: true
    });
  }
  private handlePageHide(): void {
    
    this.navigationCleanupService.triggerManualCleanup({
      clearSessionStorage: true,
      clearLocalStorage: false,
      clearArtifacts: false,
      clearLogs: false,
      clearPreviewData: false,
      clearUIDesignData: false
    });
  }

  private handlePageVisibilityChange(): void {
    sessionStorage.removeItem('lastRoute');
  }
}
