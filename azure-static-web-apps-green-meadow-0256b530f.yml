name: Azure Static Web Apps CI/CD

trigger:
  branches:
    include:
      - main
  paths:
    include:
      - "projects/elder-wand/*"
      - "package.json"
      - "package-lock.json"
      - "angular.json"
      - "tsconfig.json"

variables:
  - group: elder-wand-variables # Variable group containing shared variables
  - name: npm_config_cache
    value: $(Pipeline.Workspace)/.npm

jobs:
  - job: build_elder_wand
    displayName: "Build Elder Wand"
    pool:
      vmImage: "ubuntu-latest"
    steps:
      - template: templates/node-build-steps.yml

      - script: |
          # Replace environment variables in the production environment file
          sed -i "s|\$(EXPERIENCE_STUDIO_URL)|$(EXPERIENCE_STUDIO_URL)|g" projects/elder-wand/src/environments/environment.prod.ts
          sed -i "s|\$(API_URL)|$(API_URL)|g" projects/elder-wand/src/environments/environment.prod.ts

          # Build with environment variables
          EXPERIENCE_STUDIO_URL="$(EXPERIENCE_STUDIO_URL)" npm run build:elder-wand
          echo "Checking build output directory:"
          ls -la dist
          echo "Checking elder-wand build directory:"
          ls -la dist/elder-wand
        displayName: "Build Elder Wand"
        env:
          NODE_ENV: production
          EXPERIENCE_STUDIO_URL: $(EXPERIENCE_STUDIO_URL)
          API_URL: $(API_URL)
          NODE_AUTH_TOKEN: $(Azure_DevOPS_PAT)

      - task: CopyFiles@2
        inputs:
          sourceFolder: 'dist/elder-wand'
          contents: '**/*'
          targetFolder: '$(Build.ArtifactStagingDirectory)/elder-wand'
        displayName: 'Copy Build Artifacts'

      - task: CopyFiles@2
        inputs:
          sourceFolder: 'projects/elder-wand'
          contents: 'staticwebapp.config.json'
          targetFolder: '$(Build.ArtifactStagingDirectory)/elder-wand'
        displayName: 'Copy Static Web App Config'

      - task: PublishBuildArtifacts@1
        inputs:
          pathToPublish: '$(Build.ArtifactStagingDirectory)/elder-wand'
          artifactName: 'elder-wand'
        displayName: 'Publish Build Artifacts'

  - job: deploy_elder_wand
    displayName: "Deploy Elder Wand"
    dependsOn: build_elder_wand
    condition: succeeded()
    pool:
      vmImage: "ubuntu-latest"
    steps:
      - checkout: self
        displayName: 'Checkout Repository'

      - download: current
        artifact: 'elder-wand'
        displayName: 'Download Build Artifacts'

      - script: |
          echo "Current directory:"
          pwd
          echo "Pipeline workspace:"
          echo $(Pipeline.Workspace)
          echo "Checking downloaded artifacts:"
          ls -la $(Pipeline.Workspace)/elder-wand
          echo "Checking working directory:"
          ls -la $(Pipeline.Workspace)/s
          echo "Checking root directory:"
          ls -la /
        displayName: 'Verify Artifacts'

      - script: |
          # Create deployment directory
          mkdir -p $(Pipeline.Workspace)/deploy
          # Copy artifacts to deployment directory
          cp -r $(Pipeline.Workspace)/elder-wand/* $(Pipeline.Workspace)/deploy/
          echo "Checking deployment directory:"
          $(Pipeline.Workspace)/deploy
          ls -la $(Pipeline.Workspace)/deploy
        displayName: 'Prepare Deployment Directory'

      - task: AzureStaticWebApp@0
        inputs:
          azure_static_web_apps_api_token: $(AZURE_STATIC_WEB_APPS_API_TOKEN_ELDER_WAND)
          app_location: "/"
          cwd: "$(Pipeline.Workspace)/deploy"
          output_location: ""
          skip_app_build: true
          api_location: ""
          deployment_source: "local"
        displayName: "Deploy Elder Wand to Azure Static Web Apps"